
<template>
  <div class="echoTable">
    <sb-el-table :table="table" @updateTableData="updateTableData" :on-ok="handleDoFun"></sb-el-table>
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";
export default {
  name: 'echoTable',
  props: {
    item: {
      type: Object,
      required: true
    },
    gps: {},
    appFormValue: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      table: {
        modulName: 'table-数据', // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: 'dialog',
        hasShowSummary: false, //是否合计
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: '90px',
          formItemList: [],
        },
        tr: [],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: '600px',
          labelWidth: '120px',
          inline: true,
          formItemList: [],
          labelPosition: 'right',
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: '100',
          fixed: 'right',
          data: [
            // {id: "handleTodo",name: "查看", fun:"handleTodo"},
            { id: 'delete', name: '删除', fun: 'handleDelete' },
          ],
        },
        hasPagination: false,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    }
  },
  created() { 
    this.gettableData();
  },
  mounted() { },
   watch: {
        appFormValue: {
            handler: function(newV, oldV) {
                if (newV) {
                    // console.log("form--appFormValue值更新啦！",JSON.stringify(newV));
                    if (this.appFormValue && this.item && this.item.key) {
                        this.table.data = newV[this.item.key] ? newV[this.item.key] : []
                    }
                }
            },
            deep: true,
            immediate: true,
        },
    },
  methods: {
    gettableData() {
      if (this.item.tableInfo.list.length > 0) {
        const options = this.item.tableInfo.list.map(element => {
          return {
            id: element.value,
            label: element.name,
            prop: element.value,
          };
        });
        this.table.tr = options
      }
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  }
}
</script>

<style scoped>
.echoTable {
  width: 100%;
}
::v-deep .table-container {
  padding: 0;
}
::v-deep .table-container .el-table {
  border: none !important;
}
</style>
