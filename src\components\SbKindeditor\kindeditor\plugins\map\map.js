/*******************************************************************************
 * KindEditor - WYSIWYG HTML Editor for Internet
 * Copyright (C) 2006-2011 kindsoft.net
 *
 * <AUTHOR> <<EMAIL>>
 * @site http://www.kindsoft.net/
 * @licence http://www.kindsoft.net/license.php
 *******************************************************************************/

// Google Maps: http://code.google.com/apis/maps/index.html

KindEditor.plugin("map", function(K) {
  var self = this,
    name = "map",
    lang = self.lang(name + ".");
  self.clickToolbar(name, function() {
    var html = [
      '<div style="padding:10px 20px;">',
      '<div class="ke-dialog-row">',
      lang.address +
        ' <input id="kindeditor_plugin_map_address" name="address" class="ke-input-text" value="" style="width:200px;" /> ',
      '<span class="ke-button-common ke-button-outer">',
      '<input type="button" name="searchBtn" class="ke-button-common ke-button" value="' +
        lang.search +
        '" />',
      "</span>",
      "</div>",
      '<div class="ke-map" style="width:558px;height:360px;"></div>',
      "</div>"
    ].join("");
    var dialog = self.createDialog({
      name: name,
      width: 600,
      title: self.lang(name),
      body: html,
      yesBtn: {
        name: self.lang("yes"),
        click: function(e) {
          var geocoder = win.geocoder,
            map = win.map,
            center = map.getCenter().lat() + "," + map.getCenter().lng(),
            zoom = map.getZoom(),
            maptype = map.getMapTypeId(),
            url = "http://maps.googleapis.com/maps/api/staticmap";
          url += "?center=" + encodeURIComponent(center);
          url += "&zoom=" + encodeURIComponent(zoom);
          url += "&size=558x360";
          url += "&maptype=" + encodeURIComponent(maptype);
          url += "&markers=" + encodeURIComponent(center);
          url += "&language=" + self.langType;
          url += "&sensor=false";
          self
            .exec("insertimage", url)
            .hideDialog()
            .focus();
        }
      },
      beforeRemove: function() {
        searchBtn.remove();
        if (doc) {
          doc.write("");
        }
        iframe.remove();
      }
    });
    var div = dialog.div,
      addressBox = K('[name="address"]', div),
      searchBtn = K('[name="searchBtn"]', div),
      win,
      doc;
    var iframeHtml = [
      "<!doctype html><html><head>",
      '<meta name="viewport" content="initial-scale=1.0, user-scalable=no" />',
      "<style>",
      "	html { height: 100% }",
      "	body { height: 100%; margin: 0; padding: 0; background-color: #FFF }",
      "	#map_canvas { height: 100% }",
      "</style>",
      '<script src="http://maps.googleapis.com/maps/api/js?sensor=false&language=' +
        self.langType +
        '"></script>',
      "<script>",
      "var map, geocoder;",
      "function initialize() {",
      "	var latlng = new google.maps.LatLng(31.230393, 121.473704);",
      "	var options = {",
      "		zoom: 11,",
      "		center: latlng,",
      "		disableDefaultUI: true,",
      "		panControl: true,",
      "		zoomControl: true,",
      "		mapTypeControl: true,",
      "		scaleControl: true,",
      "		streetViewControl: false,",
      "		overviewMapControl: true,",
      "		mapTypeId: google.maps.MapTypeId.ROADMAP",
      "	};",
      '	map = new google.maps.Map(document.getElementById("map_canvas"), options);',
      "	geocoder = new google.maps.Geocoder();",
      "	geocoder.geocode({latLng: latlng}, function(results, status) {",
      "		if (status == google.maps.GeocoderStatus.OK) {",
      "			if (results[3]) {",
      '				parent.document.getElementById("kindeditor_plugin_map_address").value = results[3].formatted_address;',
      "			}",
      "		}",
      "	});",
      "}",
      "function search(address) {",
      "	if (!map) return;",
      "	geocoder.geocode({address : address}, function(results, status) {",
      "		if (status == google.maps.GeocoderStatus.OK) {",
      "			map.setZoom(11);",
      "			map.setCenter(results[0].geometry.location);",
      "			var marker = new google.maps.Marker({",
      "				map: map,",
      "				position: results[0].geometry.location",
      "			});",
      "		} else {",
      '			alert("Invalid address: " + address);',
      "		}",
      "	});",
      "}",
      "</script>",
      "</head>",
      '<body onload="initialize();">',
      '<div id="map_canvas" style="width:100%; height:100%"></div>',
      "</body></html>"
    ].join("\n");
    // TODO：用doc.write(iframeHtml)方式加载时，在IE6上第一次加载报错，暂时使用src方式
    var iframe = K(
      '<iframe class="ke-textarea" frameborder="0" src="' +
        self.pluginsPath +
        'map/map.html" style="width:558px;height:360px;"></iframe>'
    );
    function ready() {
      win = iframe[0].contentWindow;
      doc = K.iframeDoc(iframe);
      //doc.open();
      //doc.write(iframeHtml);
      //doc.close();
    }
    iframe.bind("load", function() {
      iframe.unbind("load");
      if (K.IE) {
        ready();
      } else {
        setTimeout(ready, 0);
      }
    });
    K(".ke-map", div).replaceWith(iframe);
    // search map
    searchBtn.click(function() {
      win.search(addressBox.val());
    });
  });
});
