<template>
  <el-upload
    ref="upload"
    v-bind="$attrs"
    v-on="$listeners"
    :action="util.getApiUrl() + (upload.url || '/sys/file/uploadProcessFiles')"
    :on-preview="handlePreview"
    :on-remove="handleRemove"
    :with-credentials="true"
    :data="upload.data || {}"
    :on-success="handleSuccess"
    :on-error="handleError"
    :show-file-list="upload.showFileList === false ? false : true"
    :on-progress="handleProgress"
    :before-remove="beforeRemove"
    :file-list="upload.filelist || []"
    :before-upload="beforeUpload"
    :multiple="
      upload.multiple === undefined
        ? true
        : upload.multiple || upload.limit > 1 || false
    "
    :limit="upload.limit || (upload.multiple ? 20 : 1)"
    :on-exceed="handleExceed"
    :http-request="upload.httpRequest || uploadfiles"
    :disabled="upload.disable || false"
    :name="upload.name || 'file'"
    :list-type="upload.listType || 'text'"
    :accept="
      upload.listType === 'picture-card' || upload.listType === 'picture'
        ? 'image/*'
        : upload.accept || ''
    "
  >
    <el-button :size="upload.size || 'small'" type="primary">{{
      upload.btnText || "文件上传"
    }}</el-button>
    <div
      v-if="
        (upload.accept ||
          upload.multiple ||
          upload.limit ||
          upload.showFileList) &&
          upload.showInfo
      "
      slot="tip"
      :class="'el-upload__tip' + (upload.showFileList === false ? ' fl' : '')"
    >
      <span v-if="upload.accept">只能上传{{ upload.accept }}的文件</span>
      <span v-if="upload.multiple && upload.limit && upload.limit > 1"
        >可上传多张,最多上传{{ upload.limit }}个</span
      >
      <span v-if="upload.limit && upload.limit === 1">只能上传一个附件</span>
    </div>
  </el-upload>
</template>
<script>
import { uploadProcessFiles } from "@/api/public";
export default {
  name: "SbElUpload",
  props: {
    upload: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    },
    from: {
      type: Boolean
    }
  },
  data() {
    return {
      uploadH: { currentCorp: this.$store.state.user.user.currentCorp }
    };
  },
  mounted() {
    //Loading.service({text:"文件正在上传，请稍候！"});
    //console.log(JSON.stringify(this.upload));
  },
  methods: {
    handleProgress(event, file, fileList) {
      // console.log(event);
    },
    handleError(err, file, fileList) {
      // console.log("aaaaaaaaaaaa",err);
      if (err) {
        this.$message({
          message: err || "上传失败",
          type: "error",
          duration: 3000
        });
      }
    },
    handleSuccess(response, file, fileList) {
      let res;
      if (typeof response === "string") {
        res = eval(
          response
            .replace(new RegExp("parent", "g"), "window")
            .split('<script type="text/javascript">')[1]
            .split("<\/script>")[0]
        );
      } else {
        res = response;
      }
      if (res.errcode === 0 || res.errcode === 200) {
        if (res.message) {
          this.$message({
            message: res.message,
            type: "success",
            duration: 3000
          });
        }
        file.response =
          res.data.sysFiles.length > 0
            ? res.data.sysFiles
            : res.data.listData || res.data.mapData;
        var files = [];
        for (let i in fileList) {
          if (fileList[i].response && fileList[i].response.length > 0)
            files.push(fileList[i].response[0]);
        }
        for (let i in files) {
          if (!files[i].name) files[i].name = files[i].fileName;
          if (!files[i].url)
            files[i].url = this.util.getFile() + files[i].downLoadUrl;
        }
        this.$emit("uploadData", files);
        //if(this.upload.afterUpload) this.$emit(this.upload.afterUpload,{list:files,fileData:file.response});
      } else {
        this.$message({
          message: res.message || "上传失败",
          type: "error",
          duration: 3000
        });
        if (res.errcode === 401 || res.errcode === 403) {
          //this.$confirm('你已被登出,可以取消继续留在该页面,或者重新登录','确认登出',{
          //    confirmButtonText:'重新登录',
          //    cancelButtonText:'取消',
          //    type:'warning'
          //}).then(() => {
          store.dispatch("FedLogOut").then(() => {
            location.reload();
          });
          //})
        }
      }
    },
    handlePreview() {
      //选择已上传的图片要干什么
    },
    handleRemove(file, fileList) {
      //移除后要干什么
      if (this.upload.multiple || this.upload.limit > 1) {
        let index = fileList.findIndex(file => file === node);
        if (index > -1) fileList.splice(index, 1);
        this.$emit("uploadData", fileList);
      } else {
        this.$emit("uploadData", []);
      }
      if (this.onOk && this.upload.removeFun) {
        this.onOk(this.upload, "removeFun");
      }
    },
    beforeUpload(file) {
      if (this.onOk && this.upload.beforeFun) {
        let n = this.onOk(this.upload, "beforeFun");
        return n;
      }
      if (this.upload.filesize) {
        if (file.size > this.upload.filesize) {
          this.$message({
            type: "error",
            message:
              "上传文件大小不能超过" + (this.upload.filesize || 10240) + "kb！"
          });
          //清空上传文件框
          e.target.vlaue = "";
          return;
        }
      }
      return true;
    },
    beforeRemove(file, fileList) {
      //移除前要干什么
      // console.log(fileList);
    },
    handleExceed(files, fileList) {
      if (
        fileList.length > 20 ||
        (this.upload.limit && fileList.length > this.upload.limit)
      ) {
        this.$message({
          message: "最多允许上传个" + (this.upload.limit || "20") + "文件"
        });
        this.$refs.upload.clearFiles();
        return false;
      }
    },
    uploadfiles(content) {
      let self = this;
      if (this.beforeUpload(content.file)) {
        let formData = new FormData();
        if (this.upload.data) {
          for (let i in this.upload.data) {
            formData.append(i, this.upload.data[i]);
          }
        }
        if (
          this.upload.listType === "picture" ||
          this.upload.listType === "picture-card"
        ) {
          //是否要压缩图片
          if (/\/(?:jpeg|png)/i.test(content.file.type)) {
            //如果是图片 就压缩
            let fr = new FileReader();
            fr.onload = function(e) {
              let IMG = new Image();
              IMG.src = e.target.result; //读出来的文件流
              IMG.onload = function() {
                let w = this.naturalWidth,
                  h = this.naturalHeight,
                  resizeW = this.naturalWidth,
                  resizeH = this.naturalHeight;
                // maxSize 是压缩的设置，设置图片的最大宽度和最大高度，等比缩放，level是报错的质量，数值越小质量越低
                let maxSize = {
                  width: self.upload.maxwidth || 1000,
                  height: self.upload.maxheight || 1000,
                  level: self.upload.maxlevel || 0.8
                };
                if (w > maxSize.width || h > maxSize.height) {
                  //设置压缩比例
                  if (h / w > maxSize.height / maxSize.width) {
                    resizeH = maxSize.height;
                    resizeW = (maxSize.height / h) * w;
                  } else {
                    resizeW = maxSize.width;
                    resizeH = (maxSize.width / w) * h;
                  }
                }

                //将文件流画到canvas画布上
                let canvas = document.createElement("canvas"),
                  ctx = canvas.getContext("2d");
                //if (window.navigator.userAgent.indexOf('iPhone') > 0) {
                //    canvas.width = resizeH;
                //    canvas.height = resizeW;
                //    ctx.rorate(90 * Math.PI / 180);//苹果手机需要翻转
                //    ctx.drawImage(IMG, 0, -resizeH, resizeW, resizeH);
                //} else {
                canvas.width = resizeW;
                canvas.height = resizeH;
                ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                //}
                //再将画布上元素导出到Base64
                let base64img = canvas.toDataURL("image/jpeg", maxSize.level);
                let base64 = window.atob(base64img.split(",")[1]);
                let buffer = new ArrayBuffer(base64.length);
                let ubuffer = new Uint8Array(buffer);
                for (let i = 0; i < base64.length; i++) {
                  ubuffer[i] = base64.charCodeAt(i);
                }
                //再把Base64转成blob文件（图片）
                let blob;
                try {
                  blob = new Blob([buffer], { type: "image/jpg" });
                } catch (e) {
                  window.BlobBuilder =
                    window.BlobBuilder ||
                    window.WebKitBlobBuilder ||
                    window.MozBlobBuilder ||
                    window.MSBlobBuilder;
                  if (e.name === "TypeError" && window.BlobBuilder) {
                    let blobBuilder = new BlobBuilder();
                    blobBuilder.append(buffer);
                    blob = blobBuilder.getBlob("image/jpg");
                  }
                }
                formData.append(
                  self.upload.name || "file",
                  blob,
                  content.file.name
                );
                self.doUpload(formData, content);
              }; //end imgload
            }; //end frload
            fr.readAsDataURL(content.file);
          }
        } else {
          formData.append("file", content.file);
          self.doUpload(formData, content);
        }
      }
    },
    doUpload(formData, content) {
      if (this.upload.fun) {
        this.$emit(this.from ? "handleHttpRequest" : "uploadHttpRequest", {
          fun: this.upload.fun,
          formData,
          content
        });
      } else {
        uploadProcessFiles(formData)
          .then(res => {
            content.onSuccess(res);
            //this.filelists.push(res.data);
            //this.$emit('uploadData', this.filelists);
            //if(this.upload.afterUpload) this.$emit(this.upload.afterUpload,{list:this.filelists,fileData:res.data});
          })
          .catch(error => {
            //console.log(JSON.stringify(this.$refs.upload._props));
            //this.$refs.upload.clearFiles();
            content.onError("文件上传失败");
          });
      }
    }
  }
};
</script>
<style scoped>
.el-button--small {
  font-size: 13px;
  padding: 9px 16px 8px;
}
</style>
