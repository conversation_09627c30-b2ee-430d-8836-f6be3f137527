<template>
  <div class="content">
      <!-- 业务表单 -->
  <div class="tableForm">
    <sb-el-form ref="appForm" 
      :form="appForm" 
      v-model="appFormValue" 
      :disabled="appForm.formDisabled" 
      @chooseFun="chooseFun"
      @uploadFileList="uploadFileList"
      :on-ok="handleDoFun">
    </sb-el-form>
    <div style="padding:20px 0;display: flex;justify-content: flex-end;">
        <el-button  @click="handleUp('no')" type="primary" size="small">关闭</el-button>
        <el-button type="primary" @click="handleUp('yes')" size="small" :disabled="isComfilm">确认</el-button>
    </div>
  </div>
  </div>
</template>

<script>
let defaultAppFormValue = {
    pmInsId: "",
    id: "",
    blank: "blank"
};
import { uploadProcessFiles } from "@/api/public";
export default {
  name:'addInfo',
  props: {
  gps: {
    type: Object,
    default(){ return this.$route.query; }
  },
  types: { type: String, },
  rowData: { type: Object, },
},
  computed:{ },
  data(){
      return {
          // 业务表单
          initValue: {},
          appFormValue: Object.assign({},defaultAppFormValue),
          appForm:{
            formDisabled: false,
            labelWidth: "200px",
            inline: true,
            formItemList: [
                {class: "c12",label: "分组名称",key: "groupingName",type: "input",placeholder: "",rule: {required: true}},
                {class: "c12",label: "附件" ,key: "files", type: "sbUpload", btnText: "+", fun: "uploadFileList", listType: "text", multiple: false,rule: {required: true}},
            ],
          },
          isComfilm: false,
      }
  },
  created() {
    this.fetchRowData();
  },
  methods:{
      handleUp(op) {
          if (op == "yes") {
              let formName = 'appForm'
              this.$refs[formName].$children[0].validate((valid) => {
                  if (valid) {
                      this.isComfilm = true;
                      this.$emit('event', this.appFormValue);
                      this.$emit("closeshowDialog");
                  } else {
                      console.log('error submit!!');
                      return false;
                  }
              });
          } else {
              this.$emit("closeshowDialog");
          }
      },

      handleDoFun(obj, fun, data) {//若一个beforeFun可直接在这个函数里面写
        let n = this[obj[fun]].call(this, obj, data);
        return n;
      },
      fetchRowData(){
          if(this.types=='edit'){
              this.appFormValue = this.rowData
          }
      },
      chooseFun(obj, data){
        let myorgDisplayName = data[0].orgDisplayName.split('\\')

        this.appFormValue.companyName = myorgDisplayName[0]
        this.appFormValue.depName = myorgDisplayName[1]
      },
      uploadFileList(obj) {
          uploadProcessFiles(obj.formData).then(res => {
              obj.content.onSuccess(res, obj.content.file, []);
              }).catch(error => {
                  obj.content.onError();
              });
      },
  }

}
</script>

<style scoped>
::v-deep .el-form-item .el-form-item__label[for="applyUser"] {
line-height: 15px !important;
}

.tableForm{
  border-left: none;
}

::v-deep .tableForm .el-input__inner, .tableForm .el-textarea__inner{
border-right: 1px solid #e8e8e8;
border-bottom: 1px solid #e8e8e8;
}
::v-deep .tableForm .upload_D{
border-right: 1px solid #e8e8e8;
border-bottom: 1px solid #e8e8e8;
}

</style>