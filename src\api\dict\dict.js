import request from "@/assets/js/request";
import store from "../../store";

// 查询字典列表
export function findAllDict(params) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dict/findAll?properties=createdTime&direction=asc&page=" + params.page + "&size=" + params.size,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}

// 根据id查字典类型信息
export function findDictById(id) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dict/findById?id=" + id,
        contentType: "application/json;charset=UTF-8"
    });
}

// 新增字典类型
export function createDict(params) {
    //if(!params.isPublic){
    params.blocid = params.isPublic ? "" : store.getters.user.currentBloc;
    params.corpid = params.isPublic ? "" : store.getters.user.currentCorp;
    //}
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dict/create",
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}

// 修改字典类型
export function updateDict(params) {
    //if(!params.isPublic){
    params.blocid = params.isPublic ? "" : store.getters.user.currentBloc;
    params.corpid = params.isPublic ? "" : store.getters.user.currentCorp;
    //}
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dict/update",
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}

// 根据id删除一个数据字典
export function deleteDictById(id) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dict/deleteById?id=" + id,
        contentType: "application/json;charset=UTF-8"
    });
}

// 批量删除
export function deleteDictByIds(ids) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dict/deleteAllByIds",
        contentType: "application/json;charset=UTF-8",
        data: ids
    });
}

// 修改启用/禁用
export function updateDictEnable(id, enabled) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dict/updateEnable?id=" + id + "&enabled=" + enabled,
        contentType: "application/json;charset=UTF-8"
    });
}