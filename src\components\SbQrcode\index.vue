<template>
  <div v-bind="$attrs" v-on="$listeners" ref="qrcode" class="qrcode"></div>
</template>
<script>
import QRCode from "qrcodejs2";
export default {
  name: "SbQrcode",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      qrcodeL: 1,
      text: this.item.text || ""
    };
  },
  mounted() {
    this.qrcode();
  },
  methods: {
    qrcode() {
      if (this.$refs.qrcode) {
        this.$refs.qrcode.innerHTML = "";
        let qrcode = new QRCode(this.$refs.qrcode, {
          width: this.item.size || 100,
          height: this.item.size || 100, // 高度  [图片上传失败...(image-9ad77b-1525851843730)]
          text: this.text // 二维码内容
          //render:'canvas'
          // render: 'canvas' // 设置渲染方式（有两种方式 table和canvas，默认是canvas）
          // background: '#f0f'
          // foreground: '#ff0'
        });
      }
    }
  },
  watch: {
    "item.text": {
      handler: function(newV, oldV) {
        if (newV) {
          //console.log("值更新啦！",newV);
          this.text = newV;
          this.qrcode();
        }
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
<style scoped>
.qrcode {
  background: #fff;
  padding: 5px;
}
</style>
