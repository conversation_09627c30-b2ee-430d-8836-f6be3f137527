<template>
  <div class="w100 inputBtn">
    <el-input ref="elInput" :type="item.inputType || 'text'" v-bind="$attrs" v-on="$listeners" :size="item.size || 'small'" :placeholder="item.placeholder || item.label || '请输入'" :disabled="item.disabled || false" :readonly="item.readonly || false" :autosize="item.autosize || false">
      <el-button slot="append" :size="item.size || 'small'" type="primary" :disabled="item.disabled || false" @click="openDialog">
        {{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
      </el-button>
    </el-input>

    <el-dialog title="选择数据" :visible.sync="dialogVisible" v-dialogDrag append-to-body destroy-on-close width="60%">
      <sb-el-table ref="interiorTable" :table="table" @getList="getList" @updateTableData="updateTableData" :on-ok="handleDoFun"></sb-el-table>
      <div class="el-dialog__footer" style="padding: 66px 0px 20px;">
        <span class="dialog-footer">
          <el-button type="primary" @click="saveFilter" size="mini" style="background: #0F85CF;
        border: solid 1px #0F85CF;">确认</el-button>
          <el-button @click="cancelFilter" size="mini">关闭</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { interiorDialog } from "@/api/senior";
import request from "@/assets/js/request";
import util from '@/assets/js/public';
import store from "@/store";
export default {
  name: "interiorDialog",
  props: {
    item: { 
      type: Object,
      required: true 
    },
    appFormValue: {
      type: Object,
    },
    dialogData: {
      type: Object,
    }
  },
  data() {
    return {
      dialogVisible: false,
      table: {
        modulName: "interiorDialog-选择数据", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: true, // 是否有复选框
        mulitple:
          (!this.item.mulitple && this.item.mulitple !== false) ||
          this.item.mulitple === true
            ? true
            : false, //单选多选
        showIndex: false, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
          ],
        },
        tr: [],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      urls:(appFormValue, params) => {
              // appFormValue为表单值, params为分页参数
              return request(
                {
                  url: `/${process.env.VUE_APP_APPCODE}/action/common/myDraftToDo?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
                  contentType: "application/json;charset=UTF-8",
                  data: params
                });
        }
    };
  },
  created() {
    this.getTableInfo()
  },
  methods: {
    openDialog(data) {
      let inputData = this.$refs.elInput._props;
      let formData = this.$refs.elInput.elForm._props.model;
      //console.log(JSON.stringify(formData));
      //console.log('formData');
      let relevancy = this.item.relevancy.split(",");
      let di = formData[relevancy[0].split("-")[0]]
        ? formData[relevancy[0].split("-")[0]].split(",").length
        : 0;
      if (di === 0) {
        this.table.multipleSelection = [];
        if (this.$refs.interiorTable)
          this.$refs.interiorTable.handleClearSelection(); 
      }
      for (let i = 0; i < di; i++) {
        let datai = {};
        for (let j in relevancy) {
          var reF = relevancy[j].split("-");
          if (reF.length === 1) reF.push(reF[0]);
          datai[reF[1]] = formData[reF[0]]
            ? formData[reF[0]].split(",")[i]
            : "";
        }
        var ml = 0;
        for (let k in this.table.multipleSelection) {
          for (let j in relevancy) {
            var reF = relevancy[j].split("-");
            if (reF.length === 1) reF.push(reF[0]);
            if (datai[reF[1]] === this.table.multipleSelection[k][reF[1]]) ml++;
          }
        }
        if (ml !== relevancy.length) this.table.multipleSelection.push(datai);
      }
      this.dialogVisible = true
      this.getList();
    },
    getTableInfo() {
      if(this.item.config && this.item.config.beforeRequest){
        this.urls = eval(this.item.config.beforeRequest)
      }
      
      // 
    //   console.log(this.dialogData)
      if (this.dialogData.options.length > 0) {
        const options = this.dialogData.options.map(element => {
          return {
            id: element.value,
            label: element.name,
            prop: element.value,
          };
        });
        this.table.tr = options
      }
      if (this.dialogData.paramsArr.length > 0) {
        const formItemList = this.dialogData.paramsArr.map(element => {
          return {
            type: "input",
            label: element.name,
            prop: element.value,
          };
        });
        this.table.queryForm.formItemList = formItemList
      }
      
    },
    
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      this.urls(this.appFormValue,listQuery || this.table.listQuery).then((res) => {
        this.table.loading = false;
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },

    saveFilter() {
        // console.log(this.table.multipleSelection)
        this.dialogVisible = false;
        this.$emit("chooseData", this.table.multipleSelection);
        if(this.item && this.item.alKey){
            this.$emit("chooseData2", this.item.alKey, this.table.multipleSelection);
        }
    },
    cancelFilter() {
      this.dialogVisible = false
    },


    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>