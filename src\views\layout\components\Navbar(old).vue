<template>
    <el-menu class="navbar" mode="horizontal" ref="navbarRef">
        <hamburger class="hamburger-container" :toggleClick="toggleSideBar" :isActive="sidebar.opened"></hamburger>
        <!-- <breadcrumb></breadcrumb>    -->        
        <div class="menuL1" :style="{width:menuL1li+'px'}">
            <a v-if="lrIcon" class="menuL1IconL" @click="handleScroll(true)"><svg-icon iconClass='iconzuozuo'></svg-icon></a>
            <scroll-bar-transform :type="'left'" :width='menuS' ref="menuL1SB">
                <div :style="{width:menuL+'px'}">
                <el-menu ref="topmenu" mode="horizontal" class="" :default-active="$route.path" :unique-opened="true">
                    <sidebar-item :menus="menus"></sidebar-item>
                </el-menu>
                </div>
            </scroll-bar-transform>
            <a v-if="lrIcon" class="menuL1IconR" @click="handleScroll(false)"><svg-icon iconClass='iconyouyou'></svg-icon></a>
        </div>
        <div class="avatar-container" ref="avatarRef">      
            <span class="fl f14">{{calendar}}</span>  
            <el-dropdown class="fr" trigger="click">   
                <div class="avatar-wrapper">
                    <img class="user-avatar" :src="user.avatar"/>
                    <span class="mr10">{{user.truename}}</span>
                    <svg-icon icon-class="iconxia"></svg-icon>
                </div>
                <el-dropdown-menu class="user-dropdown" slot="dropdown">
                    <router-link class="inlineBlock" to="/">
                        <el-dropdown-item>首页</el-dropdown-item>
                    </router-link>
                    <el-dropdown-item divided>
                        <span @click="changePwd" style="display:block;">修改密码</span>
                    </el-dropdown-item>
                    <el-dropdown-item divided>
                        <span @click="logout" style="display:block;">退出</span>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>         
        </div>
        <el-dialog v-dialogDrag title="修改密码" :close-on-click-modal="false" :visible.sync="pwdDialog" append-to-body width="500px">
            <el-form :model="ruleForm" status-icon :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="原密码" prop="oldpass">
                    <el-input size="medium" type="password" v-model="ruleForm.oldpass" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="新密码" prop="pass">
                    <el-input size="medium" type="password" v-model="ruleForm.pass" autocomplete="off"></el-input>
                </el-form-item>
                <el-form-item label="确认密码" prop="checkPass">
                    <el-input size="medium" type="password" v-model="ruleForm.checkPass" autocomplete="off"></el-input>
                </el-form-item>
            </el-form>          
            <span slot="footer" class="dialog-footer">
                <el-button @click="pwdDialog=false" size="small">取消</el-button>
                <el-button type="primary" @click="handleChangePwd('ruleForm')" size="small">确定</el-button>
            </span>
        </el-dialog>
    </el-menu>
</template>
<script>
import Cookies from 'js-cookie';
import { mapGetters } from 'vuex';
import Breadcrumb from '@/components/Breadcrumb';
import Hamburger from '@/components/Hamburger';
import SidebarItem from "./Sidebar/SidebarItem";
import scrollBarTransform from '@/components/ScrollBarTransform';
import util from '@/assets/js/public';
// import { updatePwd } from "@/api/home";

export default {
    components:{
        Breadcrumb,
        Hamburger,
        scrollBarTransform,
        SidebarItem
    },
    data(){
        var validateoldPass = (rule, value, callback) => {
            if (value === '') {
            callback(new Error('请输入旧密码'));
            } else {
                // if (this.ruleForm.oldpass !== '') {
                    
                // }
                callback();
            }
        };
        var validatePass = (rule, value, callback) => {
            if (value === '') {
            callback(new Error('请输入新密码'));
            } else {
                if (this.ruleForm.checkPass !== '') {
                    this.$refs.ruleForm.validateField('checkPass');
                }
                callback();
            }
        };
        var validatePass2 = (rule, value, callback) => {
            if (value === '') {
            callback(new Error('请再次输入密码'));
            } else if (value !== this.ruleForm.pass) {
                callback(new Error('两次输入密码不一致!'));
            } else {
            callback();
            }
        };
        return {
            lrIcon:false,
            menuL1li:0,
            menuS:0,
            menuL:0,
            ruleForm: {
                oldpass: '',
                pass: '',
                checkPass: ''
            },
            rules: {
                oldpass: [
                    { validator: validateoldPass, trigger: 'blur' }
                ],
                pass: [
                    { validator: validatePass, trigger: 'blur' }
                ],
                checkPass: [
                    { validator: validatePass2, trigger: 'blur' }
                ]
            },
            calendar:"",
            chooseDialog:false,
            pwdDialog: false,
            authBlocs:this.$store.getters.user.authBlocs,
            authCorps:this.$store.getters.user.authCorps,
            corpsList:[],
            chooseCorps:false,
            hasChoose:false,
            currentZone:this.$store.getters.user.zoneName,
            currentSchool:this.$store.getters.user.schoolName,
            zone:null,
            school:null
        }
    },
    computed:{
        ...mapGetters([
            'sidebar',
            'user',
            'menus'
        ]),          
		isCollapse() {
            // !this.sidebar.opened
            return true;
        }
    },
    created(){
        this.calendar=this.util.getNow("yyyy年MM月dd日 星期weekday",true);     
    },
    mounted(){        
        this.SBWidth(); 
        window.onresize = () => {
            return (() => {
                this.SBWidth();
            })()
        }       
    },
    methods:{
        SBWidth(){
            let navzc=this.$refs.navbarRef.$el.offsetWidth;//bgn56
            let avatarRef=this.$refs.avatarRef.offsetWidth;
            this.menuL1li=navzc-avatarRef-56-10;
            this.menuS=this.menuL1li-24*2;
            let zc=0;
            for(let i in this.menus){
                zc+=150;
            }           
            this.menuL=zc;
            this.lrIcon=this.menuS<this.menuL;
        },
        handleScroll(type){
            let sb=this.$refs.menuL1SB;
            let sc=sb.$refs.scrollContainer.clientWidth;
            if(sb.left<0 && type){
                if(sb.left+sc>0)
                    sb.left=0;
                else
                    sb.left+=sc;
            }
            let rc=sc-this.menuL;
            if(sb.left>rc && (!type)){
                if(sb.left-sc<rc)
                    sb.left=rc;
                else
                    sb.left-=sc;
            }
        },
        toggleSideBar(){
            this.$store.dispatch('ToggleSideBar');
        },
        logout(){
            this.$store.dispatch('LogOut').then(() => {
                this.$store.dispatch('FedLogOut').then(() => {                    
                    Cookies.remove('currentZone');
                    this.$router.push({path:'/login'});
                });
            })
        },
        changePwd() {
            this.pwdDialog = true;
            if (this.$refs['ruleForm']) {
                this.$refs['ruleForm'].resetFields();
            }
        },
        // 修改密码
        handleChangePwd(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // updatePwd({employeeId:this.$store.getters.user.id,oldPwd:util.encrypt(this.ruleForm.oldpass),newPwd:util.encrypt(this.ruleForm.pass)}).then(res => {
                    //     this.pwdDialog=false;
                    //     setTimeout(this.logout, 2000)
                    // });
                    
                } else {
                    return false;
                }
            });
        }
    }
}
</script>
<style scoped>
.menuL1{height:64px;overflow:hidden;position:relative;z-index:8;float:left;width:auto;padding:0 24px;}
.menuL1li li{width:auto;float:left;line-height:64px;font-size:14px;color:#fff;padding:0 10px;}
.menuL1li li.li_active,.menuL1li li:hover{background:#688EFF;cursor:pointer;}
.menuL1li li .icon{margin:0 10px 0 0;color:#fff;}
.menuL1li li .xia{font-size:8px;margin:0 0 0 10px;}
.menuL1IconL,.menuL1IconR{position:absolute;line-height:64px;color:#fff;top:0;padding:0;background:transparent;}
.menuL1IconL:hover,.menuL1IconR:hover{background:#688EFF;}
.menuL1IconL{left:0;}
.menuL1IconR{right:0;}
.el-menu{background-color:transparent;height:64px;}
.el-menu.el-menu--horizontal{border:0 none;}
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-menu-item{color:#515151;}
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-submenu__title,
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-menu-item{background-color:transparent;height:64px;line-height:64px;color:#fff;padding:0 35px 0 15px;font-size:15px;font-weight:bold;}
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-menu-item{padding:0 15px;}
.menuL1 ::v-deep  .icon{vertical-align:-0.45em !important;margin-right:6px !important;}
.menuL1 ::v-deep  .menuL1IconL .icon,.menuL1 ::v-deep  .menuL1IconR .icon{margin:0 !important;}
.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-submenu__title i{color:#fff;}
.menuL1 ::v-deep  .el-submenu__icon-arrow{right:15px;margin-top:-4px;}
.menuL1 ::v-deep  .el-submenu,.menuL1 ::v-deep  .el-menu--horizontal.el-menu .el-menu-item{width:auto;float:left;line-height:64px;}
.menuL1 .menu-wrapper ::v-deep  .el-submenu.is-active .el-submenu__title{border-left:0px solid #507AF6!important;background:#688EFF!important;color:#fff!important;}
.chooseZoneT{padding-left:15px;}
.chooseZoneT .icon{margin-left:4px;font-size:10px;margin-right:6px;}
.choose_tit{font-size:14px;font-weight:bold;border-left:3px solid #409eff;line-height:32px;border-bottom:1px solid #ddd;color:#333;padding:0 0 0 8px;}
.choose_nr{padding:0px 0px 15px;}
.choose_a{border:1px solid #e9e9eb;border-radius:4px;margin:15px 20px 0 0;padding:6px 18px;position:relative;display:inline-block;color:#909399;background:#f4f4f5;}
.choose_a .icon{position:absolute;bottom:0;right:0;font-size:10px;color:#409eff;margin-right:0;}
.choose_a_hover{border-color:#d9ecff;color:#409eff;background:#ecf5ff;}
.choose_a_hover .icon{color:#409eff;}
.navbar{    
  position: fixed;
  top:0;
  right:0;
  left:200px;
  height: 64px;
  line-height: 64px;
  border-radius: 0px !important;
  background:#507AF6;
  color:#fff;
  z-index:8;
}
.navbar .hamburger-container {
    line-height: 70px;
    height: 64px;
    float: left;
    padding-left: 10px;
}
.navbar .screenfull {
    position: absolute;
    right: 90px;
    top: 16px;
    color: red;
}
.navbar .avatar-container {
    height: 64px;
    display: inline-block;
    position: relative;
    right: 18px;
    float:right;
}
.navbar .avatar-container .avatar-wrapper {
    cursor: pointer;
    position: relative;
}
.navbar .avatar-container .avatar-wrapper .user-avatar {
  width: 36px;
  height: 36px;
  margin-right: 5px;
  margin-left:10px;
  background:#fff;
  border-radius: 50%;
}
.navbar .avatar-container .avatar-wrapper .el-icon-caret-bottom {
  position: absolute;
  right: -20px;
  top: 25px;
  font-size: 12px;
}
.avatar-wrapper .icon{font-size:10px;}
.el-dropdown{color:#fff;}
</style>
