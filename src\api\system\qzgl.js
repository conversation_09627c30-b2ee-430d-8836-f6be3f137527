import request from "@/assets/js/request";
import util from "@/assets/js/public";
import store from "@/store";

// 获取List表相关数据
export function getAll(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/group/getAll?page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 获取左侧树数据
export function findGroupsType() {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/group/findGroupsType`,
        contentType: "application/json; charset=utf-8"
    });
}

export function addgroup(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/group/addGroup?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

export function handleQzDelete(id,sid) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/group/deleteById?id=${id}&sid=${sid}`,
        contentType: "application/json; charset=utf-8",
    });
}

export function findPositionAndUser(params) {
    let url = util.toUrl(`/${process.env.VUE_APP_APPCODE}/uums/sys/position/findPositionAndUser`, params || {});
    return request({
        url: url,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

//群组添加人员
export function createGroupUsers(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/user/group/createGroupUsers?groupSid=${params.groupSid}&usernames=${params.usernames}&userBussinessConfig=defaultBussiness`,
        contentType: "application/json; charset=utf-8",
    });
}

//查询群组下用户
export function findUserByGroupSid(params) {
    let url = util.toUrl(`/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/findUserByGroup`, params || {});
    return request({
        url: `${url}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
//群组删除人员
export function deleteByUserIdFromGroup(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/user/group/deleteById?id=${id}`,
        contentType: "application/json; charset=utf-8"
    });
}
//查询所有应用
export function findAllApps(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/app/findAllNoPage?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}