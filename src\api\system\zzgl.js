import request from "@/assets/js/request";
import store from "../../store";
import util from "@/assets/js/public";
// 获取Lie表相关数据
export function getOrgList(params) {
    let url = util.toUrl(`/${process.env.VUE_APP_APPCODE}/uums/sys/org/findOrgCodeOrgNameDim`, params || {});
    return request({
        url: url,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 获取Lie表相关数据
export function findRootAndNextRoot(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findRootAndNextRoot?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        // data: params
    });
}
// 获取Lie表相关数据
export function findSonByParentOrgId(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findSonByParentOrgId?appcode=${process.env.VUE_APP_APPCODE}&orgCode=${params.orgCode}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function addOrg(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/create?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function updateOrgCustom(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/updateOrgCustom?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function deleteOrgCustom(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/deleteOrgCustom?id=${id}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
    });
}
export function findByIdOrg(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findById?id=${id}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 数据字典
export function findDictValue(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/dict/findDictValue`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 模板下载
export function downloadFile() {
    return request({
        url: '/' + process.env.VUE_APP_UUMSAPPCODE + '/template/downloadExcelTemplate/sso?type=org&loginuser=' + util.encrypt(store.getters.user.username) + '&appcode=' + process.env.VUE_APP_APPCODE,
        contentType: 'application/json;charset=UTF-8',
        responseType: 'blob'
    });
}
export function saveAllOrgs(params) {
    return request({
        url: '/' + process.env.VUE_APP_UUMSAPPCODE + '/action/org/org/saveAllOrgs/sso?loginuser=' + util.encrypt(store.getters.user.username) + '&appcode=' + process.env.VUE_APP_APPCODE,
        contentType: 'application/json;charset=UTF-8',
        data: params
        // responseType: 'blob'
    });
}