/** 主体区域 **/
.main-container {
  min-height: 100%;
  transition: margin-left .28s;
  padding-left: 170px;
  padding-top:54px;
}

 /** 侧边栏 **/
.sidebar-container {      
  transition: width .28s;
  width: 170px !important;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  /* overflow: hidden; */
}
.sidebar-container .scroll-wrapper{
  position: absolute;
  width:100%;
}
.sidebar-container .horizontal-collapse-transition {
    transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
}
.sidebar-container a {
  display: inline-block;
  width: 100%;
  height: 40px;
  line-height: 40px;
  border-bottom: solid 1px #f5f5f5;
}
.sidebar-container a>.el-menu-item{
  height: 40px;
  line-height: 40px;
}
.sidebar-container .el-menu {
  border: none;
  width: 100% !important;
}

.hideSidebar .sidebar-container {
  width: 36px !important;
}
.hideSidebar .main-container {
  margin-left: 36px;
}
.hideSidebar .submenu-title-noDropdown {
  padding-left: 10px !important;
  position: relative;
}      
.hideSidebar .submenu-title-noDropdown .el-tooltip {
  padding: 0 10px !important;
}
.hideSidebar .el-submenu > .el-submenu__title {
  padding-left: 10px !important;          
}
.hideSidebar .el-submenu > .el-submenu__title>span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}
.hideSidebar .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.sidebar-container .nest-menu .el-submenu > .el-submenu__title,
.sidebar-container .el-submenu .el-menu-item {
  min-width: 200px !important;
  background-color: transparent !important;
}
/* 左侧菜单 */
.el-submenu__title:hover {
  background-color: rgba(255,255,255,.15) !important;
}
/* .is-active .el-submenu__title {
  background-color: rgba(255, 255, 255, .2) !important;
} */
.el-submenu__title i { color: #fff;}
.is-active ul.el-menu li.is-active {
  background-color: rgb(255, 255, 255, .2) !important;
}
.submenu-title-noDropdown:hover {
  background-color: rgba(255,255,255,.15) !important;
}
/* 顶部面包屑 */
.el-breadcrumb__inner a {
  font-weight: normal;
  color: #484848;
}
.el-breadcrumb__inner .no-redirect {
  color: #B1B1B1;
}
.el-breadcrumb__separator {
  margin: 0 20px;
}

.icon{margin-right:16px;vertical-align:-0.5em;}
.sidebar-container .nest-menu .el-submenu > .el-submenu__title:hover,
.sidebar-container .el-submenu .el-menu-item:hover{
    background-color: rgba(255,255,255,.15) !important;
}
.el-menu--collapse .el-menu .el-submenu {
  min-width: 180px !important;
}
.menuLeft .el-submenu,.menuLeft .el-submenu__title{border-bottom: solid 1px #f5f5f5 !important;}
.menuLeft .menu-wrapper{border-top:solid 1px #f5f5f5;}
.menuLeft .el-submenu__title{height: 40px;line-height: 40px;}
.hideSidebar .navbar,.hideSidebar .tabnav{left:36px;}

/** 适配移动端**/
.mobile.hideSidebar .sidebar-container {
  transition-duration: 0.3s;
  transform: translate3d(-180px, 0, 0);
}

.mobile .main-container {
  margin-left: 0px;
}
.mobile .sidebar-container {
  top: 50px;
  transition: transform .28s;
  width: 180px !important;
}

.withoutAnimation .main-container,
.withoutAnimation .sidebar-container {
    transition: none;
}