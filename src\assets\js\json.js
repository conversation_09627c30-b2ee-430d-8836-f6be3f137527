const myDcision = [
    {//普通决策（全展）
        "id": "dict0601004",
        "groupId": null,
        "appCode": "dict",
        "processDefId": "com.simbest.dict.flow.province_recruitment_plan_create",
        "processDefName": "省公司招募方案创建流程",
        "activityDefId": "dict.startToDepartmentDirector",
        "activityDefName": "请部门主管审批",
        "decisionId": "dict.startToDeputyManager",
        "decisionName": "请部门副总经理审批",
        "opinion": "请审批",
        "spare2": null,
        "decisionConfig": "[{&quot;type&quot;:&quot;flowType&quot;,&quot;typeValue&quot;:&quot;normalStep&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;filterUserType&quot;,&quot;typeValue&quot;:&quot;departmentRule&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;position&quot;,&quot;includeValue&quot;:&quot;74,54&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;pageType&quot;,&quot;typeValue&quot;:&quot;tree&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择#treeAll#true#true#user&quot;,&quot;formula&quot;:&quot;&quot;}]",
        "decisionType": null,
        "userFlag": "a"
    },
    {//只有一个人，默认选中
        "id": "dict0601005",
        "groupId": null,
        "appCode": "dict",
        "processDefId": "com.simbest.dict.flow.province_recruitment_plan_create",
        "processDefName": "省公司招募方案创建流程",
        "activityDefId": "dict.startToDepartmentDirector",
        "activityDefName": "请部门主管审批",
        "decisionId": "dict.startToManager",
        "decisionName": "请部门总经理审批",
        "opinion": "请审批",
        "spare2": null,
        "decisionConfig": "[{&quot;type&quot;:&quot;flowType&quot;,&quot;typeValue&quot;:&quot;normalStep&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;filterUserType&quot;,&quot;typeValue&quot;:&quot;departmentRule&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;position&quot;,&quot;includeValue&quot;:&quot;53,64,73,75&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;pageType&quot;,&quot;typeValue&quot;:&quot;tree&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择#treeAll#true#true#user&quot;,&quot;formula&quot;:&quot;&quot;}]",
        "decisionType": null,
        "userFlag": "b"
    },
    {//群组
        "id": "zjsjpx005",
        "groupId": null,
        "appCode": "zjsjpx",
        "processDefId": "com.zjsjpx.flow.zjsjpx",
        "processDefName": "最佳实践评选流程",
        "activityDefId": "zjsjpx.provinceManagerAuditComplete",
        "activityDefName": "管理员审批中",
        "decisionId": "zjsjpx.provinceManagerAuditCompleteToFirstTechnology",
        "decisionName": "请相关评委初评",
        "opinion": "请相关评委初评",
        "spare2": null,
        "decisionConfig": "[{&quot;type&quot;:&quot;flowType&quot;,&quot;typeValue&quot;:&quot;normalStep&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;},{&quot;type&quot;:&quot;filterUserType&quot;,&quot;typeValue&quot;:&quot;noRule&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;省公司评委&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;filterUserType&quot;,&quot;typeValue&quot;:&quot;noRule&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;分公司评委&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;group&quot;,&quot;includeValue&quot;:&quot;G00971&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;省公司评委&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;org&quot;,&quot;includeValue&quot;:&quot;1072587687155687424,1073052629934510080,1071507126455828480,1073053445196378112,1075102991269937152,1074760099205169152,1074064193589264384,1072593893970329600,1073035303212576768,1072665990633840640,1075599184841056256,1073050155778490368,1075580986225840128,1074118202879066112,4772281615772889133,1073071529887428608,1074882253720043520,4772366715949515694&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;分公司评委&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;position&quot;,&quot;includeValue&quot;:&quot;33,3300,39,5500,7600,76,44,55&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;分公司评委&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;pageType&quot;,&quot;typeValue&quot;:&quot;tab&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;省公司评委#treeAll#false#false#user&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;pageType&quot;,&quot;typeValue&quot;:&quot;tab&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;分公司评委#treeAll#false#false#user&quot;,&quot;formula&quot;:&quot;&quot;}]",
        "decisionType": null,
        "userFlag": "c"
    },
    {//确认归档，无人员
        "id": "dict20221013004",
        "groupId": null,
        "appCode": "dict",
        "processDefId": "com.simbest.dict.flow.qlcgt_team_build_province",
        "processDefName": "全流程贯通团队组建审批流程-省",
        "activityDefId": "dict.ToStart",
        "activityDefName": "起草人确认归档",
        "decisionId": "end",
        "decisionName": "确认归档",
        "opinion": "请办理",
        "spare2": null,
        "decisionConfig": "[{&quot;type&quot;:&quot;flowType&quot;,&quot;typeValue&quot;:&quot;normalStep&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;},{&quot;type&quot;:&quot;activiType&quot;,&quot;typeValue&quot;:&quot;copy&quot;,&quot;includeValue&quot;:&quot;dict.copy&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;pageType&quot;,&quot;typeValue&quot;:&quot;tree&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;#treeAll#true#false#user#companySelect&quot;,&quot;formula&quot;:&quot;&quot;}]",
        "decisionType": null,
        "userFlag": "d"
    },
    {//确认归档，无人员（抄送）
        "id": "dict20221013005",
        "groupId": null,
        "appCode": "dict",
        "processDefId": "com.simbest.dict.flow.qlcgt_team_build_province",
        "processDefName": "全流程贯通团队组建审批流程-省",
        "activityDefId": "dict.ToStart",
        "activityDefName": "起草人确认归档",
        "decisionId": "dict.copy",
        "decisionName": "copy#抄送",
        "opinion": "请阅知",
        "spare2": null,
        "decisionConfig": "[{&quot;type&quot;:&quot;flowType&quot;,&quot;typeValue&quot;:&quot;normalStep&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;filterUserType&quot;,&quot;typeValue&quot;:&quot;orgRule&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},{&quot;type&quot;:&quot;pageType&quot;,&quot;typeValue&quot;:&quot;tree&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择#treeAll#false#false#user&quot;,&quot;formula&quot;:&quot;&quot;}]",
        "decisionType": null,
        "userFlag": "d#copy"
    },
    {//选组织
        "id": "hggl_pro_risk_warn_008",
        "groupId": null,
        "appCode": "hggl",
        "processDefId": "com.hggl.flow.hggl_pro_risk_warn",
        "processDefName": "风险合规-风险预警-省公司",
        "activityDefId": "hggl.start_bl",
        "activityDefName": "发起人办理",
        "decisionId": "hggl.applicant_pass",
        "decisionName": "请签收",
        "opinion": "请签收",
        "spare2": null,
        "decisionConfig": "[{&quot;type&quot;:&quot;flowType&quot;,&quot;typeValue&quot;:&quot;normalStep&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;filterUserType&quot;,&quot;typeValue&quot;:&quot;noRule&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择主办部门&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;position&quot;,&quot;includeValue&quot;:&quot;53,75,49&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择主办部门&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;pageType&quot;,&quot;typeValue&quot;:&quot;tab&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择主办部门#treeAll#false#true#orguser&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;activiType&quot;,&quot;typeValue&quot;:&quot;copy&quot;,&quot;includeValue&quot;:&quot;hggl.copy_and_report_pro&quot;,&quot;excludeValue&quot;:&quot;&quot;}]",
        "decisionType": null,
        "userFlag": "e"
    },
    {
        "id": "hggl_pro_risk_warn_009",
        "groupId": null,
        "appCode": "hggl",
        "processDefId": "com.hggl.flow.hggl_pro_risk_warn",
        "processDefName": "风险合规-风险预警-省公司",
        "activityDefId": "hggl.start_bl",
        "activityDefName": "发起人办理",
        "decisionId": "hggl.copy_and_report_pro",
        "decisionName": "copy#抄报",
        "opinion": "请阅示",
        "spare2": null,
        "decisionConfig": "[{&quot;type&quot;:&quot;flowType&quot;,&quot;typeValue&quot;:&quot;normalStep&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;filterUserType&quot;,&quot;typeValue&quot;:&quot;noRule&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;activiType&quot;,&quot;typeValue&quot;:&quot;copy&quot;,&quot;includeValue&quot;:&quot;hggl.copy_and_report_pro&quot;,&quot;excludeValue&quot;:&quot;nflfx.copy_to_end&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;org&quot;,&quot;includeValue&quot;:&quot;4772338661636601428&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;chooseUserType&quot;,&quot;typeValue&quot;:&quot;position&quot;,&quot;includeValue&quot;:&quot;3,4,46&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择&quot;,&quot;formula&quot;:&quot;&quot;},\n{&quot;type&quot;:&quot;pageType&quot;,&quot;typeValue&quot;:&quot;tab&quot;,&quot;includeValue&quot;:&quot;&quot;,&quot;excludeValue&quot;:&quot;&quot;,&quot;groupType&quot;:&quot;请选择#treeAll#false#false#user&quot;,&quot;formula&quot;:&quot;&quot;}\n]",
        "decisionType": null,
        "userFlag": "e#copy"
    }

];

const myUsers = {
    "a": [
        {
            "user": [
                {
                    "id": "dengjun",
                    "name": "邓君",
                    "parentId": "2140887567677891967",
                    "displayOrder": 5000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liuguohui",
                    "name": "刘国辉",
                    "parentId": "2140887567677891967",
                    "displayOrder": 5100,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhangyifei",
                    "name": "张翼飞",
                    "parentId": "2140887567677891967",
                    "displayOrder": 5200,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "huangxiaofan",
                    "name": "黄晓帆",
                    "parentId": "2140887567677891967",
                    "displayOrder": 5400,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "00000000000000000000",
                    "name": "河南移动",
                    "parentId": null,
                    "displayOrder": 1,
                    "treeType": "org",
                    "treeLevel": 0,
                    "defaultSelectUser": null,
                    "orgDisplayName": "中国移动通信集团河南有限公司",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772338661636601428",
                    "name": "省公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 99,
                    "treeType": "org",
                    "treeLevel": 1,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "2140887567677891967",
                    "name": "政企客户部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 700,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\政企客户部",
                    "cancelSelectUser": null
                }
            ],
            "page": "treeAll",
            "singleSel": "true",
            "requSel": "true",
            "display": "user",
            "group": "请选择"
        }
    ],
    "b": [
        {
            "user": [
                {
                    "id": "zhangpengcheng",
                    "name": "张鹏程",
                    "parentId": "2140887567677891967",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "00000000000000000000",
                    "name": "河南移动",
                    "parentId": null,
                    "displayOrder": 1,
                    "treeType": "org",
                    "treeLevel": 0,
                    "defaultSelectUser": null,
                    "orgDisplayName": "中国移动通信集团河南有限公司",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772338661636601428",
                    "name": "省公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 99,
                    "treeType": "org",
                    "treeLevel": 1,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "2140887567677891967",
                    "name": "政企客户部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 700,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\政企客户部",
                    "cancelSelectUser": null
                }
            ],
            "page": "treeAll",
            "singleSel": "true",
            "requSel": "true",
            "display": "user",
            "group": "请选择"
        }
    ],
    "c": [
        {
            "page": "treeAll",
            "singleSel": "false",
            "requSel": "false",
            "display": "user",
            "group": "省公司评委",
            "user": [
                {
                    "id": "shifenggai",
                    "name": "史凤改",
                    "parentId": "383225072648184341",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yangjixue",
                    "name": "杨继学",
                    "parentId": "383225072648184341",
                    "displayOrder": 1600,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "baixuejin",
                    "name": "白雪锦",
                    "parentId": "4772347866196722492",
                    "displayOrder": 3500,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wugang",
                    "name": "吴刚",
                    "parentId": "4772347866196722492",
                    "displayOrder": 7000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "miaoxiaoqiao",
                    "name": "苗晓巧",
                    "parentId": "4772347866196722492",
                    "displayOrder": 22000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yeyilin",
                    "name": "叶亦琳",
                    "parentId": "4772351610860621119",
                    "displayOrder": 30000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhanglin",
                    "name": "张琳",
                    "parentId": "4805807512786303562",
                    "displayOrder": 30000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yeyilin",
                    "name": "叶亦琳",
                    "parentId": "1070598671214768128",
                    "displayOrder": 30000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhangjingqing",
                    "name": "张景青",
                    "parentId": "383225072648184341",
                    "displayOrder": 50020,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "xindanhua",
                    "name": "辛丹华",
                    "parentId": "383225072648184341",
                    "displayOrder": 61000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "mafang",
                    "name": "马方方",
                    "parentId": "4805770522679087484",
                    "displayOrder": 70010,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liuyiwei",
                    "name": "刘艺玮",
                    "parentId": "4805770522679087484",
                    "displayOrder": 75010,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liukai",
                    "name": "刘凯",
                    "parentId": "4805770522679087484",
                    "displayOrder": 75020,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liuxing",
                    "name": "刘兴",
                    "parentId": "4805807512786303562",
                    "displayOrder": 75020,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liyaping2",
                    "name": "李亚萍",
                    "parentId": "4805770522679087484",
                    "displayOrder": 80760,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wanghuaizhao",
                    "name": "王怀朝",
                    "parentId": "383225072648184341",
                    "displayOrder": 81000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "sunke5",
                    "name": "孙克",
                    "parentId": "4805807512786303562",
                    "displayOrder": 85160,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "shixuan1",
                    "name": "施轩",
                    "parentId": "383225072648184341",
                    "displayOrder": 90001,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "huangningning1",
                    "name": "黄宁宁",
                    "parentId": "4805770522679087484",
                    "displayOrder": 100000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yanglifan",
                    "name": "杨丽帆",
                    "parentId": "4805807512786303562",
                    "displayOrder": 100000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "00000000000000000000",
                    "name": "河南移动",
                    "parentId": null,
                    "displayOrder": 1,
                    "treeType": "org",
                    "treeLevel": 0,
                    "defaultSelectUser": null,
                    "orgDisplayName": "中国移动通信集团河南有限公司",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772338661636601428",
                    "name": "省公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 99,
                    "treeType": "org",
                    "treeLevel": 1,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "383225072648184341",
                    "name": "发展战略部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 200,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\发展战略部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772351610860621119",
                    "name": "市场经营部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 600,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\市场经营部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772347866196722492",
                    "name": "客户服务部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1070598671214768128",
                    "name": "营销策划中心",
                    "parentId": "4772351610860621119",
                    "displayOrder": 10000,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\市场经营部\\营销策划中心",
                    "cancelSelectUser": null
                },
                {
                    "id": "4805770522679087484",
                    "name": "服务管理室",
                    "parentId": "4772347866196722492",
                    "displayOrder": 11000,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\客户服务部\\服务管理室",
                    "cancelSelectUser": null
                },
                {
                    "id": "4805807512786303562",
                    "name": "投诉管理室",
                    "parentId": "4772347866196722492",
                    "displayOrder": 12000,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\客户服务部\\投诉管理室",
                    "cancelSelectUser": null
                }
            ]
        },
        {
            "page": "treeAll",
            "singleSel": "true",
            "requSel": "false",
            "display": "user",
            "group": "分公司评委",
            "user": [
                {
                    "id": "yanxiaomeng",
                    "name": "闫筱萌",
                    "parentId": "1071507126455828480",
                    "displayOrder": 6000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wangyi1",
                    "name": "王谊",
                    "parentId": "1071507126455828480",
                    "displayOrder": 7000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "baiyali",
                    "name": "白雅丽",
                    "parentId": "1072665990633840640",
                    "displayOrder": 10000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "hailijie",
                    "name": "海丽洁",
                    "parentId": "1074118202879066112",
                    "displayOrder": 11000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "leichunyu",
                    "name": "雷春雨",
                    "parentId": "1073052629934510080",
                    "displayOrder": 11000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "lizhaofeng",
                    "name": "李兆峰",
                    "parentId": "1072587687155687424",
                    "displayOrder": 11000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "duanyujing",
                    "name": "段玉静",
                    "parentId": "1073053445196378112",
                    "displayOrder": 12000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "fufang",
                    "name": "付芳",
                    "parentId": "1074882253720043520",
                    "displayOrder": 12000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhaochanghua",
                    "name": "赵长华",
                    "parentId": "1074118202879066112",
                    "displayOrder": 12000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "lilu",
                    "name": "李璐",
                    "parentId": "1074064193589264384",
                    "displayOrder": 12000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "niuliying",
                    "name": "牛丽英",
                    "parentId": "1073050155778490368",
                    "displayOrder": 13000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhanghaihong",
                    "name": "张海虹",
                    "parentId": "1075599184841056256",
                    "displayOrder": 13000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wangfang1",
                    "name": "王芳",
                    "parentId": "1073071529887428608",
                    "displayOrder": 13000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wangli1",
                    "name": "王丽",
                    "parentId": "1074064193589264384",
                    "displayOrder": 14000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "kongying",
                    "name": "孔瑛",
                    "parentId": "4772366715949515694",
                    "displayOrder": 14000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wanghuan1",
                    "name": "王欢",
                    "parentId": "1074760099205169152",
                    "displayOrder": 14000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "caojing",
                    "name": "曹婧",
                    "parentId": "1073035303212576768",
                    "displayOrder": 15000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "qinnan",
                    "name": "秦楠",
                    "parentId": "4772366715949515694",
                    "displayOrder": 15000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "guohongyan",
                    "name": "郭红燕",
                    "parentId": "1074760099205169152",
                    "displayOrder": 15000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yangkun1",
                    "name": "杨琨",
                    "parentId": "1072593893970329600",
                    "displayOrder": 15000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liyan5",
                    "name": "李艳",
                    "parentId": "1073071529887428608",
                    "displayOrder": 15500,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "songxiaomei",
                    "name": "宋晓梅",
                    "parentId": "1073052629934510080",
                    "displayOrder": 16000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "tieyanyan",
                    "name": "帖艳艳",
                    "parentId": "4772281615772889133",
                    "displayOrder": 17000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liqun",
                    "name": "李群",
                    "parentId": "1074760099205169152",
                    "displayOrder": 18000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "hanmanman",
                    "name": "韩曼曼",
                    "parentId": "1075102991269937152",
                    "displayOrder": 18000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "chenjunxiang",
                    "name": "陈俊香",
                    "parentId": "1075580986225840128",
                    "displayOrder": 18000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "00000000000000000000",
                    "name": "河南移动",
                    "parentId": null,
                    "displayOrder": 1,
                    "treeType": "org",
                    "treeLevel": 0,
                    "defaultSelectUser": null,
                    "orgDisplayName": "中国移动通信集团河南有限公司",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772276805467173986",
                    "name": "郑州分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 51000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "郑州分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772416258988872377",
                    "name": "南阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 52000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "南阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772294881701191626",
                    "name": "周口分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 53000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "周口分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772398387391759323",
                    "name": "洛阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 54000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "洛阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772190764623477349",
                    "name": "商丘分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 55000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "商丘分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772237158131728644",
                    "name": "信阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 56000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "信阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772222692656252440",
                    "name": "新乡分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 57000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "新乡分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772309833745710077",
                    "name": "驻马店分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 58000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "驻马店分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772319337553908874",
                    "name": "安阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 59000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "安阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772385069455370245",
                    "name": "开封分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 60000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "开封分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772159046344071595",
                    "name": "平顶山分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 61000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "平顶山分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772253677731688102",
                    "name": "许昌分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 62000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "许昌分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772436821803101973",
                    "name": "濮阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 63000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "濮阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772371646798367640",
                    "name": "焦作分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 64000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "焦作分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772173802809103642",
                    "name": "三门峡分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 65000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "三门峡分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772428442783965979",
                    "name": "漯河分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 66000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "漯河分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772356884662698724",
                    "name": "鹤壁分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 67000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "鹤壁分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772365719866026009",
                    "name": "济源分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 68000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "济源分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772366715949515694",
                    "name": "市场经营部",
                    "parentId": "4772365719866026009",
                    "displayOrder": 3300,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "济源分公司\\市场经营部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1071507126455828480",
                    "name": "客户服务部",
                    "parentId": "4772416258988872377",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "南阳分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1072587687155687424",
                    "name": "客户服务部",
                    "parentId": "4772173802809103642",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "三门峡分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1072593893970329600",
                    "name": "客户服务部",
                    "parentId": "4772385069455370245",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "开封分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1072665990633840640",
                    "name": "客户服务部",
                    "parentId": "4772398387391759323",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "洛阳分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1073035303212576768",
                    "name": "客户服务部",
                    "parentId": "4772222692656252440",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "新乡分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1073050155778490368",
                    "name": "客户服务部",
                    "parentId": "4772436821803101973",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "濮阳分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1073052629934510080",
                    "name": "客户服务部",
                    "parentId": "4772237158131728644",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "信阳分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1073053445196378112",
                    "name": "客户服务部",
                    "parentId": "4772294881701191626",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "周口分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1073071529887428608",
                    "name": "客户服务部",
                    "parentId": "4772309833745710077",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "驻马店分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1074064193589264384",
                    "name": "客户服务部",
                    "parentId": "4772159046344071595",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "平顶山分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1074118202879066112",
                    "name": "客户服务部",
                    "parentId": "4772253677731688102",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "许昌分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1074760099205169152",
                    "name": "客户服务部",
                    "parentId": "4772319337553908874",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "安阳分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1074882253720043520",
                    "name": "客户服务部",
                    "parentId": "4772356884662698724",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "鹤壁分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1075102991269937152",
                    "name": "客户服务部",
                    "parentId": "4772190764623477349",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "商丘分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1075580986225840128",
                    "name": "客户服务部",
                    "parentId": "4772371646798367640",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "焦作分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1075599184841056256",
                    "name": "客户服务部",
                    "parentId": "4772428442783965979",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "漯河分公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772281615772889133",
                    "name": "客户服务部",
                    "parentId": "4772276805467173986",
                    "displayOrder": 3500,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "郑州分公司\\客户服务部",
                    "cancelSelectUser": null
                }
            ]
        }
    ],
    "d": [
        {
            "user": null,
            "page": "treeAll",
            "singleSel": "true",
            "requSel": "false",
            "display": "user",
            "group": "normalGrouping"
        }
    ],
    "d#copy": [
        {
            "user": [
                {
                    "id": "tonglingshuai",
                    "name": "仝另帅",
                    "parentId": "1070598672066211840",
                    "displayOrder": 30000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": true,
                    "orgDisplayName": null,
                    "cancelSelectUser": false
                },
                {
                    "id": "qinfumin",
                    "name": "秦富民",
                    "parentId": "1070598672066211840",
                    "displayOrder": 70090,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": true,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "sunxian",
                    "name": "孙献",
                    "parentId": "1070598672066211840",
                    "displayOrder": 75230,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": true,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "00000000000000000000",
                    "name": "河南移动",
                    "parentId": null,
                    "displayOrder": 1,
                    "treeType": "org",
                    "treeLevel": 0,
                    "defaultSelectUser": null,
                    "orgDisplayName": "中国移动通信集团河南有限公司",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772338661636601428",
                    "name": "省公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 99,
                    "treeType": "org",
                    "treeLevel": 1,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "2140887567677891967",
                    "name": "政企客户部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 700,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\政企客户部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1070598672066211840",
                    "name": "创新业务室",
                    "parentId": "2140887567677891967",
                    "displayOrder": 15000,
                    "treeType": "org",
                    "treeLevel": 3,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\政企客户部\\创新业务室",
                    "cancelSelectUser": null
                }
            ],
            "page": "treeAll",
            "singleSel": false,
            "requSel": true,
            "display": "user",
            "group": "normalGrouping",
            "defaultSelectUser": true
        }
    ],
    "e": [
        {
            "page": "treeAll",
            "singleSel": false,
            "requSel": "true",
            "display": "orguser",
            "group": "请选择主办部门",
            "user": [
                {
                    "id": "liuchangye",
                    "name": "刘昌晔",
                    "parentId": "4772385069455370245",
                    "displayOrder": 900,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wangyong1",
                    "name": "汪勇",
                    "parentId": "4772276805467173986",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yangjianjun",
                    "name": "杨建军",
                    "parentId": "4772340095222653632",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhangshuguang",
                    "name": "张曙光",
                    "parentId": "3024910528628002646",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhangyongjian",
                    "name": "张泳健",
                    "parentId": "1070567113873788928",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "caoning",
                    "name": "曹宁",
                    "parentId": "4772356884662698724",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "mashaojie",
                    "name": "马少杰",
                    "parentId": "4772216161202753552",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhangxinpeng",
                    "name": "张新鹏",
                    "parentId": "2700529555002203858",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhangxu",
                    "name": "张旭",
                    "parentId": "4772428442783965979",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "peizhaohua",
                    "name": "裴照华",
                    "parentId": "2700526267653981965",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wanglei4",
                    "name": "王磊",
                    "parentId": "4772398387391759323",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "xiongguoqin",
                    "name": "熊国琴",
                    "parentId": "2840736922849932269",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhaoxiaokang",
                    "name": "赵宵康",
                    "parentId": "2336667106167789927",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "shiwei",
                    "name": "石维",
                    "parentId": "4772352556325512569",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liuyingzhong",
                    "name": "刘颖众",
                    "parentId": "4772348603698773485",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "baiyu",
                    "name": "白钰",
                    "parentId": "4772309833745710077",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wuyuzhan",
                    "name": "吴玉展",
                    "parentId": "5918018156204925808",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "weijizhou",
                    "name": "魏际洲",
                    "parentId": "4772354741955101926",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": true,
                    "orgDisplayName": null,
                    "cancelSelectUser": false
                },
                {
                    "id": "zhangpengcheng",
                    "name": "张鹏程",
                    "parentId": "2140887567677891967",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "gaokunpeng",
                    "name": "郜鲲鹏",
                    "parentId": "4772416258988872377",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "huwei",
                    "name": "虎威",
                    "parentId": "4772190764623477349",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yujianwei",
                    "name": "于建伟",
                    "parentId": "4772339340089968769",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wangzhilong1",
                    "name": "王志龙",
                    "parentId": "4772253677731688102",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "shifenggai",
                    "name": "史凤改",
                    "parentId": "383225072648184341",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": true,
                    "orgDisplayName": null,
                    "cancelSelectUser": false
                },
                {
                    "id": "wangjingzhong",
                    "name": "王景仲",
                    "parentId": "1111168635321700352",
                    "displayOrder": 1500,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wanbangkui",
                    "name": "万帮奎",
                    "parentId": "4772365719866026009",
                    "displayOrder": 1500,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "hurui",
                    "name": "胡蕊",
                    "parentId": "4772336643099316130",
                    "displayOrder": 1500,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "houxiaodong",
                    "name": "侯晓东",
                    "parentId": "4772371646798367640",
                    "displayOrder": 1500,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wuwei",
                    "name": "吴巍",
                    "parentId": "4772294881701191626",
                    "displayOrder": 1900,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhouqiang",
                    "name": "周强",
                    "parentId": "4772222692656252440",
                    "displayOrder": 2000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "leichengjie",
                    "name": "雷呈杰",
                    "parentId": "1070567113739571200",
                    "displayOrder": 2000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liuna",
                    "name": "柳娜",
                    "parentId": "4772330917707393327",
                    "displayOrder": 2000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "lijie",
                    "name": "李杰",
                    "parentId": "4772329960102299737",
                    "displayOrder": 2000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wangxihong",
                    "name": "王希红",
                    "parentId": "4772237158131728644",
                    "displayOrder": 2000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "shangyunpeng",
                    "name": "尚云鹏",
                    "parentId": "4772173802809103642",
                    "displayOrder": 2000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "miaotailei",
                    "name": "苗太雷",
                    "parentId": "4772350885227876056",
                    "displayOrder": 2000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "fanguoxin",
                    "name": "范国鑫",
                    "parentId": "4772351610860621119",
                    "displayOrder": 2000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yangxiaoyu",
                    "name": "杨晓宇",
                    "parentId": "4772220220316150100",
                    "displayOrder": 2800,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "suyong",
                    "name": "苏勇",
                    "parentId": "1070567113513078784",
                    "displayOrder": 3000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "wangliang",
                    "name": "王靓",
                    "parentId": "4772319337553908874",
                    "displayOrder": 3000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "baixuejin",
                    "name": "白雪锦",
                    "parentId": "4772347866196722492",
                    "displayOrder": 3500,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "shaotong",
                    "name": "邵瞳",
                    "parentId": "4772159046344071595",
                    "displayOrder": 4000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "baiwenbo",
                    "name": "白文博",
                    "parentId": "4772436821803101973",
                    "displayOrder": 6000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "peizhaohua1",
                    "name": "裴照华",
                    "parentId": "1161377151649603584",
                    "displayOrder": 10000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhangyongjian2",
                    "name": "张泳健",
                    "parentId": "2133635433797240541",
                    "displayOrder": 100000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "zhangliuzhen1",
                    "name": "张留振",
                    "parentId": "4772395411472846483",
                    "displayOrder": 100000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "00000000000000000000",
                    "name": "河南移动",
                    "parentId": null,
                    "displayOrder": 1,
                    "treeType": "org",
                    "treeLevel": 0,
                    "defaultSelectUser": null,
                    "orgDisplayName": "中国移动通信集团河南有限公司",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772338661636601428",
                    "name": "省公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 99,
                    "treeType": "org",
                    "treeLevel": 1,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772354741955101926",
                    "name": "综合部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 100,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": true,
                    "orgDisplayName": "省公司\\综合部",
                    "cancelSelectUser": false
                },
                {
                    "id": "383225072648184341",
                    "name": "发展战略部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 200,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": true,
                    "orgDisplayName": "省公司\\发展战略部",
                    "cancelSelectUser": false
                },
                {
                    "id": "2336667106167789927",
                    "name": "法律事务部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 300,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\法律事务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772350885227876056",
                    "name": "人力资源部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 400,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\人力资源部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772329960102299737",
                    "name": "财务部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 500,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\财务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772348603698773485",
                    "name": "内审部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 510,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\内审部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772216161202753552",
                    "name": "供应链管理部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 520,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\供应链管理部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772351610860621119",
                    "name": "市场经营部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 600,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\市场经营部",
                    "cancelSelectUser": null
                },
                {
                    "id": "2140887567677891967",
                    "name": "政企客户部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 700,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\政企客户部",
                    "cancelSelectUser": null
                },
                {
                    "id": "2840736922849932269",
                    "name": "行业客户拓展中心",
                    "parentId": "4772338661636601428",
                    "displayOrder": 800,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\行业客户拓展中心",
                    "cancelSelectUser": null
                },
                {
                    "id": "1070567113873788928",
                    "name": "信息服务能力中心",
                    "parentId": "4772338661636601428",
                    "displayOrder": 900,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\信息服务能力中心",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772347866196722492",
                    "name": "客户服务部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\客户服务部",
                    "cancelSelectUser": null
                },
                {
                    "id": "2700526267653981965",
                    "name": "信息技术管理部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1100,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\信息技术管理部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1161377151649603584",
                    "name": "数据管理部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1110,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\数据管理部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772339340089968769",
                    "name": "规划技术部（乡村振兴办公室）",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1200,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\规划技术部（乡村振兴办公室）",
                    "cancelSelectUser": null
                },
                {
                    "id": "1070567113513078784",
                    "name": "工程建设管理部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1400,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\工程建设管理部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772352556325512569",
                    "name": "网络部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1500,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\网络部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1070567113739571200",
                    "name": "网络资源管理专项推进办公室",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1600,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\网络资源管理专项推进办公室",
                    "cancelSelectUser": null
                },
                {
                    "id": "2700529555002203858",
                    "name": "网络管理中心",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1700,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\网络管理中心",
                    "cancelSelectUser": null
                },
                {
                    "id": "2133635433797240541",
                    "name": "信息安全部",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1800,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\信息安全部",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772330917707393327",
                    "name": "党委办公室（党群工作部）",
                    "parentId": "4772338661636601428",
                    "displayOrder": 2000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\党委办公室（党群工作部）",
                    "cancelSelectUser": null
                },
                {
                    "id": "5918018156204925808",
                    "name": "巡察工作办公室（党风廉政办公室）",
                    "parentId": "4772338661636601428",
                    "displayOrder": 2100,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\巡察工作办公室（党风廉政办公室）",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772340095222653632",
                    "name": "纪委办公室",
                    "parentId": "4772338661636601428",
                    "displayOrder": 2200,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\纪委办公室",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772336643099316130",
                    "name": "工会",
                    "parentId": "4772338661636601428",
                    "displayOrder": 2300,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\工会",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772395411472846483",
                    "name": "客户服务中心",
                    "parentId": "4772338661636601428",
                    "displayOrder": 2900,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\客户服务中心",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772276805467173986",
                    "name": "郑州分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 51000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "郑州分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772416258988872377",
                    "name": "南阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 52000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "南阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772294881701191626",
                    "name": "周口分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 53000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "周口分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772398387391759323",
                    "name": "洛阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 54000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "洛阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772190764623477349",
                    "name": "商丘分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 55000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "商丘分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772237158131728644",
                    "name": "信阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 56000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "信阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772222692656252440",
                    "name": "新乡分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 57000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "新乡分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772309833745710077",
                    "name": "驻马店分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 58000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "驻马店分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772319337553908874",
                    "name": "安阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 59000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "安阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772385069455370245",
                    "name": "开封分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 60000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "开封分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772159046344071595",
                    "name": "平顶山分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 61000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "平顶山分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772253677731688102",
                    "name": "许昌分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 62000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "许昌分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772436821803101973",
                    "name": "濮阳分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 63000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "濮阳分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772371646798367640",
                    "name": "焦作分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 64000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "焦作分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772173802809103642",
                    "name": "三门峡分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 65000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "三门峡分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772428442783965979",
                    "name": "漯河分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 66000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "漯河分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772356884662698724",
                    "name": "鹤壁分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 67000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "鹤壁分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772365719866026009",
                    "name": "济源分公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 68000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "济源分公司\\公司管理层",
                    "cancelSelectUser": null
                },
                {
                    "id": "3024910528628002646",
                    "name": "终端运营中心",
                    "parentId": "00000000000000000000",
                    "displayOrder": 88888,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "河南分公司\\终端运营中心",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772220220316150100",
                    "name": "正数公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 88889,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "正数公司\\外派人员管理部",
                    "cancelSelectUser": null
                },
                {
                    "id": "1111168635321700352",
                    "name": "郑州数据交易中心",
                    "parentId": "00000000000000000000",
                    "displayOrder": 90000,
                    "treeType": "org",
                    "treeLevel": 2,
                    "defaultSelectUser": null,
                    "orgDisplayName": "郑州数据交易中心\\公司管理层",
                    "cancelSelectUser": null
                }
            ]
        }
    ],
    "e#copy": [
        {
            "page": "treeAll",
            "singleSel": false,
            "requSel": "false",
            "display": "user",
            "group": "请选择",
            "user": [
                {
                    "id": "louxiangping",
                    "name": "楼向平",
                    "parentId": "4772338661636601428",
                    "displayOrder": 49,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "liubangxu",
                    "name": "刘邦旭",
                    "parentId": "4772338661636601428",
                    "displayOrder": 600,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yuqian",
                    "name": "余谦",
                    "parentId": "4772338661636601428",
                    "displayOrder": 700,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": true,
                    "orgDisplayName": null,
                    "cancelSelectUser": false
                },
                {
                    "id": "fanpeiquan",
                    "name": "范培全",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1000,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "mengyongqi",
                    "name": "孟永琪",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1050,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "yanzhigang",
                    "name": "闫志刚",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1120,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "chenshiwei1",
                    "name": "陈世伟",
                    "parentId": "4772338661636601428",
                    "displayOrder": 1500,
                    "treeType": "user",
                    "treeLevel": 10000,
                    "defaultSelectUser": null,
                    "orgDisplayName": null,
                    "cancelSelectUser": null
                },
                {
                    "id": "00000000000000000000",
                    "name": "河南移动",
                    "parentId": null,
                    "displayOrder": 1,
                    "treeType": "org",
                    "treeLevel": 0,
                    "defaultSelectUser": null,
                    "orgDisplayName": "中国移动通信集团河南有限公司",
                    "cancelSelectUser": null
                },
                {
                    "id": "4772338661636601428",
                    "name": "省公司",
                    "parentId": "00000000000000000000",
                    "displayOrder": 99,
                    "treeType": "org",
                    "treeLevel": 1,
                    "defaultSelectUser": null,
                    "orgDisplayName": "省公司\\公司管理层",
                    "cancelSelectUser": null
                }
            ]
        }
    ]

};
export { myDcision,myUsers }
