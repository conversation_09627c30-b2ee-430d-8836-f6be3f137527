<template>
    <div class="app">
      <sb-el-form ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled" :on-ok="handleDoFun" :controlProcess="controlProcess"></sb-el-form>
      <!-- 功能表单 -->
      <!-- cutomcode start1 -->

      <!-- cutomcode end1 -->
    </div>
</template>
  <script>
  import store from "@/store";
  import util from "@/assets/js/public";
  import { Message, MessageBox } from "element-ui";
  import { getDictList } from "@/api/public";
  export default {
    name: 'app',
    data() {
      return {
          nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),
          appFormValue:{},
          appForm:{
            formDisabled: false,
            labelWidth: "160px",
            inline: true,
            formItemList: [],
            currentPageType: "formPage",
          },
          controlProcess: {},
          // cutomcode start

          // cutomcode end
        }
    },
    created() {
        var initValue = {
      applyUser: this.$store.getters.user.truename,
      applyUserName: this.$store.getters.user.username,
      belongCompanyName: this.$store.getters.user.belongCompanyName,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      applyTime: this.nowTime
    };
    this.appFormValue = Object.assign(this.appFormValue,initValue);
    this.appForm.formItemList = this.appForm.formItemList.map((item) => {
      item.control = this.controlProcess[item.key]
      return item;
      });
    },
  mounted() {

  },
    methods: {
      handleDoFun(obj, fun, data) {
        //若一个beforeFun可直接在这个函数里面写
        let n = this[obj[fun]].call(this, obj, data)
        return n
      },
      // cutomcode start

      // cutomcode end
    }
  }
  </script>
  <style scoped>
  .app {
      height: calc(100vh - 100px);
      padding: 10px;
  }
  </style>                  