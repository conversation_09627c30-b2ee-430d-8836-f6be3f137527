<template>
    <div
        class="scroll-container"
        ref="scrollContainer"
        @wheel.prevent="handleScroll"
    >
        <div
            class="scroll-wrapper"
            ref="scrollWrapper"
            :style="{ top: top + 'px', left: left + 'px', width: width + 'px' }"
        >
            <slot></slot>
        </div>
    </div>
</template>
<script>
const delta = 15;
export default {
    name: "scrollBar",
    props: {
        type: {
            type: String,
            default: "top",
        },
        width: {
            type: Number,
        },
    },
    data() {
        return {
            top: 0,
            left: 0,
        };
    },
    methods: {
        handleScroll(e) {
            const eventDelta = e.wheelDelta || -e.deltaY * 3;
            const $container = this.$refs.scrollContainer;
            const $containerHeight = $container.offsetHeight;
            const $containerWidth = $container.offsetWidth;
            const $wrapper = this.$refs.scrollWrapper;
            const $wrapperHeight = $wrapper.offsetHeight;
            const $wrapperWidth = $wrapper.offsetWidth;
            if (this.type === "top") {
                if (eventDelta > 0) {
                    this.top = Math.min(0, this.top + eventDelta);
                } else {
                    if ($containerHeight - delta < $wrapperHeight) {
                        if (
                            this.top <
                            -($wrapperHeight - $containerHeight + delta)
                        ) {
                            this.top = this.top;
                        } else {
                            this.top = Math.max(
                                this.top + eventDelta,
                                $containerHeight - $wrapperHeight - delta
                            );
                        }
                    } else {
                        this.top = 0;
                    }
                }
            } else {
                if (eventDelta > 0) {
                    this.left = Math.min(0, this.left + eventDelta);
                } else {
                    if ($containerWidth < $wrapperWidth) {
                        if (this.left < -($wrapperWidth - $containerWidth)) {
                            this.left = this.left;
                        } else {
                            this.left = Math.max(
                                this.left + eventDelta,
                                $containerWidth - $wrapperWidth
                            );
                        }
                    } else {
                        this.left = 0;
                    }
                }
            }
        },
    },
};
</script>
<style>
.scroll-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}
.sidebar-container {
    position: fixed;
}
.scroll-wrapper {
    position: absolute;
    width: 100%;
}
</style>
