<template>
  <div>
    <span
      class="hamburger"
      @click="toggleClick"
      :class="{ 'is-active': isActive }"
      ><svg-icon icon-class="quanzongguanli" class-name="hamindex"></svg-icon
    ></span>
  </div>
</template>
<script>
export default {
  name: "hamburger",
  props: {
    isActive: {
      type: Boolean,
      default: false
    },
    toggleClick: {
      type: Function,
      default: null
    }
  }
};
</script>
<style scoped>
.hamburger .icon {
  display: inline-block;
  cursor: pointer;
  width: 20px;
  height: 28px;
  transform: rotate(90deg);
  transition: 0.38s;
  transform-origin: 50% 50%;
}
.hamburger.is-active .icon {
  transform: rotate(0deg);
}
</style>
