import request from "@/assets/js/request";

// 获取流程相关数据
export function getProcessInfo(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/apply/getProcessInfo`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

// 获取工单详情
export function getFormDetail(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getFormDetail`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

// 保存草稿
export function saveDraft(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/saveDraft`,
        contentType: "application/json; charset=utf-8",
        loading: true,
        data: params
    });
}

// 流转
export function startProcess(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/startProcessAndTaskSubmitCommon`,
        contentType: "application/json; charset=utf-8",
        loading: true,
        data: params
    });
}

// 工单查询
export function getWorkQuery(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getWorkQuery?source=PC&page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

// 导出
export function exportParameter(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/exportMCExcel`,
        contentType: "application/json; charset=utf-8",
        data: params,
        responseType: "blob"
    });
}

// 废除归档
export function deleteProcess(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/deleteProcess?processInstId=${params.processInstId}&pmInsId=${params.pmInsId}`,
        contentType: "application/json; charset=utf-8",
    });
}

// 终止
export function stopProcess(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/stopProcess?processInstId=${params.processInstId}&pmInsId=${params.pmInsId}`,
        contentType: "application/json; charset=utf-8",
    });
}

/**
 * 查询远程数据列表
 */
 export function getDataByApi(dialogData,params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getDataByApi?apiType=` + dialogData.apiType + "&url=" + dialogData.url,
        contentType: 'application/json;charset=UTF-8',
        data:params
    })
}

// 表单初始化数据-工单编号
export function initForm(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/rule/createOrderNo`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
