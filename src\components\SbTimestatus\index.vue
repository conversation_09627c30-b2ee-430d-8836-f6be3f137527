<template>
	<div class="w100">
		<div class="flex a-c">
			<div class="t-1">体验中心预定情况</div>
			<div class="flex a-c">
				<span class="t-2">空闲中</span>
				<div class="t-3 bg-gray"></div>
			</div>
			<div class="flex a-c">
				<span class="t-2">已预订</span>
				<div class="t-3 bg-blue"></div>
			</div>
		</div>
		<el-table :data="dataList" style="width: 100%">
			<el-table-column label="序号" width="50" type="index">
			</el-table-column>
			<el-table-column prop="date" label="参观日期" width="100">
			</el-table-column>
			<el-table-column label="时间段" align="center">
				<el-table-column align="center" :label="item" v-for="(item, index) in timeList" :key="index">
					<template slot-scope="scope">
						<el-tooltip effect="dark" :content="getContent(scope.row, item)" placement="bottom">
							<div class="w100 h20" :class="[ scope.row.item?.find(a => a.name == item)?.value ? 'bg-blue' : 'bg-gray']"></div>
						</el-tooltip>
					</template>
				</el-table-column>
			</el-table-column>
		</el-table>
	</div>
</template>
<script>
	import { getDataInfoByFindAll } from '@/api/public'
	export default {
		name: "time-status",
		props: {
			// 查询接口地址
			findAll: {
				type: String,
				default: ''
			},
			// 开始时间
			startTime: {
				type: String,
				default: ''
			},
			// 结束时间
			endTime: {
				type: String,
				default: ''
			},
			// 时间间隔
			timeInterval: {
				type: [String, Number],
				default: ''
			},
		},
		data() {
			return {
				dataList: [
					// {
					// 	date: '2016-05-03',
					// 	item: [
					// 		{ name: '9:00', value: '1-1' },
					// 		{ name: '10:00', value: '1-2' }
					// 	],
					// 	titleList: [
					// 		{ id: 1, title: 'xxx公司已预订' },
					// 		{ id: 2, title: 'xxx公司已预订' }
					// 	]
					// },
					// {
					// 	date: '2016-05-03',
					// 	item: [
					// 		{ name: '9:00', value: 0 },
					// 		{ name: '10:00', value: 0 }
					// 	],
					// 	titleList: []
					// }
				],
				timeList: [
					// '9:00', '10:00'
				]
			}
		},
		created() {
			if (this.findAll) {
				this.getList()
			}
		},
		methods: {
			getList() {
				getDataInfoByFindAll(this.findAll, {
					startTime: this.startTime,
					endTime: this.endTime,
					timeInterval: this.timeInterval
				}, true).then(res => {
					this.dataList = res.data.dataList
					this.timeList = res.data.timeList
				})
			},
			getContent(row, time) {
				if (!row?.titleList?.length) {
					return '空闲中'
				}

				let id = row.item?.find(a => a.name == time)?.value?.split('-')[1]
				let title = row.titleList?.filter(a => a.id == id)?.[0].title
				if (title) {
					return title
				} else {
					return '数据错误'
				}
			}
		}
	};
</script>
<style scoped>
	.t-1 {
		text-align: center;
		line-height: 30px;
		font-size: 14px;
		font-weight: bold;
		flex: 1;
	}

	.t-2 {
		margin-right: 5px;
	}

	.t-3 {
		width: 50px;
		height: 16px;
		margin-right: 20px;
	}

	.bg-gray {
		background: #eee;
	}

	.bg-blue {
		background: #99d7fc;
	}

	.flex {
		display: flex;
	}

	.a-c {
		align-items: center;
	}

	.j-c {
		justify-content: center;
	}

	.j-s {
		justify-content: space-between;
	}

	::v-deep .el-table__header-wrapper {
		height: auto;
	}

	.h20 {
		height: 20px;
	}
</style>