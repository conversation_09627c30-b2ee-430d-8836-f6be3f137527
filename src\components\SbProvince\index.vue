<template>
  <div class="flexNowrap">
    <el-select
      v-model="province"
      @change="chooseProvince"
      placeholder="省级地区"
      :size="item.size || 'small'"
      :disabled="item.disabled || false"
    >
      <el-option
        v-for="item in provinceList"
        :key="item.id"
        :label="item.value"
        :value="item.id"
        :disabled="item.disabled || false"
      ></el-option>
    </el-select>
    <el-select
      v-model="city"
      @change="chooseCity"
      placeholder="市级地区"
      :size="item.size || 'small'"
      :disabled="item.disabled || false"
    >
      <el-option
        v-for="item in cityList"
        :key="item.id"
        :label="item.value"
        :value="item.id"
        :disabled="item.disabled || false"
      ></el-option>
    </el-select>
    <el-select
      v-model="county"
      @change="chooseRegion"
      placeholder="区级地区"
      :size="item.size || 'small'"
      :disabled="item.disabled || false"
    >
      <el-option
        v-for="item in countyList"
        :key="item.id"
        :label="item.value"
        :value="item.id"
        :disabled="item.disabled || false"
      ></el-option>
    </el-select>
  </div>
</template>
<script>
import provinceData from "./json.js";
export default {
  name: "SbProvince",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      datalist: provinceData,
      province: null,
      provinceList: [],
      city: null,
      cityList: [],
      county: null,
      countyList: []
    };
  },
  methods: {
    getData() {
      for (var item in this.datalist) {
        //   if (item.match(/0000$/)) {//省
        this.provinceList.push({
          id: item,
          value: this.datalist[item].n[0],
          children: this.datalist[item].c
        });
        //   }
      }
    },
    chooseProvince(e, type) {
      // 分类市级
      this.cityList = [];
      this.city = null;
      this.county = null;
      if (this.datalist[e]) {
        let cs = this.datalist[e].c;
        for (var item in cs) {
          this.cityList.push({
            id: item,
            value: cs[item].n[0],
            children: cs[item].c
          });
        }
        if (!type) this.getVal();
      }
    },
    chooseCity(e, type) {
      // 分类区级
      this.countyList = [];
      this.county = null;
      if (this.datalist[this.province] && this.datalist[this.province].c[e]) {
        let cs = this.datalist[this.province].c[e].c;
        for (var item in cs) {
          this.countyList.push({
            id: item,
            value: cs[item].n[0],
            children: cs[item].c
          });
        }
        if (!type) this.getVal();
      }
    },
    chooseRegion(e) {
      this.getVal();
    },
    getVal() {
      if (this.province || this.city || this.county) {
        let location = [];
        location.push(this.province ? this.datalist[this.province].n[0] : null);
        location.push(
          this.city ? this.datalist[this.province].c[this.city].n[0] : null
        );
        location.push(
          this.county
            ? this.datalist[this.province].c[this.city].c[this.county].n[0]
            : null
        );
        let data = {
          province: this.province,
          city: this.city,
          county: this.county,
          location: location.join(" ")
        };
        if (this.item.rangeName) {
          let rangeName = this.item.rangeName.split(",");
          data[rangeName[0]] = this.province;
          data[rangeName[1]] = this.city;
          data[rangeName[2]] = this.county;
          data[rangeName[3]] = location.join(" ");
        }
        this.$emit("chooseData", data);
      } else {
        this.$emit("chooseData", null);
      }
    }
  },
  created() {
    this.getData();
  },
  watch: {
    "item.defValue": {
      handler: function(newV, oldV) {
        if (newV) {
          for (let j in newV) {
            this[j] = newV[j] || null;
            if (j === "province" && newV[j]) {
              this.chooseProvince(newV[j], true);
            }
            if (j === "city" && newV[j]) {
              this.chooseCity(newV[j], true);
            }
          }
        } else {
          this.province = null;
          this.city = null;
          this.cityList = [];
          this.county = null;
          this.countyList = [];
        }
      },
      immediate: true,
      deep: true
    }
  }
};
</script>
