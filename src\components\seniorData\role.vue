<template>
    <div class="w100 inputBtn">
        <el-input ref="elInput" :type="item.inputType || 'text'" v-bind="$attrs" v-on="$listeners"
            :size="item.size || 'small'" :placeholder="item.placeholder || item.label || '请输入'"
            :disabled="item.disabled || false" :readonly="item.readonly || false" :autosize="item.autosize || false">
            <el-button v-if="item.appendShow" slot="append" :size="item.size || 'small'" type="primary"
                :disabled="item.disabled || false" @click="openDialog">{{ item.btnText }}
                <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
            </el-button>
        </el-input>
        <el-dialog title="选择角色" v-dialogDrag :visible.sync="dialogVisible" width="50%" append-to-body :center="false">
            <el-container class="contwarp">
                <el-main class="warpLeft">
                    <div class="card-header">
                        <el-input v-model="rulesRoleQueryParams.name" placeholder="请输入角色名进行搜索" style="width:200px"
                            clearable />
                        <el-button class="button" type="primary" style="margin-left:10px"
                            @click="rulesHandleQuery">搜索</el-button>
                    </div>

                    <!-- 表格 -->
                    <el-table :data="rulesRoleList" @selection-change="handleRoleChange" ref="rulesRoleTable"
                        row-key="id" highlight-current-row tooltip-effect="dark">
                        <el-table-column type="index" label="序号" align="center" width="60">
                        </el-table-column>
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column label="角色ID" align="left" prop="id"  show-overflow-tooltip />
                        <el-table-column label="启动状态" align="left" prop="enabled" />
                        <el-table-column label="角色编码" align="center" prop="roleCode" />
                        <el-table-column label="角色名称" align="center" prop="roleName" show-overflow-tooltip />

                        <el-table-column label="是否是业务角色" align="center">
                            <template #default="{ row }">
                                {{ row.isApplicationRole === 1 ? '是' : '否' }}
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-pagination v-if="rulesRoleTotal > 0" :total="rulesRoleTotal"
                        :current-page.sync="rulesRoleQueryParams.page" :page-size="rulesRoleQueryParams.rows"
                        @size-change="handleSizeChange" @current-change="handleCurrentChange" />

                </el-main>
                <el-aside width="320px" class="asideR">
                    <h5 class="fbold">已选角色</h5>
                    <div class="choose-department-checked">
                        <div class="choose-department-item" v-for="(citem, index) in choosedRoleRulesData"
                            :key="index">
                            <div class="choose-department-item-text ellipsis-line-1">
                                {{ citem[item.defaultProps.label || "roleName"] }}
                            </div>
                            <span class="choose-department-item-close" @click="delChoose(citem.id)">x</span>
                        </div>
                    </div>
                </el-aside>
            </el-container>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false" size="small">取消</el-button>
                <el-button type="primary" @click="handleConfirm" size="small">确定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import { findRole } from "@/api/senior";
export default {
    name: "role",
    props: {
        item: {
            type: Object,
            required: true
        },
        onOk: {
            type: Function
        },
    },
    data() {
        return {
            // 控制弹窗显隐
            dialogVisible: false,
            defaultProps: {
                children: "children",
                label: "orgName",
                isLeaf: 'leaf',
            },
            // 查询参数
            rulesRoleQueryParams: {
                page: 1,
                name: '',
                sid: '',
                rows: 10
            },
            // 右侧添加数据
            choosedRoleRulesData: [],
            // 所有数据
            rulesRoleList: [],
            // 所有数据条数
            rulesRoleTotal: 0,

        };
    },
    methods: {
        handleSizeChange(val) {
            this.rulesRoleQueryParams.size = val;
            this.rulesHandleQuery();
        },
        handleCurrentChange(val) {
            this.rulesRoleQueryParams.page = val;
            this.rulesHandleQuery();
        },
        // 勾选复选框添加
        handleRoleChange(val) {
            // console.log(val);
            // console.log(this.item.relevancy);
            // console.log(this.item);
            this.choosedRoleRulesData = val.map((item) => item)
            // console.log(' this.choosedRoleRulesData', this.choosedRoleRulesData);
        },
        // 渲染列表&&查询
        rulesHandleQuery() {
            // 群组列表
            findRole(this.rulesRoleQueryParams, this.rulesRoleQueryParams.page).then((res) => {
                // console.log('权限列表查询', res);
                this.rulesRoleList = res.data.content
                this.rulesRoleTotal = res.data.totalElements
            })
        },

        // 权限列表重置
        rulesResetQuery() {
            this.rulesRoleQueryParams.page = 1
            this.rulesRoleQueryParams.name = '';
            this.rulesRoleQueryParams.sid = '';
            rulesHandleQuery()
        },
        // 打开弹窗
        openDialog(e) {
            this.dialogVisible = true;
        },
        // 关闭弹窗保存数据
        handleConfirm() {
            this.dialogVisible = false;
            // console.log(this.choosedRoleRulesData);
            let parts = this.item.relevancy.split(','); // 分割成数组 ["payoffScale-name", "preScale-id"]  
            // 遍历数组，查找包含"name"的部分  
            let namePart = null;
            namePart = parts[0].split('-')[1];
            // console.log(namePart); // 输出："name"
            let showsArr = true
            if(this.choosedRoleRulesData.length>0){
                this.choosedRoleRulesData.forEach(element => {
                    if(!element[namePart]){
                        showsArr = false
                        return
                    }
                });
            }
            if(showsArr){
                this.$emit("chooseData", this.choosedRoleRulesData);
            }else{
                this.$emit("chooseData", []);
            }
            
        },
        // 删除右侧列表项
        delChoose(itemId) {
            // console.log(itemId);
            // console.log(this.choosedRoleRulesData);
            this.choosedRoleRulesData = this.choosedRoleRulesData.filter(item => item.id !== itemId);
        }
    },
    created() {
        this.rulesHandleQuery();
    }
};
</script>
<style scoped>
.icon {
    margin: 0;
}

.el-dialog__body {
    padding: 0px 20px 30px;
}

.el-main {
    padding: 0px;
    border-left: 0px solid #e0e0e0;
}

.asideR {
    border-left: 1px solid #e0e0e0;
    padding: 0px 15px 0px;
}

.chooseD a {
    display: block;
    padding: 5px 0;
}

.choose-department-checked {
    width: 100%;
    box-sizing: border-box;
    padding: 10px;
    height: 500px;
    overflow-y: auto;
    border: 1px solid #e6ebf5;
}

.choose-department-item {
    height: 30px;
    -js-display: flex;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.choose-department-item-text {
    width: 80%;
    user-select: none;
}

.choose-department-item-close {
    cursor: pointer;
    font-size: 16px;
}

.contwarp {
    height: 58vh;
    overflow: hidden;
}

.warpLeft {
    height: 100%;
    overflow: hidden;
}

.warpLeft ::v-deep .el-input__validateIcon {
    display: none;
}

.text-with-ellipsis {
    display: inline-block;
    max-width: 100%;
    /* 或者你想要的任何宽度 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>