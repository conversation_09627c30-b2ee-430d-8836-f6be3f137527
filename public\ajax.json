{"restlogin": {"errcode": 200, "timestamp": null, "status": 200, "error": "OK", "message": null, "path": null, "data": "bbbbbbbbbbb"}, "getCurrentUser": {"errcode": 0, "timestamp": "2019-07-09 18:33:00", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "1", "username": "hadmin", "truename": "超级管理员", "nickname": "超级管理员", "email": "<EMAIL>", "preferredMobile": "***********", "startTime": "2009-04-07 13:29:00", "endTime": "2018-02-05 01:01:01", "status": "0", "passwordModifiedDate": "2018-04-11 03:45:00", "displayOrder": 120000, "employeeNumber": null, "duty": null, "positionLevel": 2, "employeeTypeDictValue": 1, "employeeTypeDictDesc": null, "photo": null, "userType": 98, "openid": null, "enabled": true, "accountNonExpired": true, "accountNonLocked": true, "credentialsNonExpired": true, "currentBloc": "cmcc", "authBlocs": [{"id": "cmcc", "blocName": "河南移动", "blocDesc": null}], "currentCorp": "598905984894631936", "authCorps": [{"id": "598905984894631936", "blocId": "cmcc", "corpName": "省公司", "corpShortName": null, "corpDesc": null, "corpContent": null, "corpMail": null, "corpTel": null, "corpLegalPerson": null, "postcode": null, "address": null, "industry": null, "registerLocation": null, "belongArea": null, "webUrl": null, "linkMan": null, "linkManPhone": null, "linkManMail": null}], "authOrgs": [{"id": "2055", "orgCode": "2709049956525942918", "orgName": "管理信息系统支撑室", "parentOrgCode": "2700526267653981965", "displayOrder": 10000, "displayName": "省公司\\业务支撑中心\\管理信息系统支撑室", "belongCompanyCode": "4772338661636601428", "belongDepartmentCode": "2700526267653981965", "levelDictValue": "4", "reserve1": null, "reserve2": null, "reserve3": null, "reserve4": null, "reserve5": null, "styleDictValue": "04", "companyTypeDictValue": "01", "erpId": null, "corpId": "1", "isCorpRoot": false}], "authPositions": [{"id": "640", "positionName": "一般员工", "positionCompany": "D", "positionCode": 64, "positionType": null}], "authRoles": [{"id": "10", "roleCode": "ROLE_SUPER", "roleName": "超级管理员", "isApplicationRole": false, "displayOrder": 10, "authority": "ROLE_SUPER"}, {"id": "20", "roleCode": "ROLE_ADMIN", "roleName": "应用管理员", "isApplicationRole": false, "displayOrder": 20, "authority": "ROLE_ADMIN"}, {"id": "125", "roleCode": "ROLE_S_CJGLY_APPLY", "roleName": "省公司廉政视窗超级管理员人员", "isApplicationRole": true, "displayOrder": 125, "authority": "ROLE_S_CJGLY_APPLY"}, {"id": "40", "roleCode": "ROLE_USER", "roleName": "普通用户", "isApplicationRole": false, "displayOrder": 40, "authority": "ROLE_USER"}], "authPermissions": [{"id": "10", "permissionCode": "draft:module", "description": "会议审批起草", "url": "html/apply/meetingForm.html", "icon": null, "menuLevel": 2, "displayOrder": 2, "type": "功能模块", "parentId": "9", "remark": "会议管理起草", "authority": "draft:module"}, {"id": "11", "permissionCode": "mywork:module", "description": "我的工作", "url": null, "icon": null, "menuLevel": 2, "displayOrder": 1, "type": "功能模块", "parentId": "9", "remark": "我的工作", "authority": "mywork:module"}, {"id": "27", "permissionCode": "mywork:<PERSON><PERSON><PERSON>", "description": "我的已办", "url": "html/process/processJoin.html", "icon": null, "menuLevel": 3, "displayOrder": 2, "type": "访问路径", "parentId": "11", "remark": "我的已办", "authority": "mywork:<PERSON><PERSON><PERSON>"}, {"id": "26", "permissionCode": "mywork:todo", "description": "我的待办", "url": "html/process/processTask.html", "icon": null, "menuLevel": 3, "displayOrder": 1, "type": "访问路径", "parentId": "11", "remark": "我的待办", "authority": "mywork:todo"}], "authorities": [{"authority": "mywork:module"}, {"authority": "draft:module"}, {"authority": "mywork:<PERSON><PERSON><PERSON>"}, {"authority": "mywork:todo"}, {"authority": "ROLE_S_CJGLY_APPLY"}, {"authority": "ROLE_SUPER"}, {"authority": "ROLE_USER"}, {"authority": "ROLE_ADMIN"}], "authUserOrgs": [{"id": "1", "orgCode": "2709049956525942918", "username": "hadmin", "positionId": "640", "displayOrder": null, "status": "0"}], "belongCompanyCode": "4772338661636601428", "belongCompanyName": "省公司", "belongCompanyTypeDictValue": "01", "belongCompanyTypeDictDesc": null, "belongDepartmentCode": "2700526267653981965", "belongDepartmentName": "业务支撑中心", "belongOrgCode": "2709049956525942918", "belongOrgName": "管理信息系统支撑室", "reserve1": null, "reserve2": null, "reserve3": null, "reserve4": null, "reserve5": null}}, "tms": {"featureLib": {"findAll": {"errcode": 0, "timestamp": "2019-07-10 10:34:05", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"createdTime": "2019-05-08 18:41:52", "modifiedTime": "2019-05-08 18:41:52", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL575754245156896768", "username": "songting2", "truename": "宋婷", "featureType": 3, "featureCode": "D1311701", "startTime": "2019-05-08 18:41:48", "endTime": "2020-05-30 12:00:00", "status": 1, "photoId": null}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-05-24 15:53:17", "modifiedTime": "2019-05-24 15:53:23", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL581510039513071616", "username": "liubangxu", "truename": "刘邦旭", "featureType": 1, "featureCode": "00000007", "startTime": "2019-05-24 15:53:17", "endTime": "2019-05-24 03:56:46", "status": 1, "photoId": "V581510036610613248"}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-05-24 16:47:50", "modifiedTime": "2019-05-24 16:47:50", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL581523768640274432", "username": "wuh<PERSON><PERSON>", "truename": "武宏仁", "featureType": 3, "featureCode": "01E1D700", "startTime": "2019-05-24 16:47:50", "endTime": "2020-05-22 12:00:00", "status": 1, "photoId": null}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-05-24 18:03:55", "modifiedTime": "2019-05-24 18:04:00", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL581542915617062912", "username": "userType_5_ceshi４", "truename": "测试４", "featureType": 1, "featureCode": "00000014", "startTime": "2019-05-24 18:03:55", "endTime": "2019-05-24 06:07:24", "status": 1, "photoId": "V581542913708654592"}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-05-24 18:04:00", "modifiedTime": "2019-05-24 18:04:00", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL581542934273327104", "username": "userType_5_ceshi４", "truename": "测试４", "featureType": 4, "featureCode": "aaaaaaaa", "startTime": "2019-05-24 18:04:00", "endTime": "2019-05-24 06:07:24", "status": 1, "photoId": "V581542913708654592"}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-05-28 22:11:21", "modifiedTime": "2019-05-28 22:11:21", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL583054735737618432", "username": "<PERSON><PERSON><PERSON><PERSON>", "truename": "赵翔", "featureType": 3, "featureCode": "B1C87801", "startTime": "2019-05-28 22:11:21", "endTime": "2020-05-02 12:00:00", "status": 1, "photoId": null}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-06-05 11:27:09", "modifiedTime": "2019-06-05 11:27:09", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL585791719535017984", "username": "userType_7_zhangsan1", "truename": "张三", "featureType": 3, "featureCode": "61FFC402", "startTime": "2019-06-05 11:27:09", "endTime": "2022-06-17 12:00:00", "status": 1, "photoId": null}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-06-18 10:16:29", "modifiedTime": "2019-06-18 10:16:29", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL590484977880137728", "username": "chenweifeng", "truename": "陈伟锋", "featureType": 4, "featureCode": "D69A05D9", "startTime": "2019-06-18 10:16:29", "endTime": "2020-06-05 12:00:00", "status": 1, "photoId": null}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-06-20 10:56:32", "modifiedTime": "2019-06-20 10:56:32", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL591219831143923712", "username": "yang<PERSON><PERSON><PERSON><PERSON>", "truename": "杨冠强", "featureType": 4, "featureCode": "qwersdfg", "startTime": "2019-06-20 10:56:32", "endTime": "2019-06-29 12:00:00", "status": 1, "photoId": null}, {"orderByClause": null, "ssDate": null, "eeDate": null, "pageIndex": null, "pagesize": null, "createdTime": "2019-06-24 18:04:48", "modifiedTime": "2019-06-24 18:04:48", "enabled": true, "removedTime": null, "creator": "hadmin", "modifier": "hadmin", "id": "TFL592777160310128640", "username": "hanhailing", "truename": "韩海玲", "featureType": 3, "featureCode": "00001111", "startTime": "2019-06-24 18:04:48", "endTime": "2019-06-25 12:00:00", "status": 1, "photoId": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}}}, "common": {"grade": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "11", "gradeName": "高一", "gradeNum": 12, "studentNum": 1752, "stayNum": 752, "dayNum": 1000, "gradeMaster": "刘国平", "masterPhone": "***********", "masterAccount": "liugu<PERSON>", "parentId": null, "isDefault": false, "flag": null}, {"id": "12", "gradeName": "高二", "gradeNum": 21, "studentNum": 1563, "stayNum": 563, "dayNum": 1000, "gradeMaster": "耿晓楠", "masterPhone": "***********", "parentId": null, "isDefault": false, "flag": null}, {"id": "13", "gradeName": "高三", "gradeNum": 24, "studentNum": 1896, "stayNum": 296, "dayNum": 1200, "gradeMaster": "段彭伟", "masterPhone": "***********", "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}}, "class": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "14", "className": "18级高一1班", "studentNum": 1752, "stayNum": 752, "dayNum": 1000, "classMaster": "王树芳", "classMasterPhone": "***********", "parentId": null, "isDefault": false, "flag": null}, {"id": "15", "className": "18级高一2班", "studentNum": 1563, "stayNum": 563, "dayNum": 1000, "classMaster": "耿晓楠", "classMasterPhone": "***********", "parentId": null, "isDefault": false, "flag": null}, {"id": "16", "className": "18级高一3班", "studentNum": 1896, "stayNum": 296, "dayNum": 1200, "classMaster": "段彭伟", "classMasterPhone": "***********", "parentId": null, "isDefault": false, "flag": null}, {"id": "17", "className": "18级高一4班", "studentNum": 1752, "stayNum": 752, "dayNum": 1000, "classMaster": "王凉凉", "classMasterPhone": "12837404814", "parentId": null, "isDefault": false, "flag": null}, {"id": "18", "className": "18级高一5班", "studentNum": 1563, "stayNum": 563, "dayNum": 1000, "classMaster": "张梅梅", "classMasterPhone": "18768945236", "parentId": null, "isDefault": false, "flag": null}, {"id": "19", "className": "18级高一6班", "studentNum": 1896, "stayNum": 296, "dayNum": 1200, "classMaster": "马威威", "classMasterPhone": "15938952656", "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findAllNoPage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "14", "className": "18级高一1班", "studentNum": 1752, "stayNum": 752, "dayNum": 1000, "classMaster": "王树芳", "classMasterPhone": "***********", "parentId": "2", "isDefault": false, "flag": null}, {"id": "15", "className": "18级高一2班", "studentNum": 1563, "stayNum": 563, "dayNum": 1000, "classMaster": "耿晓楠", "classMasterPhone": "***********", "parentId": "2", "isDefault": false, "flag": null}, {"id": "16", "className": "18级高一3班", "studentNum": 1896, "stayNum": 296, "dayNum": 1200, "classMaster": "段彭伟", "classMasterPhone": "***********", "parentId": "2", "isDefault": false, "flag": null}, {"id": "17", "className": "18级高一4班", "studentNum": 1752, "stayNum": 752, "dayNum": 1000, "classMaster": "王凉凉", "classMasterPhone": "12837404814", "parentId": "2", "isDefault": false, "flag": null}, {"id": "18", "className": "18级高一5班", "studentNum": 1563, "stayNum": 563, "dayNum": 1000, "classMaster": "张梅梅", "classMasterPhone": "18768945236", "parentId": "2", "isDefault": false, "flag": null}, {"id": "19", "className": "18级高一6班", "studentNum": 1896, "stayNum": 296, "dayNum": 1200, "classMaster": "马威威", "classMasterPhone": "15938952656", "parentId": "2", "isDefault": false, "flag": null}]}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "15", "className": "18级高一2班", "studentNum": 1563, "stayNum": 563, "dayNum": 1000, "classMaster": "耿晓楠", "classMasterPhone": "***********", "parentId": null, "isDefault": false, "flag": null}}}, "orgtech": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "20", "orgId": "11", "truename": "李晓晓", "position": 1, "gender": 2, "preferredMobile": "***********", "isMaster": true, "duty": 1, "parentId": null, "isDefault": false, "flag": null}, {"id": "21", "orgId": "12", "truename": "王菲菲", "position": 1, "gender": 2, "preferredMobile": "***********", "isMaster": false, "duty": 1, "parentId": null, "isDefault": false, "flag": null}, {"id": "22", "orgId": "13", "truename": "张一一", "position": 2, "gender": 1, "preferredMobile": "***********", "isMaster": false, "duty": 2, "parentId": null, "isDefault": false, "flag": null}, {"id": "23", "orgId": "11", "truename": "马田", "position": 3, "gender": 3, "preferredMobile": "***********", "isMaster": false, "duty": 3, "parentId": null, "isDefault": false, "flag": null}, {"id": "24", "orgId": "12", "truename": "田丰", "position": 1, "gender": 1, "preferredMobile": "***********", "isMaster": true, "duty": 1, "parentId": null, "isDefault": false, "flag": null}, {"id": "25", "orgId": "13", "truename": "刘鹤", "position": 1, "gender": 2, "preferredMobile": "***********", "isMaster": true, "duty": 1, "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findAllNoPage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "01", "name": "襄城一高", "parentId": null}, {"id": "02", "name": "教务", "parentId": "01"}, {"id": "03", "name": "后勤", "parentId": "01"}, {"id": "04", "name": "管理", "parentId": "01"}, {"id": "20", "orgId": "11", "name": "李晓晓", "position": 1, "gender": 2, "preferredMobile": "***********", "isMaster": true, "duty": 1, "parentId": "02", "isDefault": false, "flag": null}, {"id": "21", "orgId": "12", "name": "王菲菲", "position": 1, "gender": 2, "preferredMobile": "***********", "isMaster": false, "duty": 1, "parentId": "03", "isDefault": false, "flag": null}, {"id": "22", "orgId": "13", "name": "张一一", "position": 2, "gender": 1, "preferredMobile": "***********", "isMaster": false, "duty": 2, "parentId": "04", "isDefault": false, "flag": null}, {"id": "23", "orgId": "11", "name": "马田", "position": 3, "gender": 3, "preferredMobile": "***********", "isMaster": false, "duty": 3, "parentId": "02", "isDefault": false, "flag": null}, {"id": "24", "orgId": "12", "name": "田丰", "position": 1, "gender": 1, "preferredMobile": "***********", "isMaster": true, "duty": 1, "parentId": "03", "isDefault": false, "flag": null}, {"id": "25", "orgId": "13", "name": "刘鹤", "position": 1, "gender": 2, "preferredMobile": "***********", "isMaster": true, "duty": 1, "parentId": "04", "isDefault": false, "flag": null}]}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "21", "orgId": "12", "truename": "王菲菲", "position": 1, "gender": 2, "preferredMobile": "***********", "isMaster": false, "duty": 1, "parentId": null, "isDefault": false, "flag": null}}, "deleteById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "21", "orgId": "12", "truename": "王菲菲", "position": 1, "gender": 2, "preferredMobile": "***********", "isMaster": false, "duty": 1, "parentId": null, "isDefault": false, "flag": null}}}, "stuextend": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "20", "gradeId": "11", "ClassId": "14", "studyName": "刘亮", "gender": 2, "studyNo": "***********", "eduNo": "***********", "isStay": true, "joinDate": "2019-8-31", "status": 1, "guardian": "刘妈妈", "guardianPhone": "15263698522", "parentId": null, "isDefault": false, "flag": null}, {"id": "21", "gradeId": "12", "ClassId": "15", "studyName": "刘亮", "gender": 2, "studyNo": "***********", "eduNo": "***********", "isStay": false, "joinDate": "2019-8-31", "status": 1, "guardian": "刘爷爷", "guardianPhone": "15263698522", "parentId": null, "isDefault": false, "flag": null}, {"id": "22", "gradeId": "13", "ClassId": "16", "studyName": "刘亮", "gender": 1, "studyNo": "***********", "eduNo": "***********", "isStay": false, "joinDate": "2019-8-31", "status": 2, "guardian": "刘爸爸", "guardianPhone": "15263698522", "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findAllNoPage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "01", "name": "襄城一高", "parentId": null}, {"id": "02", "name": "高一", "parentId": "01"}, {"id": "03", "name": "高二", "parentId": "01"}, {"id": "04", "name": "高三", "parentId": "01"}, {"id": "20", "gradeId": "11", "name": "18级高一1班", "studyName": "刘亮", "gender": 2, "studyNo": "***********", "eduNo": "***********", "isStay": true, "joinDate": "2019-8-31", "status": 1, "guardian": "刘妈妈", "guardianPhone": "15263698522", "parentId": "02", "isDefault": false, "flag": null}, {"id": "21", "gradeId": "12", "name": "18级高二1班", "studyName": "刘亮", "gender": 2, "studyNo": "***********", "eduNo": "***********", "isStay": false, "joinDate": "2019-8-31", "status": 1, "guardian": "刘爷爷", "guardianPhone": "15263698522", "parentId": "03", "isDefault": false, "flag": null}, {"id": "22", "gradeId": "13", "name": "18级高三1班", "studyName": "刘亮", "gender": 1, "studyNo": "***********", "eduNo": "***********", "isStay": false, "joinDate": "2019-8-31", "status": 2, "guardian": "刘爸爸", "guardianPhone": "15263698522", "parentId": "04", "isDefault": false, "flag": null}]}, "findStuClassListNopage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "1", "name": "王一", "classId": "10", "className": "17级高二1班"}, {"id": "2", "name": "王一二", "classId": "10", "className": "17级高二1班"}, {"id": "3", "name": "王二", "classId": "12", "className": "18级高一1班"}, {"id": "4", "name": "王三", "classId": "11", "className": "18级高一2班"}]}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "20", "gradeId": "11", "ClassId": "14", "studyName": "刘亮", "gender": 2, "studyNo": "***********", "eduNo": "***********", "isStay": true, "joinDate": "2019-8-31", "status": 1, "guardian": "刘妈妈", "guardianPhone": "15263698522", "parentId": null, "isDefault": false, "flag": null}}}, "org": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "11", "orgName": "后勤", "parentOrgId": "", "isSchooldRoot": false}, {"id": "12", "orgName": "教务", "parentOrgId": "", "isSchooldRoot": false}, {"id": "13", "orgName": "管理", "parentOrgId": "", "isSchooldRoot": false}]}}, "hour": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "11", "name": "第一节", "classTime": 1, "attendClassTime": "8:00", "finishClassTime": "8:50", "enabled": true}, {"id": "12", "name": "第二节", "classTime": 2, "attendClassTime": "16:00", "finishClassTime": "16:50", "enabled": true}, {"id": "13", "name": "第三节", "classTime": 1, "attendClassTime": "10:00", "finishClassTime": "10:50", "enabled": false}]}}, "user": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"truename": "刘国平", "username": "liugu<PERSON>", "preferredMobile": "***********", "classIds": "10,11"}, {"truename": "钟汉良", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "preferredMobile": "***********", "classIds": "10"}, {"truename": "王带伞", "username": "wa<PERSON><PERSON><PERSON>", "preferredMobile": "***********", "classIds": "11"}, {"truename": "刘昂", "username": "liu<PERSON>", "preferredMobile": "***********", "classIds": "12"}, {"truename": "林黛玉", "username": "lindaiyu", "preferredMobile": "***********", "classIds": "12,11"}, {"truename": "王熙凤", "username": "<PERSON><PERSON><PERSON><PERSON>", "preferredMobile": "***********", "classIds": "10,12"}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}}}, "leave": {"info": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "14", "gradeName": "高一", "className": "18级高一1班", "studentAccount": "张天择", "applyType": 1, "isPermitLeave": true, "requestStartLeave": "2019-04-11 12:30:40", "requestEndLeave": "2019-04-21 14:30:40", "requestTimeDuration": 10, "leaveStatus": 1, "cancelStatus": 1, "actualTimeDuration": 10, "applyUserType": 1, "applyUser": "田丰", "applyUserPhone": "***********", "modifyTimes": 3, "cancelType": 1, "requestReason": "事假事假事假事假事假事假事假事假事假事假事假事假事假事假", "actualStartLeave": "2019-04-11 13:30:40", "startLeaveImage": "", "actualEndLeave": "2019-04-21 13:30:40", "endLeaveImage": "", "oprationType": 1, "oprationContent": "事假事假", "oprationAccountName": "马田天", "oprationTime": "2019-04-11 12:00:40", "parentId": null, "isDefault": false, "flag": null}, {"id": "15", "gradeName": "高二", "className": "17级高二1班", "studentAccount": "张天择", "applyType": 1, "isPermitLeave": true, "requestStartLeave": "2019-04-11 12:30:40", "requestEndLeave": "2019-04-21 14:30:40", "requestTimeDuration": 10, "leaveStatus": 1, "cancelStatus": 1, "actualTimeDuration": 10, "applyUserType": 1, "applyUser": "田丰", "applyUserPhone": "***********", "modifyTimes": 3, "cancelType": 1, "requestReason": "事假事假事假事假事假事假事假事假事假事假事假事假事假事假", "actualStartLeave": "2019-04-11 13:30:40", "startLeaveImage": "", "actualEndLeave": "2019-04-21 13:30:40", "endLeaveImage": "", "oprationType": 1, "oprationContent": "事假事假", "oprationAccountName": "马田天", "oprationTime": "2019-04-11 12:00:40", "parentId": null, "isDefault": false, "flag": null}, {"id": "16", "gradeName": "高三", "className": "16级高一1班", "studentAccount": "张天择", "applyType": 1, "isPermitLeave": true, "requestStartLeave": "2019-04-11 12:30:40", "requestEndLeave": "2019-04-21 14:30:40", "requestTimeDuration": 10, "leaveStatus": 1, "cancelStatus": 1, "actualTimeDuration": 10, "applyUserType": 1, "applyUser": "田丰", "applyUserPhone": "***********", "modifyTimes": 3, "cancelType": 1, "requestReason": "事假事假事假事假事假事假事假事假事假事假事假事假事假事假", "actualStartLeave": "2019-04-11 13:30:40", "startLeaveImage": "", "actualEndLeave": "2019-04-21 13:30:40", "endLeaveImage": "", "oprationType": 1, "oprationContent": "事假事假", "oprationAccountName": "马田天", "oprationTime": "2019-04-11 12:00:40", "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "14", "gradeName": "高一", "className": "18级高一1班", "studentAccount": "张天择", "applyType": 1, "isPermitLeave": true, "requestStartLeave": "2019-04-11 12:30:40", "requestEndLeave": "2019-04-21 14:30:40", "requestTimeDuration": 10, "leaveStatus": 1, "cancelStatus": 1, "actualTimeDuration": 10, "applyUserType": 1, "applyUser": "田丰", "applyUserPhone": "***********", "modifyTimes": 3, "cancelType": 1, "requestReason": "事假事假事假事假事假事假事假事假事假事假事假事假事假事假", "actualStartLeave": "2019-04-11 13:30:40", "startLeaveImage": "", "actualEndLeave": "2019-04-21 13:30:40", "endLeaveImage": "", "oprationType": 1, "oprationContent": "事假事假", "oprationAccountName": "马田天", "oprationTime": "2019-04-11 12:00:40", "parentId": null, "isDefault": false, "flag": null}}}}, "political": {"project": {"findAllNoPage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "1", "name": "襄城一高", "parentId": null}, {"id": "14", "parentId": 1, "name": "量化60条"}, {"id": "15", "parentId": 1, "name": "寝室量化管理"}, {"id": "16", "parentId": 1, "name": "奖励项目"}]}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}}, "lhzb": {"findAllNoPage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "14", "totalExecutedNum": 15000, "currentYearExecuteNum": 15000, "title": "第一条", "content": "寝室熄灯前大声喧哗", "isPlus": true, "scoreType": 1, "minScore": "5", "maxScore": "10", "classScoreRate": 50, "enabled": true, "parentId": null, "isDefault": false, "flag": null}, {"id": "15", "totalExecutedNum": 16000, "currentYearExecuteNum": 15000, "title": "第二条", "content": "不可夜游，不可喧哗，不可疾行", "isPlus": false, "scoreType": 2, "minScore": "5", "maxScore": "10", "classScoreRate": 50, "enabled": false, "parentId": null, "isDefault": false, "flag": null}, {"id": "16", "totalExecutedNum": 10000, "currentYearExecuteNum": 10000, "title": "第三条", "content": "不可私自斗殴", "isPlus": false, "scoreType": 1, "minScore": "5", "maxScore": "10", "classScoreRate": 50, "enabled": true, "parentId": null, "isDefault": false, "flag": null}]}, "findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "14", "totalExecutedNum": 15000, "currentYearExecuteNum": 15000, "title": "第一条", "content": "条目内容条目内容条目内容", "isPlus": true, "scoreType": 1, "minScore": "5", "maxScore": "10", "classScoreRate": 50, "enabled": true, "parentId": null, "isDefault": false, "flag": null}, {"id": "15", "totalExecutedNum": 16000, "currentYearExecuteNum": 15000, "title": "第二条", "content": "条目内容条目内容条目内容", "isPlus": false, "scoreType": 2, "minScore": "5", "maxScore": "10", "classScoreRate": 50, "enabled": false, "parentId": null, "isDefault": false, "flag": null}, {"id": "16", "totalExecutedNum": 10000, "currentYearExecuteNum": 10000, "title": "第三条", "content": "条目内容条目内容条目内容", "isPlus": false, "scoreType": 1, "minScore": "5", "maxScore": "10", "classScoreRate": 50, "enabled": true, "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "14", "totalExecutedNum": 15000, "currentYearExecuteNum": 15000, "title": "第一条", "content": "条目内容条目内容条目内容", "isPlus": true, "scoreType": 1, "minScore": "5", "maxScore": "10", "classScoreRate": 50, "enabled": true, "parentId": null, "isDefault": false, "flag": null}}, "deleteById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "14", "totalExecutedNum": 15000, "currentYearExecuteNum": 15000, "title": "第一条", "content": "条目内容条目内容条目内容", "isPlus": true, "scoreType": 1, "minScore": "5", "maxScore": "10", "classScoreRate": 50, "enabled": true, "parentId": null, "isDefault": false, "flag": null}}}, "stulh": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "14", "gradeName": "高一", "className": "18级高一1班", "studentName": "张天", "politicalTime": "2019-10-01", "hourId": "1", "hourName": "第一节", "projectName": "量化60条", "ruleTitle": "第二条", "ruleContent": "大声说话喧哗", "isPlus": false, "executedScore": 5, "executedMan": "张三", "executeManType": 1, "executeRecorder": "林浩", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}, {"id": "15", "gradeName": "高一", "className": "18级高一1班", "studentName": "王五", "politicalTime": "2019-10-01", "hourId": "2", "hourName": "第二节", "projectName": "量化60条", "ruleTitle": "第三条", "ruleContent": "大声说话喧哗不睡觉", "isPlus": false, "executedScore": 5, "executedMan": "张三", "executeManType": 2, "executeRecorder": "林浩", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}, {"id": "16", "gradeName": "高一", "className": "18级高一1班", "studentName": "李斯", "politicalTime": "2019-10-01", "hourId": "1", "hourName": "第一节", "projectName": "量化60条", "ruleTitle": "第二条", "ruleContent": "大声说话喧哗", "isPlus": false, "executedScore": 5, "executedMan": "方舟", "executeManType": 1, "executeRecorder": "林达", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "14", "gradeName": "高一", "className": "18级高一1班", "studentName": "张天", "politicalTime": "2019-10-01", "hourName": "第一节", "projectName": "量化60条", "ruleTitle": "第二条", "ruleContent": "大声说话喧哗", "isPlus": false, "executedScore": 5, "executedMan": "张三", "executeManType": 1, "executeRecorder": "林浩", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}}, "deleteById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "14", "gradeName": "高一", "className": "18级高一1班", "studentName": "张天", "politicalTime": "2019-10-01", "hourName": "第一节", "projectName": "量化60条", "ruleTitle": "第二条", "ruleContent": "大声说话喧哗", "isPlus": false, "executedScore": 5, "executedMan": "张三", "executeManType": 1, "executeRecorder": "林浩", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}}}, "classlh": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "14", "gradeName": "高一", "className": "18级高一1班", "politicalTime": "2019-10-01", "hourName": "第一节", "projectName": "量化60条", "ruleTitle": "第二条", "ruleContent": "大声说话喧哗", "isPlus": false, "executedScore": 5, "executedMan": "张三", "executeType": 1, "executeRecorder": "林浩", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}, {"id": "15", "gradeName": "高一", "className": "18级高一1班", "politicalTime": "2019-10-01", "hourName": "第二节", "projectName": "量化60条", "ruleTitle": "第三条", "ruleContent": "大声说话喧哗不睡觉", "isPlus": false, "executedScore": 5, "executedMan": "张三", "executeType": 2, "executeRecorder": "林浩", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}, {"id": "14", "gradeName": "高一", "className": "18级高一1班", "politicalTime": "2019-10-01", "hourName": "第一节", "projectName": "量化60条", "ruleTitle": "第二条", "ruleContent": "大声说话喧哗", "isPlus": false, "executedScore": 5, "executedMan": "方舟", "executeType": 1, "executeRecorder": "林达", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "14", "gradeName": "高一", "className": "18级高一1班", "studentName": "张天", "politicalTime": "2019-10-01", "hourName": "第一节", "projectName": "量化60条", "ruleTitle": "第二条", "ruleContent": "大声说话喧哗", "isPlus": false, "executedScore": 5, "executedMan": "张三", "executeManType": 1, "executeRecorder": "林浩", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}}, "deleteById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "14", "gradeName": "高一", "className": "18级高一1班", "studentName": "张天", "politicalTime": "2019-10-01", "hourName": "第一节", "projectName": "量化60条", "ruleTitle": "第二条", "ruleContent": "大声说话喧哗", "isPlus": false, "executedScore": 5, "executedMan": "张三", "executeManType": 1, "executeRecorder": "林浩", "executeRecordTime": "2019-10-02", "parentId": null, "isDefault": false, "flag": null}}}}, "jwm": {"xk": {"findAllNoPage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "1", "subjectCode": "001", "subjectName": "语文"}, {"id": "2", "subjectCode": "002", "subjectName": "数学"}, {"id": "3", "subjectCode": "003", "subjectName": "英语"}, {"id": "4", "subjectCode": "004", "subjectName": "物理"}, {"id": "5", "subjectCode": "005", "subjectName": "化学"}, {"id": "6", "subjectCode": "006", "subjectName": "生物"}]}}, "gradeSubject": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "14", "subjectName": "语文", "teacherNum": 4, "parentId": null, "isDefault": false, "flag": null}, {"id": "15", "subjectName": "数学", "teacherNum": 3, "parentId": null, "isDefault": false, "flag": null}, {"id": "16", "subjectName": "英语", "teacherNum": 2, "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "14", "subjectName": "语文", "teacherNum": 4, "parentId": null, "isDefault": false, "flag": null}}, "findAllNoPage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "1", "name": "襄城一高", "parentId": null}, {"id": "2", "name": "高一", "parentId": "1"}, {"id": "3", "name": "一部", "parentId": "2"}]}}, "xkjs": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "14", "gradeId": "11", "subjectId": "1", "truename": "刘国平", "preferredMobile": "***********", "teacherClassDesc": "18级高一1班、18级高一1班、18级高一1班、18级高一1班", "parentId": null, "isDefault": false, "flag": null}, {"id": "15", "gradeId": "12", "subjectId": "2", "truename": "刘昂", "preferredMobile": "***********", "teacherClassDesc": "18级高一1班、19级高一1班", "parentId": null, "isDefault": false, "flag": null}, {"id": "16", "gradeId": "13", "subjectId": "2", "truename": "宋婷", "preferredMobile": "***********", "teacherClassDesc": "19级高一1班", "parentId": null, "isDefault": false, "flag": null}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "15", "gradeId": "12", "subjectId": "2", "truename": "刘国平", "preferredMobile": "***********", "teacherClassDesc": "18级高一1班、18级高一2班", "classId": ["14", "15"], "parentId": null, "isDefault": false, "flag": null}}, "deleteById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "15", "gradeId": "12", "subjectId": "2", "truename": "刘国平", "preferredMobile": "***********", "teacherClassDesc": "18级高一1班、19级高一1班", "parentId": null, "isDefault": false, "flag": null}}}}, "sys": {"dictValue": {"findDictValue": {"scoreType": {"errcode": 0, "timestamp": "2019-07-09 18:46:03", "status": 200, "error": null, "message": null, "path": null, "data": [{"value": 1, "name": "固定分值", "valueType": "int"}, {"value": 2, "name": "酌情分值", "valueType": "int"}]}, "classTime": {"errcode": 0, "timestamp": "2019-07-09 18:46:03", "status": 200, "error": null, "message": null, "path": null, "data": [{"value": 1, "name": "上午", "valueType": "int"}, {"value": 2, "name": "下午", "valueType": "int"}]}, "executeManType": {"errcode": 0, "timestamp": "2019-07-09 18:46:03", "status": 200, "error": null, "message": null, "path": null, "data": [{"value": 1, "name": "本班", "valueType": "int"}, {"value": 2, "name": "他班", "valueType": "int"}]}}}, "lesson": {"findAll": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"content": [{"id": "1", "name": "第一节", "classTime": 1, "enabled": true, "attendClassTime": "08:30:00", "finishClassTime": "09:10:00"}, {"id": "2", "name": "第二节", "classTime": 1, "enabled": true, "attendClassTime": "09:20:00", "finishClassTime": "10:00:00"}, {"id": "3", "name": "第八节", "classTime": 2, "enabled": false, "attendClassTime": "15:10:00", "finishClassTime": "15:50:00"}], "pageable": {"sort": {"sorted": false, "unsorted": true}, "pageSize": 10, "pageNumber": 0, "offset": 0, "unpaged": false, "paged": true}, "totalElements": 10, "totalPages": 1, "last": true, "first": true, "sort": {"sorted": false, "unsorted": true}, "size": 10, "number": 0, "numberOfElements": 10}}, "findAllNoPage": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "1", "name": "第一节", "classTime": 1, "isPermitLenabledeave": true, "attendClassTime": "08:30:00", "finishClassTime": "09:10:00"}, {"id": "2", "name": "第二节", "classTime": 1, "enabled": true, "attendClassTime": "09:20:00", "finishClassTime": "10:00:00"}, {"id": "3", "name": "第三节", "classTime": 1, "enabled": true, "attendClassTime": "10:10:00", "finishClassTime": "10:50:00"}]}, "update": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "deleteById": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "deleteAllByIds": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "updateEnable": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "create": {"errcode": 0, "timestamp": null, "status": 200, "error": "OK", "message": "操作成功", "path": null, "data": "bbbbbbbbbbb"}, "findById": {"errcode": 0, "timestamp": "2019-07-08 11:12:16", "status": 200, "error": null, "message": null, "path": null, "data": {"id": "1", "name": "第一节", "classTime": 1, "enabled": true, "attendClassTime": "08:30:00", "finishClassTime": "09:10:00"}}}, "userinfo": {"findPermissionByAppUser": {"errcode": 0, "timestamp": "2019-07-09 18:46:03", "status": 200, "error": null, "message": null, "path": null, "data": [{"id": "6004", "permissionCode": "home:module", "description": "首页", "url": "/home", "icon": "home", "menuLevel": 2, "displayOrder": 1, "type": "功能模块", "parentId": "6000", "remark": "首页", "authority": "home:module"}, {"id": "6001", "permissionCode": "tms:module", "description": "账户管理", "url": "/tms", "icon": "caidan1", "menuLevel": 2, "displayOrder": 2, "type": "功能模块", "parentId": "6000", "remark": "账户管理", "authority": "tms:module"}, {"id": "6002", "permissionCode": "tms:featureLib:menu", "description": "特征库管理", "url": "/tms/featureLib", "icon": "<PERSON><PERSON><PERSON><PERSON>", "menuLevel": 3, "displayOrder": 3, "type": "访问路径", "parentId": "6001", "remark": "特征库管理", "authority": "tms:featureLib:menu"}, {"id": "6005", "permissionCode": "base:module", "description": "基础信息", "url": "/base", "icon": "home", "menuLevel": 2, "displayOrder": 4, "type": "功能模块", "parentId": "6000", "remark": "基础信息", "authority": "base:module"}, {"id": "6006", "permissionCode": "base:grade:menu", "description": "年级信息", "url": "/base/grade", "icon": "home", "menuLevel": 3, "displayOrder": 5, "type": "访问路径", "parentId": "6005", "remark": "年级信息", "authority": "base:grade:menu"}, {"id": "6007", "permissionCode": "base:class:menu", "description": "班级信息", "url": "/base/class", "icon": "home", "menuLevel": 3, "displayOrder": 6, "type": "访问路径", "parentId": "6005", "remark": "班级信息", "authority": "base:class:menu"}, {"id": "6008", "permissionCode": "base:teach:menu", "description": "教职工信息", "url": "/base/teach", "icon": "home", "menuLevel": 3, "displayOrder": 7, "type": "访问路径", "parentId": "6005", "remark": "教职工信息", "authority": "base:teach:menu"}, {"id": "6009", "permissionCode": "base:stu:menu", "description": "学生信息", "url": "/base/stu", "icon": "home", "menuLevel": 3, "displayOrder": 8, "type": "访问路径", "parentId": "6005", "remark": "学生信息", "authority": "base:stu:menu"}, {"id": "6010", "permissionCode": "leave:module", "description": "请假管理", "url": "/leave", "icon": "home", "menuLevel": 2, "displayOrder": 9, "type": "功能模块", "parentId": "6000", "remark": "请假管理", "authority": "leave:module"}, {"id": "6011", "permissionCode": "leave:leaveManage:menu", "description": "请假管理", "url": "/leave/leaveManage", "icon": "home", "menuLevel": 3, "displayOrder": 10, "type": "访问路径", "parentId": "6010", "remark": "请假管理", "authority": "leave:leaveManage:menu"}, {"id": "6012", "permissionCode": "zjm:module", "description": "政教管理", "url": "/zjm", "icon": "home", "menuLevel": 2, "displayOrder": 11, "type": "功能模块", "parentId": "6000", "remark": "政教管理", "authority": "zjm:module"}, {"id": "6013", "permissionCode": "zjm:lhzb:menu", "description": "量化指标", "url": "/zjm/lhzb", "icon": "home", "menuLevel": 3, "displayOrder": 12, "type": "访问路径", "parentId": "6012", "remark": "量化指标", "authority": "zjm:lhzb:menu"}, {"id": "6014", "permissionCode": "zjm:stulh:menu", "description": "学生量化管理", "url": "/zjm/stulh", "icon": "home", "menuLevel": 3, "displayOrder": 13, "type": "访问路径", "parentId": "6012", "remark": "学生量化管理", "authority": "zjm:stulh:menu"}, {"id": "6015", "permissionCode": "zjm:classlh:menu", "description": "班级量化管理", "url": "/zjm/classlh", "icon": "home", "menuLevel": 3, "displayOrder": 14, "type": "访问路径", "parentId": "6012", "remark": "班级量化管理", "authority": "zjm:classlh:menu"}, {"id": "6016", "permissionCode": "jwm:module", "description": "教务管理", "url": "/jwm", "icon": "home", "menuLevel": 2, "displayOrder": 15, "type": "功能模块", "parentId": "6000", "remark": "教务管理", "authority": "jwm:module"}, {"id": "6017", "permissionCode": "jwm:gradeSubject:menu", "description": "年级学科", "url": "/jwm/gradeSubject", "icon": "home", "menuLevel": 3, "displayOrder": 16, "type": "访问路径", "parentId": "6016", "remark": "年级学科", "authority": "jwm:gradeSubject:menu"}, {"id": "6018", "permissionCode": "jwm:module", "description": "家校通", "url": "/jxt", "icon": "home", "menuLevel": 2, "displayOrder": 17, "type": "功能模块", "parentId": "6000", "remark": "家校通", "authority": "jxt:module"}, {"id": "6019", "permissionCode": "jxt:oneCardPass:menu", "description": "一卡通管理", "url": "/jxt/oneCardPass", "icon": "home", "menuLevel": 3, "displayOrder": 18, "type": "访问路径", "parentId": "6018", "remark": "一卡通管理", "authority": "jxt:oneCardPass:menu"}, {"id": "6020", "permissionCode": "jxt:access:menu", "description": "门禁管理", "url": "/jxt/access", "icon": "home", "menuLevel": 3, "displayOrder": 19, "type": "访问路径", "parentId": "6018", "remark": "门禁管理", "authority": "jxt:access:menu"}, {"id": "6021", "permissionCode": "jxt:jxtManage:menu", "description": "家校通管理", "url": "/jxt/jxtManage", "icon": "home", "menuLevel": 3, "displayOrder": 20, "type": "访问路径", "parentId": "6018", "remark": "家校通管理", "authority": "jxt:jxtManage:menu"}, {"id": "6022", "permissionCode": "jxt:package:menu", "description": "家校通套餐设置", "url": "/jxt/package", "icon": "home", "menuLevel": 3, "displayOrder": 21, "type": "访问路径", "parentId": "6018", "remark": "家校通套餐设置", "authority": "jxt:package:menu"}, {"id": "6023", "permissionCode": "jxt:open:menu", "description": "家校通开通查询", "url": "/jxt/open", "icon": "home", "menuLevel": 3, "displayOrder": 22, "type": "访问路径", "parentId": "6018", "remark": "家校通开通查询", "authority": "jxt:open:menu"}, {"id": "6024", "permissionCode": "sys:module", "description": "系统管理", "url": "/sys", "icon": "home", "menuLevel": 2, "displayOrder": 23, "type": "功能模块", "parentId": "6000", "remark": "系统管理", "authority": "sys:module"}, {"id": "6025", "permissionCode": "sys:lessonManage:menu", "description": "课时管理", "url": "/sys/lesson", "icon": "home", "menuLevel": 3, "displayOrder": 24, "type": "访问路径", "parentId": "6024", "remark": "课时管理", "authority": "sys:lessonManage:menu"}]}}}}