<template>
	<div>
		<component class="w99" 
			v-if="dynamicComponent"
	        :is="dynamicComponent"
			:key="cKey"
            :href="gps"
            :dialogClose="dialogClose"
		></component>
	</div>
</template>
<script>
export default {
	name: "dialogForm",
	props: {
		gps: {
			type: Object,
			default(){
				return this.$route.query;
			}
		},
		cKey: {
			type: Number,
		},
		dialogClose: {
			type: Function
		},
		isDialog: {
			type: Boolean,
			default(){
				return true;
			}
		}
	},
	computed:{
		dynamicComponent(){
			var th = 'views'+this.gps.btnForm;
			if(th){
				return ()=> import(`@/${th}`);// import不能接收纯动态参数
			}else{
				return null;
			}
		}
	},
	data() {
		return {
		
		}
	},
	created() {
		
	},
	mounted(){
		
	},
	methods: {
		handleDoFun(obj, fun, data) {
			//若一个beforeFun可直接在这个函数里面写
			let n = this[obj[fun]].call(this, obj, data);
			return n;
		}
	}
};
</script>
<style scope>
.w99{
	width: 99%;
    height: 80% !important;
	margin: 0 auto;
    overflow: auto;
}
.p10{
	padding: 10px;
}
.el-dialog__body{
	padding: 20px 20px 0;
}
</style>
