<template>
	<div class="app-container" style="display:flex;justify-content: space-between;">
		<div class="container-right">
			<sb-el-table :table="table" @getList="getList" @handleEnabled="handleEnabled" @handleAddData="handleAddData" @handleUpData="handleUpData"
			@handleDelete="handleDelete" @handleUpDataGetRow="handleUpDataGetRow" @handleAddBef="handleAddBef" @uploadHttpRequest="uploadHttpRequest">
				<!-- <template v-slot:enableStatus="{ obj }">
					<el-switch v-model="obj.row.enableStatus" :active-value="1" :inactive-value="0" @change="handleChangeEnable(obj.$index, obj.row)"></el-switch>
				</template>
				<template v-slot:publicStatus="{ obj }">
					<div>{{ obj.row.publicStatus == true ? '是' : '否' }}</div>
				</template> -->
				<!--
				<template v-slot:dataSource="{ obj }">
					<el-select v-model="obj.formValue.dataSourceId" filterable clearable :disabled="obj.item.disabled || false" placeholder="请选择" @change="sourceChange">
						<el-option v-for="item in dbArr" :key="item.id" :label="item.name" :value="item.id">
						</el-option>
					</el-select>
				</template> -->
				<template v-slot:skinEnabled="{ obj }">
					<el-switch v-model="obj.row.skinEnabled" :active-value="true" :inactive-value="false" @change="handleEnabled(obj.$index, obj.row)"></el-switch>
				</template>
				<template v-slot:previewSkinUrl="{ obj }">
					<el-button type="text" @click="handleJumpImage(obj.row.previewSkinUrl)">{{ obj.row.previewSkinUrl }}</el-button>
				</template>
			</sb-el-table>
			<el-dialog title="图片预览" :visible.sync="imgDialog" width="250px" :close-on-click-modal="false">
				<div style="display:flex;flex-direction: column;">
					<img :src="dialogImageUrl" alt="图片预览" class="dialog-img" style='height:300px'/>
				</div>
			<span slot="footer" class="dialog-footer">
				<el-button @click="imgDialog = false">取 消</el-button>
			</span>
		</el-dialog>
		</div>
	</div>
</template>
<script>
import { getSkinList,createSkinList,setEnabled,oneselfUploadFiles,updateSkinList,deleteById } from '@/api/system/dlypfgl.js';
export default {
	name: 'dataSet',
	props: ["tableType", "tableTitle"],
	data() {
		return {
			table: {
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'dlypfgl-皮肤管理', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: true, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					inline: true,
					labelWidth: '110px',
					labelPosition: 'right',
					formItemList: [
						{ label: '皮肤名称', key: 'skinName', type: 'input', clearable: true },
					],
				},
				tr: [
					{ id: 'skinName', label: '皮肤名称', prop: 'skinName',},
					{ id: 'startTime', label: '有效开始时间', prop: 'startTime' },
					{ id: 'endTime', label: '有效结束时间', prop: 'endTime',  },
					{ id: 'imageName', label: '皮肤图片名称', prop: 'imageName',  },
					{ id: 'skinImageUrl', label: '皮肤图片url', prop: 'skinImageUrl',  },
					{ id: 'previewSkinUrl', label: '预览皮肤url', prop: 'previewSkinUrl', show: 'template',width: '400' },
					{ id: 'skinEnabled', label: '是否启用', prop: 'skinEnabled', show: 'template' },
				],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				form: {
					width: '500px',
					labelWidth: '120px',
					inline: true,
					labelPosition: 'right',
					formItemList: [
						{ class: 'c11', label: '皮肤名称', key: 'skinName', type: 'input',rule: { required: true }, clearable: true },
						{ class: 'c11', label: '有效开始时间', key: 'startTime', type: 'date', rule: { required: false }, clearable: true },
						{ class: 'c11', label: '有效结束时间', key: 'endTime', type: 'date', clearable: true  },
						{ class: 'c11', label: '图片', key: 'fileList', type: 'upload', btnText: "上传", fun: 'uploadHttpRequest', url: '/action/loginSkin/oneselfUploadFiles', listType: "picture", multiple: false, clearable: true},
					],
				},
				listFormModul: {},
				hasOperation: true, //是否有操作列表
				operation: {
					width: '200',
					data: [
						{ id: 'add', name: '新增',beforeFun: "handleAddBef", fun: 'handleAddData' },
                        { id: 'read', name: '查看', fun: 'handleUpDataGetRow' },
						{ id: 'update', name: '编辑', fun: 'handleUpData', beforeFun: "handleUpDataGetRow" },
						{ id: 'delete', name: '删除', fun: 'handleDelete'},
						// { id: 'enabled', name: '启用', fun: 'handleEnabled'},
					],
				},
				hasPagination: true,
				listQuery: { size: 10, page: 1 },
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: true,
				otherQueryBtn: {
					data: [
                        {
                            id: 'export',
                            name: '导出 ',
                            fun: 'exportSelect',
                        },
                    ],
				},
				hasGroupTabs: true,
				tabsList: [],
				tabsPosition: '全部',
			},
            defaultProps: {
                children: 'children',
                label: 'label'
            },
			imgDialog: false,
			dialogImageUrl: ''
		};
	},
	activated() {
		
	},
	created(){
		this.getList() 
	},
	methods: {
		handleJumpImage(url) {
			// window.open(url, '_blank');
			this.imgDialog = true
			this.dialogImageUrl = url
		},
		uploadHttpRequest(obj) {
			console.log(obj.formData,"obj.formData");
			oneselfUploadFiles(obj.formData).then(res => {
					obj.content.onSuccess(res, obj.content.file, []);
					this.table.listFormModul.fileIds = res.data.sysFiles[0].id;
					this.table.listFormModul.id = '';
					this.table.listFormModul.imageName = res.data.sysFiles[0].fileName;
					this.table.listFormModul.previewSkinUrl = res.data.sysFiles[0].fileName;
					this.table.listFormModul.skinEnabled = false;
					this.table.listFormModul.skinImageUrl = res.data.sysFiles[0].fileName;
			}).catch(error => {
					obj.content.onError();
			});
		},
		handleEnabled(index,obj) {
			let params = {
				id: obj.id,
			}
			setEnabled(params).then((res) => {
				if(res.status == 200) this.getList();
			}).catch((err) => {
				
			});
		},
		// 查询列表
		getList() {
			this.table.loading = true;
			getSkinList(this.table.listQuery).then((res) => {
				this.table.loading = false;
				this.table.data = res.data&&res.data.content?res.data.content:[];
				this.table.total = res.data.totalElements;
			}).catch((err) => {
				this.table.loading = false;
			});
		},
		handleAddBef(){
			this.table.operation.nowBtn = true;
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = false;
			})
			return true
		},
		// 新增
		handleAddData() {
			this.table.listFormModul.skinEnabled = false;
			createSkinList(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200){
					this.getList();
				} 
			})
		},

		// 编辑
		handleUpData() {
			updateSkinList(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200) this.getList();
			})
		},
		// 删除
		handleDelete(row) {
			deleteById(row.id).then((res) => {
				if (res.status == 200) this.getList();
			});
		},
		// 根据id查询行数据
		handleUpDataGetRow(row) {
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = row.read;
			})
			this.table.listFormModul = row
		},
		
		// 修改是否启用
		handleChangeEnable(index, row) {
			// changeStatus({ id: row.id, enableStatus: row.enableStatus }).then((res) => { }).catch((error) => {
			// 	this.table.data[index].enableStatus = this.table.data[index].enableStatus;
			// });
		},
	},
};
</script>
<style scoped>
.table-name {
    font-weight: bold;
}
.container-left {
    width: 300px;
    height: 100%;
    overflow: auto;
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
}
.container-right {
    width: calc(100% - 10px);
    height: 100%;
	position: relative;
}
::-webkit-scrollbar {
    display: none;
}
::v-deep .el-card__header{
	padding: 8px 15px;
}
.treeData{
	width:100%;
	display:flex;
	justify-content: space-between;
	font-size: 15px;
	height: 30px;
	line-height: 30px;
	color: #444;
	padding-left: 8px;
}
.treeData .text{
	width: 215px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	/* padding: 5px; */
	cursor: pointer;
}
.treeData .text:hover{
	color: #0F85CF;
}
.treeData .execute{
	width: 70px;
	color: #0F85CF;
	cursor: pointer;
	text-align: right;
}
.treeData .execute:hover{
	text-decoration: underline;
}
::v-deep .el-card__body{
	padding: 0 15px;
	padding-bottom: 15px;
}
.head-container{
	height: calc(100vh - 180px);
	overflow-y: auto;
    padding: 20px;
}
::v-deep .el-table__body-wrapper{
	max-height: calc(100vh - 320px);
	overflow-y: auto;
}
.acceptanceBox{
    position: relative;
	/* height: 250px; */
}
.footer{
    width: 100%;
    /* position: absolute; */
    left: 0;
    bottom: 0;
    text-align: center;
    margin-top: 20px;
	margin-bottom: 20px;
}
.selectedColor {
	background-color: #eeeeee;
}
</style>
