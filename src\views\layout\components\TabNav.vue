<template>
  <div class="tabnav" :class="showSidebar?'':'left0'">
    <a class="sidebarIcon" @click="handleSidebar()">
      <svg class="hamburger" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" style="color: #fff !important">
        <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z" />
      </svg>
    </a>
    <a class="tabIconL" @click="handleScroll(true)"><svg-icon iconClass="zuoshuangjiantou"></svg-icon></a>
    <scroll-bar-transform :type="'left'" :width="tabli" ref="tabnvaSB">
      <ul class="tabli" :style="{ height: '40px', width: tabli + 'px' }">
        <li v-for="(item, index) in tabnav" :ref="'tab' + index" :path="'tab' + index" :key="item.id" :class="[isActive(item)?'li_active':'',tabnav.length==1?'oneLi':'']">
          <router-link :to="item.path">
            <span class="f12">{{item.title}}</span>
            <a v-show="tabnav.length>1" @click.prevent.stop="closeTab(item, index)"><svg-icon class="f10" iconClass="bg-guanbi"></svg-icon></a>
          </router-link>
        </li>
      </ul>
    </scroll-bar-transform>
    <a class="tabIconR" @click="handleScroll(false)"><svg-icon iconClass="youshuangjiantou"></svg-icon></a>
    <el-dropdown class="dropdown" :show-timeout="0">
      <i class="el-icon-arrow-down el-icon--right"></i>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item @click.native="closeTabActive">关闭当前标签页</el-dropdown-item>
        <el-dropdown-item @click.native="closeTabOther">关闭其它标签页</el-dropdown-item>
        <el-dropdown-item @click.native="closeTabAll">关闭全部标签页</el-dropdown-item>
        <el-dropdown-item @click.native="tabActiveRefresh">刷新当前标签页</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import scrollBarTransform from "@/components/ScrollBarTransform";
export default {
  components: { scrollBarTransform },
  computed: {
    ...mapGetters(["tabnav"]),
  },
  data() {
    return {
      tabli: 0,
      showSidebar: true
    };
  },
  mounted() {
    this.addTab();
    this.SBWidth();
  },
  watch: {
    $route() {
      this.addTab();
    },
  },
  methods: {
    handleScroll(type) {
      let sb = this.$refs.tabnvaSB;
      if (sb.left < 0 && type) {
        if (sb.left + 100 > 0) sb.left = 0;
        else sb.left += 100;
      }
      let rc = sb.$refs.scrollContainer.clientWidth - this.tabli;
      if (sb.left > rc && !type) {
        if (sb.left - 100 < rc) sb.left = rc;
        else sb.left -= 100;
      }
    },
    handleSidebar() {
      this.showSidebar = !this.showSidebar;
      this.$emit("handleSidebar");
    },
    SBWidth() {
      let zc = 0;
      for (let i in this.tabnav) {
        //if(this.$refs["tab"+i]) zc+=(39+this.$refs['tab'+i][0].clientWidth);

        // zc+=(14+36+(this.tabnav[i].title.length*13));
        zc += 14 + 39 + this.tabnav[i].title.length * 13;
      }
      this.tabli = zc;
    },
    isActive(tab) {
      return tab.path === this.$route.path;
    },
    addTab() {
      this.$store
        .dispatch("AddTabnav", {
          name: this.$router.getMatchedComponents()[1].name,
          path: this.$route.path,
          title: this.$route.meta.title,
        })
        .then((res) => {
          this.SBWidth();
        });
    },
    closeTab(item, index) {
      this.$store.dispatch("CloseTabnav", item).then((res) => {
        if (this.isActive(item)) {
          const lastTag = res.slice(-1)[0];
          // 前一个 tab-view 页面存在，就跳；不存在就到首页
          if (lastTag) {
            this.$router.push({ path: lastTag.path });
          } else {
            // this.$router.push({ path: "/home" });
            this.$router.push({ path: "/welcome" });
          }
        }
        this.SBWidth();
      });
    },
    closeTabActive() {
      let item = this.tabnav.find((item) => item.path === this.$route.path);
      this.$store.dispatch("CloseTabnav", item).then((res) => {
        if (this.isActive(item)) {
          const lastTag = res.slice(-1)[0];
          // 前一个 tab-view 页面存在，就跳；不存在就到首页
          if (lastTag) {
            this.$router.push({ path: lastTag.path });
          } else {
            // this.$router.push({ path: "/home" });
            this.$router.push({ path: "/welcome" });
          }
        }
        this.SBWidth();
      });
    },
    closeTabOther() {
      let item = this.tabnav.find((item) => item.path === this.$route.path);
      this.$store.dispatch("CloseTabOther", item).then((res) => {
        this.SBWidth();
      });
    },
    closeTabAll() {
      this.$store.dispatch("CloseTabAll").then((res) => {
        // this.$router.push({ path: "/home" });
        this.$router.push({ path: "/welcome" });
        this.SBWidth();
      });
    },
    tabActiveRefresh() {
      let item = this.tabnav.find((item) => item.path === this.$route.path);
      this.$router.replace({
        path: "/redirect",
        query: {
          t: Date.now(),
        },
      });
    },
  },
};
</script>
<style scoped>
.scroll-wrapper {
  height: 40px;
  width: auto;
}
.icon {
  margin: 0;
  line-height: 38px;
}
.tabnav {
  background: #fff;
  padding: 0 62px 0 24px;
  border-bottom: 2px solid #dddddd;
  height: 40px;
  overflow: hidden;
  position: fixed;
  left: 170px;
  right: 0;
  top: 54px;
  z-index: 8;
}
.tabnav.left0{
  left: 0;
}
.scroll-container {
  background: #eee;
}
.tabli li {
  width: auto;
  float: left;
  /* border: 1px solid #d9d9d9;
    border-width: 0 1px; */
  /* background: #eee; */
  padding-left: 12px;
  line-height: 38px;
  font-size: 14px;
  color: #484848;
  margin-right: 10px;
}
.tabli li.li_active,
.tabli li:hover {
  /* background: #f1f1f1; */
  background: #fff;
  height: 26px;
  line-height: 26px;
  margin-top: 7px;
  border-radius: 12px;
}
.tabli li.oneLi {
  padding-right: 12px;
}
.tabli li .icon {
  margin: 0 8px 0 4px;
  color: #eee;
}
.tabli li:hover .icon,
.tabli li.li_active .icon {
  color: #bbb;
}
.tabIconL,
.tabIconR,
.dropdown,
.sidebarIcon {
  position: absolute;
  line-height: 40px;
  color: #515151;
  top: 0;
  padding: 0 3px;
}
.tabIconL,
.tabIconR {
  color: #999;
  font-size: 12px;
  background: #eee;
}
.sidebarIcon {
  color: #999;
  width: 24px;
  left: 0;
  z-index: 9;
  line-height: 44px;
}
.tabIconL {
  left: 24px;
  z-index: 9;
  /* border-right: 1px solid #d9d9d9; */
}
.tabIconR {
  right: 38px;
  /* border-left: 2px solid #d9d9d9; */
}
.dropdown {
  right: 0;
  font-size: 24px;
  /* padding: 0 1px; */
  color: #999;
  border-left: 1px solid #d9d9d9;
  background: #eee;
  cursor: pointer;
}
.dropdown .el-icon--right {
  margin: 0 5px;
}
.f10 {
  color: #333;
}
.zuoshuangjiantou,
.youshuangjiantou {
  border: solid 1px #999;
}
</style>
