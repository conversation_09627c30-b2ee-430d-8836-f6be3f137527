<template>
  <div v-bind="$attrs" v-on="$listeners">
    <div v-if="!item.src"><el-tag>无照片</el-tag></div>
    <div v-else>
      <el-tag v-if="item.type === 'button'" @click="handleImgToBig"
        >查看</el-tag
      >
      <img
        v-else
        ref="img"
        :style="imgStyle"
        :src="imgToSeeUrl"
        @click="handleImgToBig"
      />
    </div>
    <el-dialog v-dialogDrag :visible.sync="imgToBig" width="60%" append-to-body>
      <img :src="imgToSeeUrl" style="width:100%;" />
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "SbImg",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      imgToBig: false,
      imgToSeeUrl: this.item.src || ""
    };
  },
  computed: {
    imgStyle: function() {
      let sty = {};
      if (this.item.width) sty.width = this.item.width;
      if (this.item.height) sty.height = this.item.height;
      if (sty.length === 0) sty.width = "100%";
      return sty;
    }
  },
  methods: {
    handleImgToBig(item, index) {
      this.imgToBig = true;
    }
  },
  watch: {
    "item.src": {
      handler: function(newV, oldV) {
        if (newV) {
          // console.log("值更新啦！", newV);
          //this.imgToSeeUrl=require(`${newV}`);
          this.imgToSeeUrl = newV;
        }
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
