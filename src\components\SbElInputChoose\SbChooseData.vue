<template>
  <div class="inputBtn" style="width:100%" >
    <el-input ref="elInput" :type="item.inputType || 'text'" v-bind="$attrs" v-on="$listeners" :size="item.size || 'small'" :placeholder="item.placeholder || item.label || '请输入'" :disabled="item.disabled || false" :readonly="item.readonly || false" :autosize="item.autosize || false">
      <el-button slot="append" :size="item.size || 'small'" type="primary" :disabled="item.disabled || false" @click="openDialog">{{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
      </el-button>
    </el-input>
    <el-dialog title="选择数据" v-dialogDrag :visible.sync="dialogVisible" width="60%" append-to-body>
      <el-container>
        <el-main>
          <sb-el-table :table="table" ref="chooseTeacher" v-bind="$attrs" v-on="$listeners" @getList="getList" @updateTableData="updateTableData"></sb-el-table>
        </el-main>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="handleConfirm" size="small">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { associationFind } from "@/api/public";
import util from '@/assets/js/public'
export default {
  name: "SbChooseData",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      table: {
        border: true, //是否带纵向边框
        loading: false, // 加载中动画
        modulName: "chooseData", //列表中文名称
        stripe: true, //是否为斑马条样式
        hasSelect: true, //是否有复选框
        data: [], //数据
        addAndUpdateType: "dialog",
        showIndex: true,
        mulitple: false,
        rowKey: "username",
        total: null,
        hasQueryForm: false, //是否有查询条件
        // show有三种值：false隐藏当前列，true常规表格列，template自定义表格列  默认:true
        //<template slot-scope='props' slot='example'>
        //<a class='list-a' target='_blank' :href='"/#/bombscreen?mobile=" + props.obj.row.mobile'>{{ props.obj.row.username }}</a>
        //</template>
        queryForm: {
          inline: true,
          labelWidth: "0px",
          formItemList: [
          ]
        },
        tr: [
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {},
        hasOperation: false, //是否有操作列表
        operation: {},
        listFormModul: {},
        hasPagination: true,
        listQuery: {
          size: 10,
          page: 1,
        },
        hasBatchOperate: false //有无批量操作
        //batchOperate:{}
      }
    };
  },
  methods: {
    openDialog(e) {
      this.getList();
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("chooseData", this.table.multipleSelection[0][this.item.associationId]);
    },
    getList(listQuery) {
      this.table.loading = true;
      associationFind(listQuery || this.table.listQuery)
        .then(res => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
          this.table.loading = false;
        })
        .catch(err => {
          this.table.loading = false;
        });
    },
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
  },
  created() {
    if (this.item.associationOptions.length > 0) {
      this.item.associationOptions.forEach((item) => {
        item.id = item.value
        item.label = item.name
        item.prop = item.value
      })
      this.table.tr = this.item.associationOptions
    } else {
      this.table.tr = []
    }
    this.table.listQuery.url = this.util.toHump(this.item.association)
    // this.getList();
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}
.el-main {
  padding: 0px 20px;
  margin-left: 20px;
}
.asideR {
  border-left: 1px solid #e0e0e0;
  padding-left: 15px;
}
.chooseD a {
  display: block;
  padding: 5px 0;
}
</style>
