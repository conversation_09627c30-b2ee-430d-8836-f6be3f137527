<template>
  <div class="inputBtn" style="width:100%" >
    <el-input
      ref="elInput"
      :type="item.inputType || 'text'"
      v-bind="$attrs"
      v-on="$listeners"
      :size="item.size || 'small'"
      :placeholder="item.placeholder || item.label || '请输入'"
      :disabled="item.disabled || false"
      :readonly="item.readonly || false"
      :autosize="item.autosize || false"
    >
      <el-button
        slot="append"
        :size="item.size || 'small'"
        type="primary"
        :disabled="item.disabled || false"
        @click="openDialog"
        >{{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
      </el-button>
    </el-input>
    <el-dialog
      title="选择数据"
      v-dialogDrag
      :visible.sync="dialogVisible"
      width="60%"
      append-to-body
    >
      <el-container>
        <el-main>
          <sb-el-table
            :table="table"
            ref="chooseGroup"
            v-bind="$attrs"
            v-on="$listeners"
            @getList="getList"
            @updateTableData="updateTableData"
          ></sb-el-table>
        </el-main>
        <el-aside width="200px" class="asideR">
          <h5 class="fbold">已选数据</h5>
          <div class="chooseD">
            <a
              v-for="citem in table.multipleSelection"
              :key="citem[item.dialogData.stores]"
              >{{ citem[item.dialogData.reveals]
              }}
                <!-- <span class="fr" @click="delChoose(citem)"><svg-icon iconClass="close"></svg-icon></span> -->
              </a>
          </div>
        </el-aside>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="handleConfirm" size="small"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getDataByApi } from "@/api/apply/application";
import util from "@/assets/js/public";
export default {
  name: "group",
  props: {
    item: {
      type: Object,
      required: true
    },
    dialogData: {
      type: Object,
      required: true
    },
    appFormValue: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      dialogVisible: false,
      table: {
        border: true, //是否带纵向边框
        loading: false, // 加载中动画
        modulName: "chooseGroup", //列表中文名称
        stripe: true, //是否为斑马条样式
        hasSelect: true, //是否有复选框
        data: [], //数据
        addAndUpdateType: "dialog",
        showIndex: true,
        mulitple: false,
        rowKey: "username",
        total: null,
        hasQueryForm: true, //是否有查询条件
        // show有三种值：false隐藏当前列，true常规表格列，template自定义表格列  默认:true
        //<template slot-scope='props' slot='example'>
        //<a class='list-a' target='_blank' :href='"/#/bombscreen?mobile=" + props.obj.row.mobile'>{{ props.obj.row.username }}</a>
        //</template>
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
          ]
        },
        tr: [
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {},
        hasOperation: false, //是否有操作列表
        operation: {},
        listFormModul: {},
        hasPagination: true,
        listQuery: {
          size: 10,
          page: 1,
        },
        hasBatchOperate: false //有无批量操作
        //batchOperate:{}
      }
    };
  },
  methods: {
    openDialog(e) {
      //console.log(this.$refs.elInput);
      let inputData = this.$refs.elInput._props;
      let formData = this.$refs.elInput.elForm._props.model;
      //console.log(JSON.stringify(formData));
      //console.log('formData');
      let relevancy = this.item.relevancy.split(",");
      let di = formData[relevancy[0].split("-")[0]]
        ? formData[relevancy[0].split("-")[0]].split(",").length
        : 0;
      if (di === 0) {
        this.table.multipleSelection = [];
      }
      for (let i = 0; i < di; i++) {
        let datai = {};
        for (let j in relevancy) {
          var reF = relevancy[j].split("-");
          if (reF.length === 1) reF.push(reF[0]);
          datai[reF[1]] = formData[reF[0]]
            ? formData[reF[0]].split(",")[i]
            : "";
        }
        var ml = 0;
        for (let k in this.table.multipleSelection) {
          for (let j in relevancy) {
            var reF = relevancy[j].split("-");
            if (reF.length === 1) reF.push(reF[0]);
            if (datai[reF[1]] === this.table.multipleSelection[k][reF[1]]) ml++;
          }
        }
        if (ml !== relevancy.length) {
          this.table.multipleSelection.push(datai);
        }
      }
      if (this.$refs.chooseGroup) {
        this.$refs.chooseGroup.handleTableMultipSelection();
      }
      //handleSelect
      //console.log(JSON.stringify(this.table.multipleSelection));
      this.dialogVisible = true;
    },
    handleConfirm() {
        console.log(this.table.multipleSelection)
        this.dialogVisible = false;
        this.$emit("chooseData", this.table.multipleSelection);
    },
    getList() {
      this.dialogData.parameter.forEach((item) => {
        item.DEFAULT_VALUE = item.formsId ? (this.appFormValue[item.formsId] || this.table.listQuery[item.API_PARAM_NAME]) : this.table.listQuery[item.API_PARAM_NAME]
      })
      this.table.loading = true;
      getDataByApi(this.dialogData.dialogData, this.dialogData.parameter).then((res) => {
            this.table.loading = false;
            this.table.data = res.data;
        })
        .catch(err => {
          this.table.loading = false;
        });
    },
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    delChoose(row) {
      if (
        (!this.item.mulitple && this.item.mulitple !== false) ||
        this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.table.multipleSelection));
        for (let i in arry) {
          let fl = 0;
          for (let j in row) {
            if (arry[i][j] === row[j]) fl++;
          }
          if (fl === row.length) arry.splice(arry[i], 1);
        }
        this.table.multipleSelection = arry;
        for (let i in this.table.data) {
          let fl = 0;
          for (let a in row) {
            if (this.table.data[i][a] && row[a] === this.table.data[i][a]) {
              fl++;
            }
          }
          if (fl === this.item.relevancy.split(",").length) {
            this.$refs.chooseGroup.handleToggleRowSelection(
              this.table.data[i],
              false
            );
          }
        }
      } else {
        //单选
        this.table.multipleSelection = [];
        this.$refs.chooseGroup.handleClearSelection();
      }
    }
  },
  created() {
    if (this.dialogData.options.length > 0) {
      this.dialogData.options.forEach((item) => {
        item.id = item.value
        item.label = item.name
        item.prop = item.value
      })
      this.table.tr = this.dialogData.options
    } else {
      this.table.tr = []
    }

    if (this.dialogData.paramsArr.length > 0) {
      this.dialogData.paramsArr.forEach((item) => {
        item.type = "input"
        item.label = item.CHINESE_NAME ? item.CHINESE_NAME : item.API_PARAM_NAME
        item.key = item.API_PARAM_NAME
      })
      this.table.queryForm.formItemList = this.dialogData.paramsArr
    } else {
      this.table.queryForm.formItemList = []
    }
    this.getList();
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}
.el-main {
  padding: 0px 20px;
  margin-left: 20px;
}
.asideR {
  border-left: 1px solid #e0e0e0;
  padding-left: 15px;
}
.chooseD a {
  display: block;
  padding: 5px 0;
}
</style>
