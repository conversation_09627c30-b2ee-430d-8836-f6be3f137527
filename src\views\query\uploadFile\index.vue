<template>
<div>
			<el-button type="primary" style="margin-left:10px;" size="mini"  @click="handle">上传</el-button>
      	<el-dialog :title="title" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body
			width="50%">
			<addInfo :key="cKey" :types="types" :rowData="rowdata"  @closeshowDialog="closeshowDialog">
			</addInfo>
		</el-dialog>
</div>
</template>
<script>
import addInfo from "@/components/myCom/addInfo";

export default {
  components: { addInfo },
  data(){
    return {
      title:'',
      viewD:false,
      cKey:0,
      types:'',
      rowdata:{}
    }
  },
  methods:{
    handle(){
      this.cKey++;
			this.viewD = true;
			this.title = '新增'
			this.types = 'add'
    },
    closeshowDialog() {
			this.viewD = false
		},
  }
}
</script>