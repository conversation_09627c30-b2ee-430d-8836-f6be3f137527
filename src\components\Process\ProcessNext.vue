<template>
  <div>
    <div :class="['processBox',{'processBoxHalf':hasCopy}]">
      <div class="left">
        <div class="boxTit">填写意见</div>
        <div class="boxCon leftCon">
          <!-- 决策项 -->
          <fieldset class="decision">
            <legend>决策项</legend>
            <el-radio-group v-model="decisionData['main'].decisionId || ''" @input="chooseDecision">
              <el-radio v-for="item in decisionList" v-if="!item.isCopy" :key="item.decisionId" :label="item.decisionId">{{item.decisionName}}</el-radio>
            </el-radio-group>
          </fieldset>
          <!-- 意见 -->
          <fieldset class="opinion">
            <legend>意见内容</legend>
            <el-input v-model="opinion" type="textarea" resize="none" :rows="5"></el-input>
          </fieldset>
        </div>
      </div>
      <div class="right">
        <div class="boxTit">{{gps.type=="toRead"?"抄送人":"办理人"}}</div>
        <div class="boxCon rightCon">
          <ul class="groupTit">
            <li v-for="(item,index) in decisionUser['main']" :key="index" :class="item.isShow?'active':''" @click="chooseGroup(index,'main')">{{item.group=="normalGrouping"?"待选人员":item.group}}</li>
          </ul>
          <div class="groupBox">
            <!-- key值需保证每个树的唯一性 -->
            <div class="groupD" v-for="(item,index) in decisionUser['main']" :key="decisionData['main'].decisionId+'_'+index" v-if="item.isShow">
              <div class="treeD">
                <el-tree :ref="'tree'+index" class="filter-tree" node-key="id" :data="item.tree" :props="defaultProps" :render-content="renderTreeNode"
                         :highlight-current="true" empty-text="" default-expand-all @node-click="(data, node, tree) => treeClick(data, node, tree, index, 'main')">
                  <!-- <span class="custom-tree-node" slot-scope="{node,data}">
											<span v-if="data.children" class="iconfont">&#xe70d;</span>
											<span v-else class="iconfont" style="margin-right: 3px"></span>
											<span>{{node.label}}</span>
									</span> -->
                </el-tree>
              </div>
              <div class="chooseD">
                <ul>
                  <li v-for="(it,i) in choosedUser['main'][index]" v-if="it.show!==false" :key="it.id">
                    <font>{{it.name}}</font>
                    <svg-icon v-if="it.cancelSelectUser!==false" icon-class="bg-guanbi" class-name="guanbi" @click.native="deleteSelectUser(index,i,'main')"></svg-icon>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 抄送 -->
    <div class="processBox copyBox" v-if="hasCopy">
      <div class="left">
        <div class="boxTit">{{decisionData["copy"].decisionNameNew || "抄送"}}</div>
        <div class="boxCon leftCon">
          <!-- 意见 -->
          <fieldset class="opinion">
            <legend>意见内容</legend>
            <el-input v-model="copyMessage" type="textarea" resize="none"></el-input>
          </fieldset>
        </div>
      </div>
      <div class="right">
        <div class="boxTit">{{decisionData["copy"].decisionNameNew || "抄送"}}人</div>
        <div class="boxCon rightCon">
          <ul class="groupTit">
            <li v-for="(item,index) in decisionUser['copy']" :key="index" :class="item.isShow?'active':''" @click="chooseGroup(index,'copy')">{{item.group=="normalGrouping"?"待选人员":item.group}}</li>
          </ul>
          <div class="groupBox">
            <div class="groupD" v-for="(item,index) in decisionUser['copy']" :key="decisionData['copy'].decisionId+'_'+index" v-if="item.isShow">
              <div class="treeD">
                <el-tree :ref="'copyTree'+index" class="filter-tree" node-key="id" :data="item.tree" :props="defaultProps" :highlight-current="true" empty-text="" default-expand-all @node-click="(data, node, tree) => treeClick(data, node, tree, index, 'copy')">
                </el-tree>
              </div>
              <div class="chooseD">
                <ul>
                  <li v-for="(it,i) in choosedUser['copy'][index]" v-if="it.show!==false" :key="it.id">
                    <font>{{it.name}}</font>
                    <svg-icon v-if="it.cancelSelectUser!==false" icon-class="bg-guanbi" class-name="guanbi" @click.native="deleteSelectUser(index,i,'copy')"></svg-icon>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getDecision, getOrgAndUser, getLastVersion } from "@/api/process";
import { ref } from 'vue';
// import { myDcision,myUsers } from "@/assets/js/json.js";
export default {
  name: "ProcessNext",
  props: {
    gps: {
      type: Object,
      required: true
    },
    appFormValue: {
      type: Object,
      required: true
    },
    decisionTypeArr: {
      type: Array,
    }
  },
  data() {
    return {
      decisionList: [],//决策项列表
      decisionData: { "main": {}, "copy": {} },//选中的决策项
      decisionUser: { "main": [], "copy": [] },//人员组织树
      choosedUser: { "main": [], "copy": [] },//选择的人员
      opinion: "",//意见
      indexG: 0,
      copyIndexG: 0,
      version: null,
      hasCopy: true,
      copyLocation: "",
      copyMessage: "",

      getUserOrOrg: {},

      defaultProps: {
        children: "children",
        label: "name",
        // disabled: (data) => {
        //   return data.treeLevel != "userNode";
        // },
      },
      isLoad: false,//用于标识接口是否加载完毕


      singleSel: true,

      decisionExpandConfig:[],  // 决策项过滤扩展规则
      expandtreeD:[], //人员
      expandUser:[],  //树状人员
    }
  },
  created() {
    // console.log('gps', JSON.parse(JSON.stringify(this.gps)))
    this.getDecisionList();//获取决策项
    // this.getLastVersionFun()
  },
  mounted() {

  },
  methods: {
    renderTreeNode(h, { node, data, store }) {  
      return h('span', [  
        h('span', {  
          attrs: {  
            // src: data.icon, // 使用节点数据中的图标路径  
            alt: '',  
            class: data.treeType=="user"?'custom-tree-user-icon':'custom-tree-node-icon', // 可选的CSS类名，用于样式定制  
          },  
        }),  
        h('span', data.name), // 节点文本  
      ]);  
    },
    // 获取决策项
    getDecisionList() {
      // body体参数
      var data = {
        appCode: process.env.VUE_APP_APPCODE,
        processDefId: this.gps.processDefKey,
        pmInsType: this.gps.pmInsType,
        activityDefId: this.gps.location ? this.gps.location : (process.env.VUE_APP_APPCODE + ".start"),
        type:!this.gps.location || (this.gps.type == "draft" && this.gps.location == process.env.VUE_APP_APPCODE + ".start")? "START": "FLOW",
        formulas: {},
        username:this.$store.getters.user.username,
      };
      // if (this.gps.type == 'toRead') {
      //   data.activityDefId = this.gps.nextlocation
      // }
      if (this.decisionTypeArr && this.decisionTypeArr.length > 0) {
        this.decisionTypeArr.forEach((item) => {
          data.formulas[item] = this.appFormValue[item]
        })
      }
      if (this.gps.taskId) data.taskId = this.gps.taskId;
      if (this.gps.processInstId) data.processInstId = this.gps.processInstId;
      getDecision(data).then((res) => {
        // TODO---start
        // var res = {};
        // res.data = [...myDcision];
        // TODO---end
        // 判断是否是抄送
        for (var i in res.data) {
          if (res.data[i].decisionName.indexOf("copy#") > -1) {
            res.data[i].isCopy = true;
          } else {
            res.data[i].isCopy = false;
          }
        }
        this.decisionList = res.data;
        // 默认选择第一个
        if (this.decisionList?.length > 0) {
          this.chooseDecision(this.decisionList[0].decisionId);
        }
      });
    },
    // 获取版本号
    getLastVersionFun() {
      getLastVersion({ key: this.gps.processDefKey, tenantId: process.env.VUE_APP_APPCODE }).then(res => {
        this.version = res.data.version
      })
    },
    // 选择决策项
    chooseDecision(curDecisionId) {
      // 重置
      this.decisionData["main"] = {}, this.decisionData["copy"] = {};
      this.choosedUser["main"] = [], this.choosedUser["copy"] = [];
      this.decisionUser["main"] = [], this.decisionUser["copy"] = [];
      var dIndex = this.decisionList.findIndex(item => item.decisionId === curDecisionId);
      if (dIndex > -1) {
        this.decisionData["main"] = { ...this.decisionList[dIndex] };
        var pdata = { ...this.decisionList[dIndex] };
        // console.log("决策项", JSON.parse(JSON.stringify(this.decisionList)));
        pdata.decisionConfig = this.util.htmlDecode(pdata.decisionConfig).replace(/'/g, '"');
        var decisionConfig = JSON.parse(pdata.decisionConfig);
        for (var m in decisionConfig) {
          if (decisionConfig[m].type == "pageType") {
            // console.log("候选人选择规则", decisionConfig[m]);
            // console.log("候选人选择规则", decisionConfig[m].groupType.split("#"));
            // this.singleSel = decisionConfig[m].groupType.split("#")[2]
            pdata.decisionConfigInfo = decisionConfig[m].typeValue;
            // console.log("候选人选择规则", pdata.decisionConfigInfo);
          }
          if (decisionConfig[m].type == "activiType" && decisionConfig[m].typeValue == "copy") {
            pdata.decisionConfigCopyInfo = true;
            pdata.decisionConfigincludeValue = decisionConfig[m].includeValue;
          }
        }

        this.opinion = pdata.opinion;//意见
        if (this.gps.type == "toRead") {
          this.copyLocation = pdata.decisionConfigincludeValue;
        }

        // console.log("决策项", pdata);
        // 办理人
        this.loadTree(pdata, "main");

        // 抄送
        // if(pdata.decisionConfigCopyInfo && (this.gps.type=="task" || (this.gps.location && this.gps.location.indexOf('.start')>-1))){
        // TODO
        if (pdata.decisionConfigCopyInfo && (this.gps.type == "task" || !this.gps.location || (this.gps.location && this.gps.location.indexOf('.start') > -1))) {
          // TODO
          this.hasCopy = true;
          for (var i in this.decisionList) {
            if (pdata.decisionConfigincludeValue == this.decisionList[i].decisionId) {
              this.decisionData["copy"] = { ...this.decisionList[i] };
              this.decisionData["copy"].decisionConfig = this.util.htmlDecode(this.decisionData["copy"].decisionConfig).replace(/'/g, '"');
              var copyDecisionConfig = JSON.parse(this.decisionData["copy"].decisionConfig);
              for (var m in copyDecisionConfig) {
                if (copyDecisionConfig[m].type == "activiType" && copyDecisionConfig[m].typeValue == "copy") {
                  this.decisionData["copy"].decisionConfigincludeValue = copyDecisionConfig[m].includeValue;
                }
              }
              this.decisionData["copy"].decisionNameNew = this.decisionData["copy"].decisionName.split("#")[1];

              this.copyLocation = this.decisionData["copy"].decisionConfigincludeValue;
              this.copyMessage = this.decisionData["copy"].opinion;

              // 抄送人
              this.loadTree(this.decisionData["copy"], "copy");
            }
          }
        } else {
          this.hasCopy = false;
        }


        // 决策项过滤扩展规则
        if(pdata.decisionExpandConfig){
          let decisionExpandConfig = this.util.htmlDecode(pdata.decisionExpandConfig).replace(/'/g, '"');
          this.decisionExpandConfig = JSON.parse(decisionExpandConfig);
        }
       
      }
      // console.log("选择的决策项", JSON.parse(JSON.stringify(this.decisionData)))
    },


    // 加载树
    loadTree(data, type) {
      data.source = 'PC';
      data.currentUserCode = this.$store.getters.user.username;
      if (this.gps.taskId) data.taskId = this.gps.taskId;
      if (this.gps.processInstId) data.processInstId = this.gps.processInstId;
      if (this.gps.pmInsId) data.pmInsId = this.gps.pmInsId;
      data.formData = this.appFormValue
      getOrgAndUser(data).then((res) => {
        // TODO---start
        // var res = {};
        // var typeF = this.decisionData[type].userFlag;
        // res.data = myUsers[typeF];
        // TODO---end
        this.isLoad = true;
        if (res.data&&res.data.length > 0) {
          this.decisionUser[type] = [...res.data];//人员组织树
          for (var i in this.decisionUser[type]) {
            this.decisionUser[type][i].flag = 0;
            this.choosedUser[type].push([]);
          }
          this.chooseGroup(0, type);


          // 决策项过滤扩展规则
          if(res.data[0].user && res.data[0].user.length>0){
            // 转为树形结构
            this.expandtreeD = res.data[0].user
            this.expandUser = this.util.toTreeData(this.expandtreeD, "id", "parentId", "id,parentId,name,treeType,cancelSelectUser,defaultSelectUser,treeLevel");
          }

        }else{
          this.choosedUser[type] = []
        }
      });
    },

    // 选择群组（多树情况）
    chooseGroup(index, type) {
      if (type == "main") {
        this.indexG = index;
      } else if (type == "copy") {
        this.copyIndexG = index;
      }

      for (var i in this.decisionUser[type]) {
        this.decisionUser[type][i].isShow = false;
        // 转为树形结构
        var treeD = this.decisionUser[type][i].user ? [...this.decisionUser[type][i].user] : [];
        if (this.decisionUser[type][i].display.indexOf("org") > -1) {
          treeD = this.getTreeType(this.decisionUser[type][i].user, "org");
        }
        this.decisionUser[type][i].tree = this.util.toTreeData(treeD, "id", "parentId", "id,parentId,name,treeType,cancelSelectUser,defaultSelectUser");
      }
      this.decisionUser[type][index].isShow = true;


      // 初始化处理默认值
      this.$nextTick(() => {
        if (this.decisionUser[type][index].flag == 0) {
          // 只有一个人时，默认选中
          var allNodes = [];
          if (type == "main") {
            allNodes = this.$refs["tree" + index][0].store.nodesMap;
          } else if (type == "copy") {
            allNodes = this.$refs["copyTree" + index][0].store.nodesMap;
          }
          if(this.decisionUser[type][i].tree.length > 0){
            var num = 0; //是否只有一个人
            var defaultUser = {}; //第一个人的信息
            for (let m = 0; m < this.decisionUser[type][i].user.length; m++) {
                if (this.decisionUser[type][i].user[m].treeType == 'user') {
                    num++;
                    defaultUser = this.decisionUser[type][i].user[m];
                    if (num > 1) {
                        break;
                    }
                }
            }
            if (num == 1) {
                this.choosedUser[type][index] = [defaultUser];
            }

            // var num = 0, defaultUser;
            // for (var m in allNodes) {
            //   if (allNodes[m].isLeaf) {
            //     defaultUser = { ...allNodes[m].data };
            //     num++;
            //     if (num > 1) break;
            //   }
            // }
            // if (num == 1) {
            //   this.choosedUser[type][index] = [defaultUser];
            // }
          }
        
          // 系统配置默认人
          var selectUser = [];
          for (var j in this.decisionUser[type][index].user) {
            if (this.decisionUser[type][index].user[j].defaultSelectUser) {
              var selectUserObj = this.decisionUser[type][index].user[j];

              if (((this.decisionUser[type][index].display == "user" && this.decisionUser[type][index].user[j].treeType == "user"))) {
                selectUserObj.show = true;
                selectUser.push(selectUserObj);
              } else {
                // 选组织情况下需要把人员也提交到后台，但是已选区域不展示人员
                selectUserObj.show = false;
              }
              
            }
          }

          if (selectUser.length > 0) {
            this.choosedUser[type][index] = [...selectUser];
          }
          this.choosedUser = JSON.parse(JSON.stringify(this.choosedUser));
          this.decisionUser[type][index].flag++;
          this.decisionUser = JSON.parse(JSON.stringify(this.decisionUser));
        }
      });
      // console.log('人员',JSON.parse(JSON.stringify(this.decisionUser)));
      // console.log('已选人员',JSON.parse(JSON.stringify(this.choosedUser)));
    },

    getTreeType(data, type) {
      var newData = [];
      for (var i in data) {
        if (data[i].treeType == type) {
          newData.push(data[i]);
        }
      }
      return newData;
    },

    // 点击树节点
    treeClick(data, node, tree, index, type) {
      // console.log(data, type, index)
      // 选组织情况（半展）
      if (this.decisionUser[type][index].display.indexOf("org") > -1 && data.page == "treePart") {

      } else {
        if (this.decisionUser[type][index].display == "user" && this.decisionUser[type][index].page == "treePart") {

        }
        if ((this.decisionUser[type][index].display == "user" && data.treeType == "user") || (this.decisionUser[type][index].display.indexOf("org") > -1 && data.treeType == "org")) {
          var cIndex = this.choosedUser[type][index].findIndex(item => item.id === data.id);
          if (cIndex == -1) {
            if (this.decisionUser[type][index].singleSel === true || this.decisionUser[type][index].singleSel == "true") {
              this.choosedUser[type][index] = [];
            }
            this.choosedUser[type][index].push(data);
          }
        }
      }
      this.choosedUser = JSON.parse(JSON.stringify(this.choosedUser));
    },

    // 删除已选人员
    deleteSelectUser(index, i, type) {
      this.choosedUser[type][index].splice(i, 1);
      this.choosedUser = JSON.parse(JSON.stringify(this.choosedUser));
    },

    // 点击树节点
    nodeClick(data) {
      // if (data.treeType == "user") {
      //   this.nextUsername = data;
      // }
      // // this.$refs.tree.setCheckedNodes([data]);
    },

    // 决策项过滤扩展规则
    decisionExpand(){
      if(this.decisionExpandConfig.length>0){
        let config = ''
        this.decisionExpandConfig.forEach(element => {
          if(element.typeValue != 'department'){
            config = element
          }
        });
        var nextUser = []
        for (var i in this.choosedUser["main"]) {
          for (var j in this.choosedUser["main"][i]) {
            nextUser.push(this.choosedUser["main"][i][j]);
          }
        }
        if(config && config.includeValue && nextUser.length>0){
          if(config.typeValue == 'allCompanyUser'){
              const parentsArrs = this.getparentsArrs()
              let company = this.getCompany(parentsArrs,nextUser)
              if(company){
                return true
              }else{
                this.$message({
                  message: "所有公司级别必须选择一人",
                  type: "warning",
                  duration: 1500
                });
                return false
              }
          } else if (config.typeValue == 'allDeptUser'){
              const parentsArrs = this.getparentsArrs()
              const deptsArrs = this.getdeptsArrs(parentsArrs)
              let company = this.getCompany(deptsArrs,nextUser)
              if(company){
                return true
              }else{
                this.$message({
                  message: "所有部门级别必须选择一人",
                  type: "warning",
                  duration: 1500
                });
                return false
              }
          } else if (config.typeValue == 'allOrgUser'){
              const parentsArrs = this.getparentsArrs()
              const deptsArrs = this.getdeptsArrs(parentsArrs)
              const orgsArrs = this.getdeptsArrs(deptsArrs)
              let company = this.getCompany(orgsArrs,nextUser)
              if(company){
                return true
              }else{
                this.$message({
                  message: "所有人科室级别必须选择一人",
                  type: "warning",
                  duration: 1500
                });
                return false
              }
          } else {
            return true
          }
        }else{
          return true
        }
      }else{
        return true
      }
    },
    // 判断选择人员是否符合规则
    getCompany(parentsArrs,nextUser){
      let allCode = []
      nextUser.forEach(element => {
        let treeData = this.getAllParentArr(this.expandUser,element.id,'id','children')
        const companyCode = treeData.filter(element => {
          return element.id && element.treeType !== 'user';
        }).map(element => {
          return element.id;
        });
        allCode = [...allCode, ...companyCode]
      });
      let newAllCode = this.util.newArrFn(allCode)
      let company = true
      parentsArrs.forEach(element => {
        if(newAllCode.indexOf(element) == -1){
          company = false
        } 
      });
      return company
    },
    // 获取所有公司
    getparentsArrs(){
      const parentsArrs = this.expandtreeD.filter(element => {
        return (element.parentId == '00000000000000000000');
      }).map(element => {
          return element.id;
      });
      return parentsArrs
    },
    // 根据传的数组获取所有下级
    getdeptsArrs(parentsArrs){
      const deptsArrs = this.expandtreeD.filter(info => {
        return (parentsArrs.indexOf(info.parentId) > -1);
      }).map(info => {
          return info.id;
      });
      return deptsArrs
    },
    // 获取选择人员的所有父级
    getAllParentArr(list,id,name,child){
        for(let i in list){
            if(list[i][name] == id){
                return [list[i]]
            }
            if (list[i][child]) {
                let node = this.getAllParentArr(list[i][child],id,name,child)
                if(!!node){
                    return node.concat(list[i])
                }
            }
        }
    }
  }
}
</script>
<style scope>
.bor1 {
  outline: 1px solid green;
}
.bor2 {
  outline: 1px solid red;
}
.processBox {
  display: flex;
  justify-content: space-between;
  /* align-items: stretch; */
  margin-bottom: 15px;
}
.processBox .left {
  /* flex: 1; */
  width: 260px;
}
.processBox .right {
  width: calc(100% - 260px);
}
.processBox .boxTit {
  height: 35px;
  font-size: 14px;
  line-height: 32px;
  font-weight: bold;
  text-align: center;
  background-color: #eee;
  border-top: 2px solid #39aef5;
}
.processBox .leftCon {
  padding-right: 20px;
}
.processBox fieldset {
  min-height: 60px;
  padding: 8px 10px 10px;
  border: 1px solid #e2e2e2;
  margin-top: 10px;
}
.processBox legend {
  font-size: 14px;
  font-weight: bold;
  line-height: 20px;
  padding: 0 10px;
  margin-left: 0;
}
.decision .el-radio-group {
  height: 200px;
  padding: 0 10px 0 10px;
  overflow-x: hidden;
  overflow-y: auto;
}
.processBoxHalf .decision .el-radio-group {
  height: 110px;
}
.decision .el-radio {
  float: none;
  width: 100%;
  margin-right: 0;
}
.decision .el-radio__label {
  padding-left: 6px;
  white-space: normal;
}
.opinion .el-textarea__inner {
  height: 100px;
  font-size: 12px;
  line-height: 16px;
  padding: 8px;
  border: 1px solid #e5e5e5;
  border-radius: 0;
}
.processBoxHalf .opinion .el-textarea__inner {
  height: 35px;
}
.processBox .rightCon {
  position: relative;
  height: calc(100% - 55px);
  max-height: 374px;
}
.rightCon ul.groupTit {
  position: absolute;
  top: -10px;
  z-index: 9;
  width: 65%;
  display: flex;
  padding: 0 10px;
}
.rightCon ul.groupTit li {
  background-color: #fff;
  padding: 0 8px;
  margin-right: 12px;
  cursor: pointer;
}
.rightCon ul.groupTit li:last-child {
  margin-right: 0;
}
.rightCon ul.groupTit li.active {
  color: #444;
  font-weight: bold;
}
.rightCon .groupBox {
  position: relative;
  width: 100%;
  height: 100%;
  margin-top: 20px;
}
.rightCon .groupD {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  /* align-items: stretch; */
  height: 100%;
  border: 1px solid #e2e2e2;
}
.rightCon .treeD {
  /* flex: 1; */
  width: 65%;
  height: calc(100% - 15px);
  padding: 0 10px 5px;
  margin-top: 15px;
  overflow-y: auto;
}
.treeD .el-tree__empty-text {
  font-size: 13px;
}
.rightCon .chooseD {
  width: 35%;
  height: 100%;
  border-left: 1px solid #e2e3e2;
  overflow-y: auto;
}
.chooseD ul li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  padding: 5px 10px;
  cursor: pointer;
}
.chooseD ul li .guanbi {
  width: 1.4em;
  color: #aaa;
  margin: 0;
}
.chooseD ul li:hover {
  background: #e2f4ff;
  color: #00b4f1;
  text-decoration: none;
  font-weight: bold;
}
.chooseD ul li:hover .guanbi {
  color: #00b4f1;
}

.custom-tree-node-icon {  
  /* 你可以在这里添加自定义样式来进一步定制图标 */  
  display: inline-block;
  background-image: url('../../assets/images/tree_icons.png');
  width: 18px;
  height: 18px;
  margin: 0 3px -3px 0;
  background-position: 20px 0;
}
.custom-tree-user-icon {  
  /* 你可以在这里添加自定义样式来进一步定制图标 */  
  display: inline-block;
  background-image: url('../../assets/images/tree_icons.png');
  width: 18px;
  height: 18px;
  margin: 0 3px -3px 0;
  background-position: 20px 19px;
}
</style>
