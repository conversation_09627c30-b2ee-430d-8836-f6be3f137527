import request from "@/assets/js/request";

// 根据字典类型查字典值列表
export function findAllDictValue(params) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dictValue/findAll?page=" +
            params.page +
            "&size=" +
            params.size,
        contentType: "application/json;charset=UTF-8",
        data: {
            dictType: params.dictType
        }
    });
}

// 根据字典值id查字典值信息
export function findDictValueById(id) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dictValue/findById?id=" + id,
        contentType: "application/json;charset=UTF-8"
    });
}

// 新增字典值
export function createDictValue(params) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dictValue/create",
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}

// 修改字典值
export function updateDictValue(params) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dictValue/update",
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}

// 根据id删除一个字典值
export function deleteDictValueById(id) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dictValue/deleteById?id=" + id,
        contentType: "application/json;charset=UTF-8"
    });
}

// 批量删除
export function deleteDictValueByIds(ids) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dictValue/deleteAllByIds",
        contentType: "application/json;charset=UTF-8",
        data: ids
    });
}

// 修改启用/禁用
export function updateDictValueEnable(id, enabled) {
    return request({
        url: "/" + process.env.VUE_APP_APPCODE + "/sys/dictValue/updateEnable?id=" + id + "&enabled=" + enabled,
        contentType: "application/json;charset=UTF-8"
    });
}