<template>
  <div>
    <div
        :id="item.key"
        :style="`padding: 20px;width:${
            control.styleWidth ? control.styleWidth : '800px'
        };height:${
            control.styleHeight ? control.styleHeight : '600px'
        }`"
    />
    <div style="width: 800px; height: 600px" :id="item.key"></div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import util from "@/assets/js/public";
import request from "@/assets/js/request";
import henanData from './henan.js'

export default {
  name: "sb-map",
  props: {
    item: {
      type: Object,
      required: true,
    },
    control: {
      type: Object,
      required: true,
    },
  },
  data: () => {
    return {
      mapChart: {}
    }
  },
  mounted() {
    this.mapChart = echarts.init(document.getElementById(this.item.key))
    this.handleMap()
  },
  methods: {
    async handleMap() {
      let optionMap = {
        // 标题
        title: {
          show: this.control.showMapTitle,
          text: this.control.mapTitle,
        },
        // 几个图例
        legend: {
          data: ['noMap', 'top4', "top5To8", "top9To12", "top13To18"]
        },
        // 鼠标悬浮提示
        tooltip: {
          show: true,
          triger: 'item',
          formatter: function (params, ticket, callback) {
            if (params.componentType == "series") {
              return params.data ? params.data.name + this.control.tooltipCenterContent + params.data.value : params.name + this.control.tooltipCenterContent + "0";
            } else if (params.componentType == "markPoint") {
              return params.data ? params.data.name + this.control.tooltipCenterContent + optionMap.series[0].data[0].value : params.name + this.control.tooltipCenterContent + "0";
            }
          }
        },
        visualMap: {
          //这里为地图图例
          left: 20,
          bottom: 20,
          show: true,
          // inRange: {
          // 	color: ['#FF2627', '#FDAB24', '#ABFE28', '#29E9FE'], //图例色域
          // },
          selectedMode: false,
          pieces: this.control.visualMapList,
        },
        series: [
          {
            type: "map",
            map: "henan",
            aspectScale: 0.85, //用于 scale 地图的长宽比，如果设置了projection则无效
            zoom: 1.21, //当前视角的缩放比例
            roam: false, //是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
            dragEnable: false, // 地图是否可通过鼠标拖拽平移，默认为true
            zoomEnable: true, //地图是否可缩放，默认值为true
            clickable: false,
            label: {
              normal: {
                show: true,
              },
              emphasis: {
                show: true,
              },
            },
            itemStyle: {
              //地图区域的多边形 图形样式。
              normal: {
                areaColor: "#D2EEFC",
                borderColor: "#75C9F5",
                borderWidth: 1.5,
              },
              emphasis: {
                areaColor: "#3DBAFD", //鼠标经过区域颜色
                label: {
                  //鼠标经过区域内文字样式
                  show: true,
                  textStyle: {color: "#333", fontSize: 14, fontWeight: 700},
                },
              },
            },
            data: [],
            nameMap: {
              '郑州市': '郑州分公司',
              '开封市': '开封分公司',
              '洛阳市': '洛阳分公司',
              '平顶山市': '平顶山分公司',
              '安阳市': '安阳分公司',
              '鹤壁市': '鹤壁分公司',
              '新乡市': '新乡分公司',
              '焦作市': '焦作分公司',
              '濮阳市': '濮阳分公司',
              '许昌市': '许昌分公司',
              '漯河市': '漯河分公司',
              '三门峡市': '三门峡分公司',
              '南阳市': '南阳分公司',
              '商丘市': '商丘分公司',
              '信阳市': '信阳分公司',
              '周口市': '周口分公司',
              '驻马店市': '驻马店分公司',
              '济源市': '济源分公司',
            },
            markPoint: {
              symbol: "triangle", // 标记点的图形
              symbolSize: 12, // 标记点的大小
              itemStyle: {
                color: "red", // 标记点的颜色
              },
              data: [],
            },
          },
        ],
      }
      echarts.registerMap('henan', henanData)
      if (this.control.isShowProvincialCompany) {
        optionMap.series[0].markPoint.data[0] = {
          name: "省公司",
          branchCode: '4772338661636601428',
          coord: [113.535912, 34.757975],
          label: {
            show: true, // 显示标记点的文本
            formatter: "{b}", // 标记点文本的格式化字符串，这里使用{name}表示取数据项的name属性
            position: "right", // 标记点文本的位置，可以是'top'、'bottom'、'left'、'right'等
          },
        }
      } else {
        optionMap.series[0].markPoint.data = optionMap.series[0].markPoint.data.filter(item => item.name !== '省公司')
      }
      let res = {}
      try {
        res = await this.getData()
      } catch (e) {
        this.mapChart.hideLoading()
      }
      const cityData = res.data; //一个分类好的对象
      let mergedData = [];
      for (let category in cityData) {
        cityData[category].forEach(function (item) {
          item.category = category; // 添加 category 属性以便在视觉映射中使用
          item.value = item.num
          mergedData.push(item);
        });
      }
      optionMap.series[0].data = mergedData
      this.mapChart.setOption(optionMap)
    },
    handelAPI() {
      const {type, reqUrl} = this.control.api;
      let params = {};
      this.control.api.params.forEach((item) => {
        params[item.key] = item.value;
      });
      let data = {};
      if (this.control.api.radio === "form-data") {
        this.control.api.body.listValue.forEach((item) => {
          data[item.key] = item.value;
        });
      } else {
        const list = JSON.parse(
            JSON.stringify(this.control.api.body.jsonValue)
        );
        list.forEach((item) => {
          data[item.key] = item.value;
        });
      }
      return {
        type,
        reqUrl,
        params,
        data
      }
    },
    async getData(nowArea) {
      const {type, reqUrl, params, data} = this.handelAPI()
      return request({
        method: type,
        url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}`),
        params: params,
        data: data,
        contentType: "application/json;charset=UTF-8",
      });
    }
  },

}
</script>
