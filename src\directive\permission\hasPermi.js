import store from '@/store'

const hasPermi = (el, binding, vnode)=> {
  const { value } = binding
  const all_permission = "*:*:*"; // 全部可以访问
  const permissions = store.getters && store.getters.permissions
  console.log('permissions', permissions)

  if (value && value instanceof Array && value.length > 0) {
    const permissionFlag = value

    const hasPermissions = permissions.some(permission => {
      return all_permission === permission || permissionFlag.includes(permission)
    })

    if (!hasPermissions) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  } else {
    throw new Error(`请设置操作权限标签值`)
  }
}
export default hasPermi