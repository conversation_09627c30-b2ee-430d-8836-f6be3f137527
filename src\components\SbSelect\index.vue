<template>
    <div
        class="sbSelectDiv"
        :style="'line-height:' + height + 'px;'"
        v-clickoutside="handlerClose"
    >
        <div class="sbSelectTit" @click="showSelectOpts = !showSelectOpts">
            <span>{{ isChooseNames || name }}</span
            ><a class="arrDown"><svg-icon icon-class="arrDown"></svg-icon></a>
        </div>
        <div
            v-if="showSelectOpts"
            class="sbSelectOpt"
            :style="'top:' + height + 'px;'"
        >
            <div class="sbSelectOptDiv">
                <a
                    v-for="item in chooseOptions"
                    :key="item[option.value]"
                    @click="handlerOption(item)"
                    :class="item.isChoose ? 'a_click' : ''"
                >
                    <span><i></i></span><font>{{ item[option.label] }}</font>
                </a>
            </div>
            <div class="sbSelectOptBtn">
                <a @click="showSelectOpts = false">取消</a
                ><a class="sbSelectOptBtnSure" @click="handlerSureSelectOptBtn"
                    >确认</a
                >
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "SbSelect",
    props: {
        options: {
            type: Array,
            // required:true,
            default: () => {
                return [
                    { name: "男", value: "1" },
                    { name: "女", value: "2" },
                ];
            },
        },
        height: {
            type: String,
            default: "32",
        },
        mulit: {
            type: Boolean,
            default: true,
        },
        value: {
            type: String,
            default: "",
        },
        option: {
            type: Object,
            default: () => {
                return { label: "name", value: "value" };
            },
        },
        name: {
            type: String,
            default: "请选择",
        },
        onOk: {
            type: Function,
        },
        from: {
            type: Boolean,
        },
    },
    data() {
        return {
            showSelectOpts: false,
            chooseOptions: this.options,
            isChooseNames: "",
        };
    },
    created() {
        console.log(this.options);
    },
    methods: {
        handlerClose(e) {
            this.showSelectOpts = false;
        },
        handlerSureSelectOptBtn() {
            let choose = [];
            this.chooseOptions.forEach((item) => {
                if (item.isChoose) choose.push(item[this.option.value]);
            });
            this.$emit("sbSelectChange", choose.join(","));
            this.showSelectOpts = false;
        },
        handlerOption(item) {
            if (this.mulit) {
                let index = this.chooseOptions.findIndex(
                    (ti) => item[this.option.value] == ti[this.option.value]
                );
                this.chooseOptions[index].isChoose = !this.chooseOptions[index]
                    .isChoose;
            } else {
                this.chooseOptions.forEach((ti) => {
                    if (ti[this.option.value] == item[this.option.value]) {
                        ti.isChoose = !ti.isChoose;
                    } else {
                        ti.isChoose = false;
                    }
                });
            }
            this.chooseOptions = JSON.parse(JSON.stringify(this.chooseOptions));
        },
    },
    watch: {
        value: {
            handler: function(newV, oldV) {
                if (newV) {
                    // console.log("值更新啦！",newV);
                    let vals = newV.split(",");
                    let names = [];
                    for (var i in vals) {
                        let index = this.chooseOptions.findIndex(
                            (ti) => ti[this.option.value] == vals[i]
                        );
                        if (index > -1) {
                            this.chooseOptions[index].isChoose = true;
                            names.push(
                                this.chooseOptions[index][this.option.label]
                            );
                        }
                    }
                    this.isChooseNames = names.join(",");
                    this.chooseOptions = JSON.parse(
                        JSON.stringify(this.chooseOptions)
                    );
                }
            },
            deep: true,
            immediate: true,
        },
        options: {
            handler: function(newV, oldV) {
                if (newV) {
                    console.log("值更新啦！", newV);
                    this.chooseOptions = newV;
                }
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>
<style scoped>
.sbSelectDiv {
    width: auto;
    min-width: 90px;
    display: inline-block;
    font-size: 14px;
    color: #606266;
    position: relative;
}
.sbSelectTit {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
}
.sbSelectTit span {
    padding-left: 20px;
}
.sbSelectTit .arrDown {
    padding-left: 5px;
    color: #999999;
}
.sbSelectOpt {
    position: absolute;
    left: 0;
    right: 0;
    background: #fff;
    padding: 8px 8px 5px;
    line-height: 32px;
    transform: ;
}
.sbSelectOptDiv {
    max-height: 260px;
    overflow-y: auto;
}
.sbSelectOptDiv a {
    display: block;
    padding: 2px 5px;
}
.sbSelectOptDiv a span {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 1px solid #333;
    float: left;
    margin: 9px 6px 0 0;
    padding: 3px;
}
.sbSelectOptDiv a span i {
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: #fff;
    display: block;
}
.sbSelectOptDiv a:hover span {
    border-color: #3072f6;
}
.sbSelectOptDiv a.a_click span {
    border-color: #333;
}
.sbSelectOptDiv a.a_click span i {
    background: #333;
}
.sbSelectOptDiv a.a_click:hover span {
    border-color: #3072f6;
}
.sbSelectOptDiv a.a_click:hover span i {
    background: #3072f6;
}
.sbSelectOptBtn {
    padding-top: 5px;
    line-height: 30px;
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
}
.sbSelectOptBtnSure {
    color: #3072f6;
    margin-left: 10px;
}
</style>
