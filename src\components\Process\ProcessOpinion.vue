<template>
  <div class="app-container">
    <sb-el-table :table="table" @getList="getList"></sb-el-table>

    <el-divider v-if="copyOpinionTable">抄报意见</el-divider>
    <sb-el-table :table="table2" v-if="copyOpinionTable" @getList="getList2"></sb-el-table>

  </div>
</template>
<script>
import { getWfOptMags,flowTodoReTracking } from "@/api/process";
export default {
  name: "ProcessOpinion",
  props: {
    gps: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      table: {
        modulName: "processOpinion-查看意见", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [],
        },
        tr: [
          { id: "opinionUser", label: "审批意见人", prop: "opinionUser" },
          { id: "content", label: "审批意见", prop: "content" },
          { id: "taskCreateTime", label: "审批时间", prop: "taskCreateTime"},
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [],
        },
        hasPagination: false,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {}
      },

      copyOpinionTable: false,
      table2: {
        modulName: "processOpinion-抄报意见", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [],
        },
        tr: [
          { id: "recipientName", label: "抄报人", prop: "recipientName" },
          { id: "content", label: "抄报意见", prop: "content" },
          { id: "modifiedTime", label: "抄报时间", prop: "modifiedTime" },
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [],
        },
        hasPagination: false,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {}
      },
      
    };
  },
  created() {
    this.getList();
    this.getList2();
  },
  methods: {
    // 查询列表
    getList() {
      this.table.loading = true;
      getWfOptMags(this.gps.processInstId).then((res) => {
        this.table.loading = false;
        for (var i in res.data) {
          if (res.data[i].belongCompanyName == res.data[i].belongDepartmentName) {
            res.data[i].opinionUser = `${res.data[i].belongCompanyName}/${res.data[i].currentUserName}`;
          } else if (res.data[i].belongOrgName == res.data[i].belongDepartmentName) {
            res.data[i].opinionUser = `${res.data[i].belongCompanyName}/${res.data[i].belongDepartmentName}/${res.data[i].currentUserName}`;
          } else {
            res.data[i].opinionUser = `${res.data[i].belongCompanyName}/${res.data[i].belongDepartmentName}/${res.data[i].belongOrgName}/${res.data[i].currentUserName}`;
          }
        }
        this.table.data = res.data;
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    getList2() {
      this.table2.loading = true;
      flowTodoReTracking(this.gps.processInstId).then((res) => {
        this.table2.loading = false;
        this.table2.data = res.data;
        if(this.table2.data.length > 0){
          this.copyOpinionTable = true
        }else{
          this.copyOpinionTable = false
        }
      }).catch((err) => {
        this.table2.loading = false;
      });
    }
  }
};
</script>
<style scoped>
.app-container {
  padding-bottom: 20px;
}
</style>