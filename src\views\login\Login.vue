<template>
  <div class="login-warpper" v-wechat-title="title">
    <div class="logo_wrap">
      <div class="logo">
        <h4 style="color:#fff;font-weight: bold;">{{tits}}</h4>
      </div>
    </div>
    <div class="login-form-layout">
      <el-form
        autoComplete="on"
        :model="loginForm"
        ref="loginForm"
        :rules="loginFormRules"
        label-position="center"
        class="login-form-warpper"
      >
        <h5 class="txtc login_tit">用户登录</h5>
        <el-form-item prop="username" class="mt32 plf15">
          <el-input
            name="username"
            type="text"
            v-model="loginForm.username"
            autoComplete="on"
            placeholder="请输入用户名"
          >
            <span slot="prefix">
              <img src="./images/user.png" />
            </span>
          </el-input>
        </el-form-item>
        <el-form-item prop="passwordm" class="plf15">
          <el-input
            name="passwordm"
            :type="pwdType"
            v-model="loginForm.passwordm"
            autoComplete="on"
            placeholder="请输入密码"
          >
            <span slot="prefix">
              <img src="./images/lock.png" />
            </span>
            <span slot="suffix" @click="showPwd">
              <svg-icon icon-class="chakan" class-name="chakan"></svg-icon>
            </span>
          </el-input>
        </el-form-item>
        <div class="plf15 of_hidden">
          <el-form-item prop="verifyCode" style="width:220px;" class="fl">
            <el-input
              name="verifyCode"
              type="text"
              v-model="loginForm.verifyCode"
              @keyup.enter.native="handleLogin"
              autoComplete="on"
              placeholder="请输入验证码"
            >
              <span slot="prefix">
                <img src="./images/code.png" />
              </span>
            </el-input>
          </el-form-item>
          <img
            width="115"
            class="fr"
            height="40"
            id="captcha"
            @click="getCode"
            :src="imgCode"
            title="点击更换"
          />
        </div>
        <el-form-item class="plf15">
          <el-button
            class="w100"
            type="primary"
            :loading="loading"
            @click.native.prevent="handleLogin"
            >登录</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <!--<div class="copy">©2017-2027&nbsp;中国移动通信集团河南有限公司&nbsp;&nbsp;|&nbsp;&nbsp;联系支持电话：0371-60880301转1&nbsp;&nbsp;|&nbsp;&nbsp;邮箱：<EMAIL></div>-->
  </div>
</template>
<script>
import util from "@/assets/js/public";
import { captcha } from "@/api/login.js";
import store from "@/store";
export default {
  name: "login",
  computed: {
    title: function() {
      return this.$route.meta.title + "-" + process.env.VUE_APP_APPNAME;
    }
  },
  data() {
    let imgSrc;
    if (process.env.NODE_ENV == "development") {
      //开发环境
      // imgSrc = process.env.VUE_APP_DEVBASEURL;
      imgSrc = window.location.origin+'/' + process.env.VUE_APP_APPCODE;
    } else if (process.env.NODE_ENV == "debug") {
      //测试环境
      imgSrc = process.env.VUE_APP_DEBBASEURL;
    } else if (process.env.NODE_ENV == "production") {
      //生产环境
      imgSrc = process.env.VUE_APP_PROBASEURL;
    }
    return {
      imgCode: imgSrc+"/captcha", 
      loginForm: {
        username:'',
        passwordm:'',
        verifyCode:''
      },
      loginFormRules: this.getformrules({
        username: { required: true },
        passwordm: { required: true },
        verifyCode: { required: true }
      }),
      loading: false,
      pwdType: "password",
      tits: (process.env.VUE_APP_APPNAME).replace("系统","") + '系统',
    };
  },
  created() {
    // this.captchaFun();
    this.getCode();
  },
  methods: {
    captchaFun() {
      captcha().then(res => {
        let url = window.URL.createObjectURL(res.data);
        this.imgCode = url;
        // this.util.blobDownload(res.data,"gy.jpg");
      });
    },
    showPwd() {
      if (this.pwdType === "password") {
        this.pwdType = "";
      } else {
        this.pwdType = "password";
      }
    },
    getCode() {
      this.imgCode = this.imgCode.split("?")[0]+"?tm="+(new Date()).getTime();
      // this.captchaFun();
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          this.loginForm.password = util.encrypt(this.loginForm.passwordm);
          this.$store.dispatch("Login", this.loginForm).then(res => {
              this.loading = false;
              if(process.env.VUE_APP_flow && process.env.VUE_APP_flow  == 'noflow'){
                // this.$router.push({path: "/welcome"});
                this.$router.push({path: "/workBench/workBench_form"});
              }else{
                this.$router.push({path: "/workBench/workBench_form"});
              }
            }).catch(() => {
              this.loading = false;
              this.passwordm = "";
              this.verifyCode = "";
              this.getCode();
            });
        }else{
          return false;
        }
      });
    }
  }
};
</script>
<style scoped>
.el-form-item {
  margin-bottom: 8px;
}
.login-warpper /deep/ .el-form-item__error{
  top: 70%;
}
.login-warpper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  z-index: 999;
  background: url("./images/login_bg.jpg") center 0 no-repeat;
  background-size: cover;
}
.login-form-layout {
  background: transparent url(./images/login_t_btm.png) no-repeat scroll center center;
  bottom: 0;
  height: 382px;
  left: 0;
  position: absolute;
  width: 100%;
  padding: 15px;
}
.logo_wrap {
  background: transparent url(./images/login_tab_bg.png) repeat-x;
  width: 100%;
  min-height: 20px;
  padding-top: 20px;
  text-align: center;
}
.logo {
  width: 360px;
  height: 48px;
  background: transparent url(./images/login_t_tx.png) repeat-x center;
  margin: 0 auto;
}
.login-form-warpper {
  width: 380px;
  margin: 0 auto;
  line-height: 30px;
}
.login_tit {
  color: #fff;
  font-size: 26px;
  margin-bottom: 20px;
}
.chakan{
  color: #aaa;
}
.copy {
  bottom: 12px;
  left: 0;
  position: absolute;
  width: 100%;
  height: auto;
  color: #003758;
  text-align: center;
  padding: 9px 0px;
  line-height: 20px;
  font-size: 12px;
  z-index: 8;
}
</style>
