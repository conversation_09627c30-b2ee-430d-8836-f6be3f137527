<template>
	<div :class="gps.location?'w99':'p10'">
		<process-btn ref="processBtn" :gps="gps" :processBtn="processBtn" :formData="appFormValue" :dialogClose="dialogClose" :on-ok="handleDoFun"></process-btn>

		<div class="tip">提示：1、临时性事项仅开展一次且时间不超过一周；2、常态化开展事项需要选择周期（至少一周）和频次。</div>

		<!-- 业务表单 -->
		<div class="message tableForm">
			<div class="orderTitle" style>网格日常事务申请</div>
			<sb-el-form ref="appForm" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled" @handleTaskType="handleTaskType" @handleWorkNature="handleWorkNature" @getTaskType="getTaskType" @getWorkNature="getWorkNature" @handleStartTime="handleStartTime" :on-ok="handleDoFun">
				<template v-slot:frequency>
					<div class="frequencyBox">
						<p>每</p>
						<el-input v-model="num1" type="number" :min="1" placeholder></el-input>
						<el-select v-model="frequencyValue" placeholder="请选择">
							<el-option v-for="item in frequencyList" :key="item.value" :label="item.label" :value="item.value"></el-option>
						</el-select>
						<el-input v-model="num2" type="number" :min="1" placeholder></el-input>
						<p>次</p>
					</div>
				</template>
			</sb-el-form>
		</div>
	</div>
</template>
<script>
	import store from "@/store";
	import util from "@/assets/js/public";
	import ProcessBtn from "@/components/Process/ProcessBtn";
	import { getDictList } from "@/api/public";
	import { deleteDraft } from "@/api/process";
	import {
		getProcessInfo,
		getFormDetail,
		saveDraft,
		startProcess
	} from "@/api/apply/application";

	let defaultAppFormValue = {
		pmInsId: "",
		id: "",
		// applyUser: store.getters.user.truename,
		// applyUserName: store.getters.user.username,
		// belongCompanyName: store.getters.user.belongCompanyName,
		// belongDepartmentName: store.getters.user.belongDepartmentName,
		// applyTime: util.getNow(),
		blank: "blank"
	};

	export default {
		name: "application",
		components: { ProcessBtn },
		props: {
			href: {
				type: Object,
				default () {
					return {};
				}
			},
			// 关闭
			dialogClose: {
				type: Function
			}
		},
		data() {
			return {
				gps: this.href,
				processDefKey: "wgrcsw_process",
				processBtn: {
					optClose: false
				},

				processD: false,
				pnKey: 0,
				clickFlag: true, //防止多次点击

				nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

				// 业务表单
				initValue: {},
				appFormValue: Object.assign({}, defaultAppFormValue),
				appForm: {
					formDisabled: false,
					labelWidth: "150px",
					inline: true,
					formItemList: [{
							class: "c12",
							label: "",
							key: "scoreVal",
							placeholder: "系统自动生成，无需填写",
							disabled: true
						},
						{
							class: "c12",
							label: "工单编号",
							key: "workNumber",
							type: "input",
							placeholder: "系统自动生成，无需填写",
							disabled: true
						},
						{
							class: "c12",
							label: "工单标题",
							key: "title",
							type: "input",
							rule: { required: true }
						},
						{
							class: "c4",
							label: "申请人",
							key: "applyUser",
							type: "input",
							disabled: true,
							rule: { required: true }
						},
						{
							class: "c4",
							label: "申请人OA",
							key: "applyUserName",
							type: "input",
							disabled: true,
							show: false
						},
						{
							class: "c4",
							label: "申请单位",
							key: "belongCompanyName",
							type: "input",
							disabled: true,
							rule: { required: true }
						},
						{
							class: "c4",
							label: "申请部门",
							key: "belongDepartmentName",
							type: "input",
							disabled: true,
							rule: { required: true }
						},
						{
							class: "c4",
							label: "申请时间",
							key: "applyTime",
							type: "date",
							subtype: "date",
							valueFormat: "yyyy-MM-dd hh:mm:ss",
							disabled: true,
							rule: { required: true }
						},
						{
							class: "c4",
							label: "任务类型",
							key: "taskTypeValue",
							type: "select",
							dictType: "taskType",
							fun: "getTaskType",
							changeFun: "handleTaskType",
							rule: { required: true }
						},
						{
							class: "c4",
							label: "任务类型名称",
							key: "taskTypeName",
							type: "input",
							disabled: true,
							show: false
						},
						{
							class: "c4",
							label: "工作性质",
							key: "workNatureValue",
							type: "select",
							dictType: "workNature",
							fun: "getWorkNature",
							changeFun: "handleWorkNature",
							rule: { required: true }
						},
						{
							class: "c4",
							label: "工作性质名称",
							key: "workNatureName",
							type: "input",
							show: false
						},
						{
							class: "c4",
							label: "开始时间",
							key: "startTime",
							type: "date",
							subtype: "date",
							valueFormat: "yyyy-MM-dd",
							disabledDate: "afterDate",
							changeFun: "handleStartTime",
							rule: { required: true }
						},
						{
							class: "c4",
							label: "结束时间",
							key: "endTime",
							type: "date",
							subtype: "date",
							valueFormat: "yyyy-MM-dd",
							rule: { required: true }
						},
						{
							class: "c4",
							label: "",
							key: "blank",
							type: "text",
							value: "blank"
						},
						{
							class: "c4",
							label: "频次",
							key: "frequency",
							type: "template",
							template: "frequency",
							show: false
						},
						{
							class: "c12",
							label: "下派网格事务说明",
							key: "inferiorFactionIllustration",
							type: "input",
							inputType: "textarea",
							rule: { required: true, maxlength: 2000 }
						}
					]
				},
				taskTypeList: [],
				workNatureList: [],
				// 频次
				num1: 1,
				num2: 1,
				frequencyList: [
					{ label: "日", value: "日" },
					{ label: "周", value: "周" },
					{ label: "月", value: "月" },
					{ label: "年", value: "年" }
				],
				frequencyValue: "日"
			};
		},
		created() {
			var query = this.util.getQueryString();
			this.gps = Object.assign(this.gps, query);
			// console.log("gps", JSON.parse(JSON.stringify(this.gps)));

			this.initValue = {
				applyUser: this.$store.getters.user.truename,
				applyUserName: this.$store.getters.user.username,
				belongCompanyName: this.$store.getters.user.belongCompanyName,
				belongDepartmentName: this.$store.getters.user.belongDepartmentName,
				applyTime: this.nowTime
			};
			this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);

			this.initFun(); //初始化
		},
		methods: {
			// 初始化
			initFun() {
				this.gps.processDefKey = this.processDefKey;

				// 起草及草稿不显示“工单编号”
				// if(!this.gps.location || this.gps.location=="wgrcsw.start"){
				// 	var index = this.appForm.formItemList.findIndex(item => item.key==="workNumber");
				// 	if(index > -1){
				// 		this.appForm.formItemList[index].show = false;
				// 	}
				// 	this.appFormValue = JSON.parse(JSON.stringify(this.appFormValue));
				// }
				// 加载表单
				if (this.gps.location || (this.gps.action && this.gps.action == "read")) {
					this.loadForm();
				}
			},

			// 获取工单详情
			loadForm() {
				var data = {
					pmInsId: this.gps.pmInsId,
					processDefKey: this.gps.processDefKey
				};
				getFormDetail(data).then(res => {
					this.appFormValue = res.data;

					// 选择“常态化开展事项”时展示“频次”
					var index1 = this.appForm.formItemList.findIndex(
						item => item.key === "frequency"
					);
					if (index1 > -1) {
						this.appForm.formItemList[index1].show =
							res.data.workNatureValue == "normalcy" ? true : false;
					}
					var index2 = this.appForm.formItemList.findIndex(
						item => item.key === "blank"
					);
					if (index2 > -1) {
						this.appForm.formItemList[index2].show =
							res.data.workNatureValue == "normalcy" ? false : true;
					}

					var index3 = this.appForm.formItemList.findIndex(
						item => item.key === "endTime"
					);
					if (index3 > -1) {
						if (this.appFormValue.workNatureValue == "normalcy") {
							// 常态化开展事项情况
							this.appForm.formItemList[index3].disabledDate = "afterNumDate";
							this.appForm.formItemList[index3].dateNum =
								this.appFormValue.startTime + "#" + "7";
						} else {
							// 临时性事务情况
							this.appForm.formItemList[index3].disabledDate =
								"afterDateBeforeNum";
							this.appForm.formItemList[index3].dateNum =
								this.appFormValue.startTime + "#" + "7";
						}
						this.appForm = JSON.parse(JSON.stringify(this.appForm));
					}

					// 频次赋值
					if (this.appFormValue.frequency) {
						var frequencyArry = this.appFormValue.frequency.split("#");
						this.num1 = frequencyArry[0];
						this.frequencyValue = frequencyArry[1];
						this.num2 = frequencyArry[2];
					}
					// 占位
					this.appFormValue.blank = "blank";

					// 设置只读
					if (
						(this.gps.type != "draft" && this.gps.type != "task") ||
						this.gps.location != "wgrcsw.start"
					) {
						this.appForm.formDisabled = true;
					} else {
						this.appForm.formDisabled = false;
					}
				});
			},

			// 获取任务类型下拉列表
			getTaskType(obj) {
				getDictList("taskType").then(res => {
					this.taskTypeList = res.data;
				});
			},

			// 选择任务类型
			handleTaskType(obj, value) {
				var index = this.taskTypeList.findIndex(item => item.value === value);
				if (index > -1) {
					this.appFormValue["taskTypeName"] = this.taskTypeList[index].name;
				}
			},

			// 获取工作性质下拉列表
			getWorkNature(obj) {
				getDictList("workNature").then(res => {
					this.workNatureList = res.data;
				});
			},

			// 选择工作性质
			handleWorkNature(obj, value) {
				// 选择“常态化开展事项”时展示“频次”
				var index1 = this.appForm.formItemList.findIndex(
					item => item.key === "frequency"
				);
				if (index1 > -1) {
					this.appForm.formItemList[index1].show =
						value == "normalcy" ? true : false;
				}
				var index2 = this.appForm.formItemList.findIndex(
					item => item.key === "blank"
				);
				if (index2 > -1) {
					this.appForm.formItemList[index2].show =
						value == "normalcy" ? false : true;
				}

				var index = this.workNatureList.findIndex(item => item.value === value);
				if (index > -1) {
					this.appFormValue["workNatureName"] = this.workNatureList[index].name;
				}

				this.handleStartTime();
			},

			// 获取结束时间可选范围
			handleStartTime(obj, value) {
				if (this.appFormValue.workNatureValue && this.appFormValue.startTime) {
					this.appFormValue.endTime = "";
					var index = this.appForm.formItemList.findIndex(
						item => item.key === "endTime"
					);
					if (index > -1) {
						if (this.appFormValue.workNatureValue == "normalcy") {
							// 常态化开展事项情况
							this.appForm.formItemList[index].disabledDate = "afterNumDate";
							this.appForm.formItemList[index].dateNum =
								this.appFormValue.startTime + "#" + "7";
						} else {
							// 临时性事务情况
							this.appForm.formItemList[index].disabledDate =
								"afterDateBeforeNum";
							this.appForm.formItemList[index].dateNum =
								this.appFormValue.startTime + "#" + "7";
						}
						this.appForm = JSON.parse(JSON.stringify(this.appForm));
					}
				}
			},

			// 重置表单
			handleFormReset() {
				this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);
			},

			beforeSubmit() {},

			// 流转下一步
			handleNextBtn() {
				this.$refs["appForm"].$children[0].validate(valid => {
					if (!valid) {
						this.$message({
							message: "表单数据校验不通过",
							type: "warning",
							duration: 1500
						});
						return false;
					} else {
						// 频次
						if (
							this.appFormValue.workNatureValue == "normalcy" &&
							(!this.num1 || !this.num2 || !this.frequencyValue)
						) {
							this.$message({
								message: "请选择频次",
								type: "warning",
								duration: 1500
							});
							return false;
						} else {
							this.appFormValue.frequency =
								this.num1 + "#" + this.frequencyValue + "#" + this.num2;
							this.$refs["processBtn"].doProcessNext();
						}
					}
				});
			},

			// 保存草稿
			handleSaveDraft() {
				this.$refs["appForm"].$children[0].validate(valid => {
					if (!valid) {
						this.$message({
							message: "表单数据校验不通过",
							type: "warning",
							duration: 1500
						});
						return false;
					} else {
						if (this.clickFlag) {
							this.clickFlag = false;
							// 频次
							if (
								this.appFormValue.workNatureValue == "normalcy" &&
								(!this.num1 || !this.num2 || !this.frequencyValue)
							) {
								this.$message({
									message: "请选择频次",
									type: "warning",
									duration: 1500
								});
								return false;
							} else {
								this.appFormValue.frequency =
									this.num1 + "#" + this.frequencyValue + "#" + this.num2;

								saveDraft({
										processDefKey: this.processDefKey,
										title: this.appFormValue.title,
										formData: this.appFormValue
									})
									.then(res => {
										this.clickFlag = true;
										if (!this.gps.location) {
											this.$router.push({ name: "processDraft" });
										} else {
											this.dialogClose();
										}
									})
									.catch(err => {
										this.clickFlag = true;
									});
							}
						}
					}
				});
			},

			// 废除草稿
			handleAbolish() {
				if (this.clickFlag) {
					this.clickFlag = false;
					deleteDraft({
							pmInsId: this.gps.pmInsId,
							processDefKey: this.processDefKey
						})
						.then(res => {
							this.clickFlag = false;
							this.dialogClose();
						})
						.catch(err => {
							this.clickFlag = true;
						});
				}
			},

			handleDoFun(obj, fun, data) {
				//若一个beforeFun可直接在这个函数里面写
				let n;
				if (obj) {
					n = this[obj[fun]].call(this, obj, data);
				} else {
					n = this[fun].call(this, data);
				}
				return n;
			}
		}
	};
</script>
<style>
	.w99 {
		width: 99%;
		margin: 0 auto;
	}

	.p10 {
		padding: 15px;
	}

	.tip {
		font-size: 13px;
		color: #a5a5a5;
		margin: 20px 0 10px;
	}

	.frequencyBox {
		display: flex;
		justify-content: space-around;
	}

	.frequencyBox p {
		width: 15%;
		line-height: 32px;
		text-align: center;
	}

	.frequencyBox>.el-input {
		width: 24%;
	}

	.frequencyBox>.el-input .el-input__inner {
		padding: 0 4px;
		text-align: center;
		border: 1px solid #dcdfe6;
	}

	.frequencyBox>.el-select {
		width: 29%;
	}

	.frequencyBox>.el-select .el-input__inner {
		padding: 0 4px 0 6px;
		border: 1px solid #dcdfe6;
	}
</style>