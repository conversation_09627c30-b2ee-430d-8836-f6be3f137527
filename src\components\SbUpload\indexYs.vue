<template>
    <div class="upload_D"    
        v-loading.fullscreen.lock="loading" 
        element-loading-text="请稍后，正在上传..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(255, 255, 255, 0.5)"><!-- v-bind="$attrs" v-on="$listeners"-->
        <div v-if="!upload.disabled" class="upload_Btn">
            <input ref="sbUpload" type='file' :multiple="upload.multiple?'multiple':false" :accept="(upload.listType && upload.listType.indexOf('picture')>-1)?'image/*':(upload.accept || '*')" @change="chooseFile"/>
            <el-button class="uploadB" :size="upload.size || 'small'" type="primary">{{upload.btnText || '文件上传'}}</el-button>
        </div>
        <div v-else>
            <span style="color:#C0C4CC;" v-if="!upload.filelist[0]">{{'暂无'+upload.label}}</span>
        </div>
        <div class="clear"></div>
        <ul v-if="!upload.listType || (upload.listType && upload.listType!=='data')" :class="'el-upload-list'+((upload.listType && upload.listType.indexOf('picture')>-1)?' el-upload-list--picture-card':'')">
            <li v-for="(item,index) in fileList" :key="index" class="el-upload-list__item is-success"> 
                <img v-if="upload.listType && upload.listType.indexOf('picture')>-1" :src="handleImgUrl(item)" class="el-upload-list__item-thumbnail"/>
                <a v-if="!upload.listType || upload.listType==='text'" @click="handleFileToSee(item,index)" class="el-upload-list__item-name"><i class="el-icon-document"></i>{{item.fileName}}</a>
                <label class="el-upload-list__item-status-label"><i :class="'el-icon-upload-success '+((upload.listType && upload.listType.indexOf('picture')>-1)?'el-icon-check':'el-icon-circle-check')"></i></label>
                <i v-if="(!upload.listType || upload.listType==='text') && (!upload.disabled)" class="el-icon-close" @click="handleRemove(item,index)"></i>
                <span v-if="(upload.listType && upload.listType.indexOf('picture')>-1)" class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handleImgToBig(item,index)"><i class="el-icon-zoom-in"></i></span>
                    <span v-if="!upload.disabled" class="el-upload-list__item-delete" @click="handleRemove(item,index)"><i class="el-icon-delete"></i></span>
                </span>
                <div v-if="upload.showName" style="font-size:12px;">{{item.fileName}}</div>
            </li>
        </ul>
        <el-dialog destroy-on-close v-dialogDrag :visible.sync="imgToBig" width="60%" append-to-body>
            <img style='width:100%;' :src="imgToSeeUrl"/>
        </el-dialog>
    </div>
</template>
<script>
import {uploadProcessFiles} from "@/api/public";
import EXIF from 'exif-js';
import { Loading } from 'element-ui';
export default {
    name:'SbUpload',
    props:{
        upload:{
            type:Object,
            required:true
        },
        onOk:{
            type:Function
        },
        from:{
            type:Boolean
        }        
    },
    data(){
        return {
            imgToBig:false,
            imgToSeeUrl:"",
            fileList:this.upload.filelist || [],
            reqs:[],
            valide:false,
            loading:false
        }
    },
    created(){
        //Loading.service({text:"文件正在上传，请稍候！"});
    },
    methods:{
        handleImgUrl(item){
            if(typeof item==='object'){
                //return '/'+process.env.VUE_APP_APPCODE+item.downLoadUrl;
                return item.anonymousFilePath;
            }
            //return '/'+process.env.VUE_APP_APPCODE+item;
            return item;
        },
        handleImgToBig(item,index){            
            if(typeof item==='object'){                
                //this.imgToSeeUrl='/'+process.env.VUE_APP_APPCODE+item.downLoadUrl;
                this.imgToSeeUrl=item.anonymousFilePath;
            }else{
                //this.imgToSeeUrl='/'+process.env.VUE_APP_APPCODE+item;
                this.imgToSeeUrl=item;
            }
            this.imgToBig=true;
        },
        handleFileToSee(item,index){
            //if(process.env.VUE_APP_FILEOPEN && process.env.VUE_APP_FILEOPEN==='false') 
                //this.util.fileDownload(item.id);
            //else
                //this.util.fileOpen(item.id);
        },
        handleInof(){
            let info={has:false};
            let infoA=[];
            if (this.upload.listType && this.upload.listType==='picture'){
                infoA.push('只能上传图片');
            }
            if(this.upload.filesize){
                infoA.push('上传文件大小不能超过'+(this.upload.filesize || 10240)+'kb');
            }
            info.info=infoA.join(";")
            return info;
        },
        chooseFile(e){
            this.loading=true;
            let etf=e.target.files,et=e.target; 
            if(this.upload.accept){
				var len=etf.length;
				for(var i=0;i<len;i++){
					if(i<len) {
						var fileExtensionArray = etf[i].name.split('.');
                        var fileExtension = fileExtensionArray[fileExtensionArray.length - 1].toLowerCase();
						if (this.upload.accept.indexOf(fileExtension) == -1 && etf[i].name != "") {
							this.$message({
                                type: 'error',
                                message:'只能上传后缀名为'+this.upload.accept+'的文件！'
                            });
                            //清空上传文件框
                            e.target.vlaue="";
                            this.valide=false;
                            this.loading=false;
                            return;
						}
					}
				}       
            }
            let cFL=etf.length+this.fileList.length;
            if(cFL>(this.upload.limit || 20)){// || (this.upload.limit && cFL>this.upload.limit)
                this.$message({
                    type: 'error',
                    message:'最多允许上传'+(this.upload.limit || 20)+'个文件！'
                });
                //清空上传文件框
                e.target.vlaue="";
                this.valide=false;
                this.loading=false;
                return;
            }   
            if(this.upload.imgmaxSize || this.upload.imgminSize){
                for(let i=0;i<etf.length;i++){      
                    if(etf[i].size>(this.upload.imgmaxSize*1024)){  
                        let info='上传文件大小不能超过'+this.upload.imgmaxSize+'kb';
                        if(this.upload.imgminSize) info+=("，不能低于"+this.upload.imgminSize+"kb");                  
                        this.$message({
                            type: 'error',
                            message:info
                        });
                        //清空上传文件框
                        e.target.vlaue="";
                        this.valide=false;
                        this.loading=false;
                        return;
                    } 
                    if(etf[i].size<(this.upload.imgminSize*1024)){    
                        let info="上传文件大小";
                        if(this.upload.imgmaxSize) info="不能超过"+this.upload.imgmaxSize+"kb，";
                        info+=('不能低于'+this.upload.imgminSize+'kb');                                     
                        this.$message({
                            type: 'error',
                            message:info
                        });
                        //清空上传文件框
                        e.target.vlaue="";
                        this.valide=false;
                        this.loading=false;
                        return;
					} 
                }
            }  
            // if(this.upload.filesize && (this.upload.oversizedo===false || (!this.upload.oversizedo))){
            //     for(let i=0;i<etf.length;i++){
            //         if(etf[i].size>(this.upload.filesize*1024)){                        
            //             this.$message({
            //                 type: 'error',
            //                 message:'上传文件大小不能超过'+(this.upload.filesize || 10240)+'kb！'
            //             });
            //             //清空上传文件框
            //             e.target.vlaue="";
            //             this.valide=false;
            //             return;
            //         }
            //     }
            // }  
            this.valide=true;   
            this.loading=true;	       
			let imgwhL=0,imgL=true;	
			if(this.upload.listType && (this.upload.listType==='picture' || this.upload.listType==='picture-card')){
				this.valide=false;
                this.loading=true;
				for (let fi = 0; fi < etf.length; fi++) {
					let filei = etf[fi];
                    if (/\/(?:jpeg|png)/i.test(filei.type)) {//如果是图片 就判断                    
                        let that=this,uploadT=this.upload;
                        EXIF.getData(filei,function(){
                            // EXIF.getAllTags(this);
                            let Orientation = EXIF.getTag(this, 'Orientation');                        
                            let fr = new FileReader();
						    fr.onload = function (e) {
						    	let imgTit=["上传图片"];
						    	let IMG = new Image();
						    	IMG.src = this.result;//读出来的文件流
						    	IMG.onload = function () {
                                    let w = this.naturalWidth, h = this.naturalHeight, resizeW = this.naturalWidth, resizeH = this.naturalHeight;                                    
								    imgwhL++;
                                    //将文件流画到canvas画布上
			    				    // let canvas = document.createElement('canvas'),
                                    // ctx = canvas.getContext('2d');                                  
                                    switch(Orientation){
                                        case 1://0°
                                            break;
                                        case 3://180°
                                            // ctx.rorate(180 * Math.PI / 180);
                                            break;
                                        case 6://顺时针90°
                                            resizeW=h;
                                            resizeH=w;
                                            // ctx.rorate(90 * Math.PI / 180);
                                            break;
                                        case 8://逆时针90°
                                            resizeW=h;
                                            resizeH=w;
                                            // ctx.rorate(-90 * Math.PI / 180);
                                            break;
                                    }
                                    // canvas.width = resizeW;
                                    // canvas.height = resizeH;
                                    // ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                    if(that.upload.imgmaxWidth || that.upload.imgmaxHeight || that.upload.imgminWidth || that.upload.imgminHeight || that.upload.imgWidth || that.upload.imgHeight){
                                        that.valide=false;	
                                        if(uploadT.imgmaxWidth && resizeW>uploadT.imgmaxWidth){
                                            that.valide=false;
                                            imgL=false;
                                            imgTit.push("宽度不能超过"+uploadT.imgmaxWidth+"px");
                                        } 
                                        if(uploadT.imgmaxHeight && resizeH>uploadT.imgmaxHeight){
                                            that.valide=false;	
                                            imgL=false;
                                            imgTit.push("高度不能超过"+uploadT.imgmaxHeight+"px");
                                        }	
                                        if(uploadT.imgminWidth && resizeW<uploadT.imgminWidth){
                                            that.valide=false;	
                                            imgL=false;
                                            imgTit.push("宽度不能小于"+uploadT.imgminWidth+"px");
                                        } 
                                        if(uploadT.imgminHeight && resizeH<uploadT.imgminHeight){
                                            that.valide=false;	
                                            imgL=false;
                                            imgTit.push("高度不能小于"+uploadT.imgminHeight+"px");
                                        }	
                                        if(uploadT.imgWidth && resizeW==uploadT.imgWidth){
                                            that.valide=false;	
                                            imgL=false;
                                            imgTit.push("宽度为"+uploadT.imgWidth+"px");
                                        } 
                                        if(uploadT.imgHeight && resizeH==uploadT.imgHeight){
                                            that.valide=false;	
                                            imgL=false;
                                            imgTit.push("高度为"+uploadT.imgHeight+"px");
                                        }	
                                    }else{
                                        that.valide=true;	
                                    }
                                    if(imgL && imgwhL==etf.length){
                                        that.valide=true;
                                        that.chooseDoUpload(etf);
                                    }else{
                                        if(imgwhL==etf.length){
                                            that.$message({
                                                type: 'error',
                                                message:imgTit.join("")
                                            });
                                            et.vlaue="";
                                            that.valide=false;
                                            that.loading=false;
                                        } 										
                                    }							
                                };//end imgload
						    };//end frload
						    fr.readAsDataURL(filei);
                        });
					}
				}
			}else{
				this.valide=true;	
			}
            if(this.valide) this.chooseDoUpload(etf);     
        },
        chooseDoUpload(files){ 
            this.loading=true;	
            if(this.valide){   
                if (files.length === 0) { return; }          
                if (!this.upload.multiple) { files = [files[0]]; }
                const { uid } = files;
                const options = {
                  files: files,
                  onProgress: e => {
                    //this.onProgress(e, rawFile);
                  },
                  onSuccess: res => {
                    //this.onSuccess(res, rawFile);                    
                    this.handleSuccess(res, files);
                    delete this.reqs[uid];
                  },
                  onError: err => {
                    //this.onError(err, rawFile);
                    this.handleError(err, files);
                    delete this.reqs[uid];
                  }
                };
                const req = this.uploadfiles(files,options);
                this.reqs[uid] = req;
                if (req && req.then) {
                  req.then(options.onSuccess, options.onError).catch(error => { });
                }
            }
        },
        handleError(err, file){
            this.valide=true;
            this.loading=false;	 
            // if(err){
            //     this.$message({
            //         message:err || '上传失败',
            //         type:'error',
            //         duration: 3000
            //     })
            // }
        },
        handleValueType(){
            return {valueType:this.upload.listType && this.upload.listType.indexOf('picture')>-1 && (this.upload.valueType && this.upload.valueType==='string')}
        },
        handleSuccess(response, file){
            this.valide=true; 
            this.$refs.sbUpload.value="";
            this.loading=false;	
            let res=response; 
            //if(res.errcode===0 || res.errcode===200){
                //if(res.message){
                //    this.$message({
                //        message:res.message || "上传成功！",
                //        type:'success',
                //        duration: 3000
                //    })
                //}
                let picType=this.handleValueType().valueType;
                if(this.upload.multiple || this.upload.limit>1){
                    for(let i in res.data.sysFiles){
                        if(picType){
                            this.fileList.push(res.data.sysFiles[i].anonymousFilePath);
                        }else{
                            this.fileList.push(res.data.sysFiles[i]);
                        }
                    }
                }else{
                    if(picType){
                        this.fileList=[res.data.sysFiles[0].anonymousFilePath];
                    }else{
                        this.fileList=[res.data.sysFiles[0]];
                    }
                }                
                //if(this.upload.uploadData) 
                this.$emit("uploadData", picType?this.fileList.join(","):this.fileList);
                if(this.onOk && this.upload.afterUpload){
                    this.onOk(this.upload,"afterUpload",res.data);
                }
                //if(this.upload.afterUpload) this.$emit(this.upload.afterUpload,{list:files,fileData:file.response});
            //}else{
                //this.$message({
                //    message:res.message || '上传失败',
                //    type:'error',
                //    duration: 3000
                //});
                //if(res.errcode===401 || res.errcode===403){
                //    //this.$confirm('你已被登出,可以取消继续留在该页面,或者重新登录','确认登出',{
                //    //    confirmButtonText:'重新登录',
                //    //    cancelButtonText:'取消',
                //    //    type:'warning'
                //    //}).then(() => {
                //        store.dispatch('FedLogOut').then(() => {
                //            location.reload();
                //        });
                //   // })
                //}
            //}
        },
        handleRemove(file,i){
            //移除后要干什么
            if(this.upload.multiple || this.upload.limit>1){
                let index=this.fileList.findIndex(fi => fi===file);
                if(index>-1) this.fileList.splice(index,1);
                this.$emit("uploadData", this.fileList);
            }else{
                this.fileList=[];
                this.$emit("uploadData", []);
            }
            if(this.onOk && this.upload.removeFun){
                this.onOk(this.upload,"removeFun");
			}
        },
        beforeUpload(file){
            if(this.onOk && this.upload.beforeFun){
                let n=this.onOk(this.upload,"beforeFun");
                return n;
            }
            return true;
        },
        handleExceed(){
            if(this.fileList.length>(this.upload.limit ||20)){// || (this.upload.limit && this.fileList.length>this.upload.limit)
                this.$message({
                    message:'最多允许上传'+(this.upload.limit || 20)+'个文件！'
                });
                //清空上传文件框
                return false;
            }
            return true;
        },
        uploadfiles(files,content){      
            let self=this;
            if(this.beforeUpload(content.files)){
                let formData=new FormData;
                if(this.upload.data){
                    for(let i in this.upload.data){                        
                        formData.append(i,this.upload.data[i]);
                    }
                }
                if (this.upload.listType && (this.upload.listType==='picture' || this.upload.listType==='picture-card')) {//是否要压缩图片                
                    let imgL=0,newFiles=[];
                    for(var i=0;i<content.files.length;i++){
                        let filei=content.files[i];
                        if (/\/(?:jpeg|png)/i.test(content.files[i].type)) {//如果是图片 就压缩
                            if((!self.upload.canvasSize) || (self.upload.canvasSize && self.upload.canvasSize*1024<filei.size)){
                                let fr = new FileReader();
                                fr.onload = function (e) {
                                    let IMG = new Image();
                                    IMG.src = e.target.result;//读出来的文件流
                                    IMG.onload = function () {
                                        let w = this.naturalWidth, h = this.naturalHeight, resizeW = this.naturalWidth, resizeH = this.naturalHeight;           
                                        // maxSize 是压缩的设置，设置图片的最大宽度和最大高度，等比缩放，level是报错的质量，数值越小质量越低
                                        let fs = ((self.upload.filesize*1024) / filei.size);
                                        let maxSize = {
                                            width: self.upload.maxWidth || w,
                                            height: self.upload.maxHeight || h,
                                            level: self.upload.maxLevel || fs || 0.8
                                        };
                                        if (w > maxSize.width || h > maxSize.height) {//设置压缩比例
                                            if (h / w > maxSize.height / maxSize.width) {
                                                resizeH = maxSize.height;
                                                resizeW = parseInt((maxSize.height / h) * w)+1;
                                            } else {
                                                resizeW = maxSize.width;
                                                resizeH = parseInt((maxSize.width / w) * h)+1;
                                            }
                                        }                                    
                                        //将文件流画到canvas画布上
                                        let canvas = document.createElement('canvas');
                                        let ctx = canvas.getContext('2d');   
                                        canvas.width = resizeW;
                                        canvas.height = resizeH;                            
                                        ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                        // let Orientation=filei.exifdata.Orientation;   
                                        // switch(Orientation){
                                        //     case 1://0°
                                        //     case 6://顺时针90°  
                                        //         canvas.width = resizeW;
                                        //         canvas.height = resizeH;
                                        //         ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                        //         break;
                                        //     case 3://180°
                                        //         canvas.width = resizeW;
                                        //         canvas.height = resizeH;
                                        //         ctx.rotate(-360 * Math.PI / 180);
                                        //         ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                        //         break; 
                                        //     case 8://逆时针90°   
                                        //         var nw=resizeW;
                                        //         resizeW=resizeH;
                                        //         resizeH=nw;
                                        //         canvas.width = resizeW;
                                        //         canvas.height = resizeH;
                                        //         ctx.rotate(360 * Math.PI / 180);
                                        //         ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                        //         break;
                                        //     default:                                            
                                        //         ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                        //         break;
                                        // }
                                        //再将画布上元素导出到Base64
                                        let base64img = canvas.toDataURL('image/jpeg', maxSize.level);
                                        let base64 = window.atob(base64img.split(',')[1]);
                                        let buffer = new ArrayBuffer(base64.length);
                                        let ubuffer = new Uint8Array(buffer);
                                        for (let i = 0; i < base64.length; i++) {
                                            ubuffer[i] = base64.charCodeAt(i)
                                        }
                                        //再把Base64转成blob文件（图片）
                                        let blob;
                                        try {
                                            blob = new Blob([buffer], { type: 'image/jpg' });
                                        } catch (e) {
                                            window.BlobBuilder = window.BlobBuilder || window.WebKitBlobBuilder || window.MozBlobBuilder || window.MSBlobBuilder;
                                            if (e.name === 'TypeError' && window.BlobBuilder) {
                                                let blobBuilder = new BlobBuilder();
                                                blobBuilder.append(buffer);
                                                blob = blobBuilder.getBlob('image/jpg');
                                            }
                                        }
                                        //  newFiles.push(blob);
                                        formData.append((self.upload.name || 'file'), blob, filei.name);  
                                        imgL++;  
                                        if(imgL===content.files.length){                                      
                                            // formData.append((self.upload.name || 'file'), newFiles); 
                                            self.doUpload(formData,content);
                                        } 
                                    };//end imgload
                                };//end frload                     
                                fr.readAsDataURL(filei);
                            }else{
                                formData.append((self.upload.name || 'file'), filei, filei.name);
                                imgL++;  
                                if(imgL===content.files.length){                                      
                                    // formData.append((self.upload.name || 'file'), newFiles); 
                                    self.doUpload(formData,content);
                                }  
                            }
			    	    }
                    }
			    }else{
                    for(var i=0;i<content.files.length;i++){ 
                        if(content.files[i].size==0){
                            let blobType={ "323":"text/h323","3gp":"video/3gpp","aab":"application/x-authoware-bin","aam":"application/x-authoware-map","aas":"application/x-authoware-seg","acx":"application/internet-property-stream","ai":"application/postscript","aif":"audio/x-aiff","aifc":"audio/x-aiff","aiff":"audio/x-aiff","als":"audio/X-Alpha5","amc":"application/x-mpeg","ani":"application/octet-stream","apk":"application/vnd.android.package-archive","asc":"text/plain;charset=utf-8","asd":"application/astound","asf":"video/x-ms-asf","asn":"application/astound","asp":"application/x-asap","asr":"video/x-ms-asf","asx":"video/x-ms-asf","au":"audio/basic","avb":"application/octet-stream","avi":"video/x-msvideo","awb":"audio/amr-wb","axs":"application/olescript","bas":"text/plain;charset=utf-8","bcpio":"application/x-bcpio","bin ":"application/octet-stream","bld":"application/bld","bld2":"application/bld2","bmp":"image/bmp","bpk":"application/octet-stream","bz2":"application/x-bzip2","c":"text/plain;charset=utf-8","cal":"image/x-cals","cat":"application/vnd.ms-pkiseccat","ccn":"application/x-cnc","cco":"application/x-cocoa","cdf":"application/x-cdf","cer":"application/x-x509-ca-cert","cgi":"magnus-internal/cgi","chat":"application/x-chat","class":"application/octet-stream","clp":"application/x-msclip","cmx":"image/x-cmx","co":"application/x-cult3d-object","cod":"image/cis-cod","conf":"text/plain;charset=utf-8","cpio":"application/x-cpio","cpp":"text/plain;charset=utf-8","cpt":"application/mac-compactpro","crd":"application/x-mscardfile","crl":"application/pkix-crl","crt":"application/x-x509-ca-cert","csh":"application/x-csh","csm":"chemical/x-csml","csml":"chemical/x-csml","css":"text/css","cur":"application/octet-stream","dcm":"x-lml/x-evm","dcr":"application/x-director","dcx":"image/x-dcx","der":"application/x-x509-ca-cert","dhtml":"text/html","dir":"application/x-director","dll":"application/x-msdownload","dmg":"application/octet-stream","dms":"application/octet-stream","doc":"application/msword","docx":"application/vnd.openxmlformats-officedocument.wordprocessingml.document","dot":"application/msword","dvi":"application/x-dvi","dwf":"drawing/x-dwf","dwg":"application/x-autocad","dxf":"application/x-autocad","dxr":"application/x-director","ebk":"application/x-expandedbook","emb":"chemical/x-embl-dl-nucleotide","embl":"chemical/x-embl-dl-nucleotide","eps":"application/postscript","epub":"application/epub+zip","eri":"image/x-eri","es":"audio/echospeech","esl":"audio/echospeech","etc":"application/x-earthtime","etx":"text/x-setext","evm":"x-lml/x-evm","evy":"application/envoy","exe":"application/octet-stream","fh4":"image/x-freehand","fh5":"image/x-freehand","fhc":"image/x-freehand","fif":"application/fractals","flr":"x-world/x-vrml","flv":"flv-application/octet-stream","fm":"application/x-maker","fpx":"image/x-fpx","fvi":"video/isivideo","gau":"chemical/x-gaussian-input","gca":"application/x-gca-compressed","gdb":"x-lml/x-gdb","gif":"image/gif","gps":"application/x-gps","gtar":"application/x-gtar","gz":"application/x-gzip","h":"text/plain;charset=utf-8","hdf":"application/x-hdf","hdm":"text/x-hdml","hdml":"text/x-hdml","hlp":"application/winhlp","hqx":"application/mac-binhex40","hta":"application/hta","htc":"text/x-component","htm":"text/html","html":"text/html","hts":"text/html","htt":"text/webviewhtml","ice":"x-conference/x-cooltalk","ico":"image/x-icon","ief":"image/ief","ifm":"image/gif","ifs":"image/ifs","iii":"application/x-iphone",
                            "imy":"audio/melody","ins":"application/x-internet-signup","ips":"application/x-ipscript","ipx":"application/x-ipix","isp":"application/x-internet-signup","it":"audio/x-mod","itz":"audio/x-mod","ivr":"i-world/i-vrml","j2k":"image/j2k","jad":"text/vnd.sun.j2me.app-descriptor","jam":"application/x-jam","jar":"application/java-archive","java":"text/plain;charset=utf-8","jfif":"image/pipeg","jnlp":"application/x-java-jnlp-file","jpe":"image/jpeg","jpeg":"image/jpeg","jpg":"image/jpeg","jpz":"image/jpeg","js":"application/x-javascript","jwc":"application/jwc","kjx":"application/x-kjx","lak":"x-lml/x-lak","latex":"application/x-latex","lcc":"application/fastman","lcl":"application/x-digitalloca","lcr":"application/x-digitalloca","lgh":"application/lgh","lha":"application/octet-stream","lml":"x-lml/x-lml","lmlpack":"x-lml/x-lmlpack","log":"text/plain;charset=utf-8","lsf":"video/x-la-asf","lsx":"video/x-la-asf","lzh":"application/octet-stream","m13":"application/x-msmediaview","m14":"application/x-msmediaview","m15":"audio/x-mod","m3u":"audio/x-mpegurl","m3url":"audio/x-mpegurl","m4a":"audio/mp4a-latm","m4b":"audio/mp4a-latm","m4p":"audio/mp4a-latm","m4u":"video/vnd.mpegurl","m4v":"video/x-m4v","ma1":"audio/ma1","ma2":"audio/ma2","ma3":"audio/ma3","ma5":"audio/ma5","man":"application/x-troff-man","map":"magnus-internal/imagemap","mbd":"application/mbedlet","mct":"application/x-mascot","mdb":"application/x-msaccess","mdz":"audio/x-mod","me":"application/x-troff-me","mel":"text/x-vmel","mht":"message/rfc822","mhtml":"message/rfc822","mi":"application/x-mif","mid":"audio/mid","midi":"audio/midi","mif":"application/x-mif","mil":"image/x-cals","mio":"audio/x-mio","mmf":"application/x-skt-lbs","mng":"video/x-mng","mny":"application/x-msmoney","moc":"application/x-mocha","mocha":"application/x-mocha","mod":"audio/x-mod","mof":"application/x-yumekara","mol":"chemical/x-mdl-molfile","mop":"chemical/x-mopac-input","mov":"video/quicktime","movie":"video/x-sgi-movie","mp2":"video/mpeg","mp3":"audio/mpeg","mp4":"video/mp4","mpa":"video/mpeg","mpc":"application/vnd.mpohun.certificate","mpe":"video/mpeg","mpeg":"video/mpeg","mpg":"video/mpeg","mpg4":"video/mp4","mpga":"audio/mpeg","mpn":"application/vnd.mophun.application","mpp":"application/vnd.ms-project","mps":"application/x-mapserver","mpv2":"video/mpeg","mrl":"text/x-mrml","mrm":"application/x-mrm","ms":"application/x-troff-ms","msg":"application/vnd.ms-outlook","mts":"application/metastream","mtx":"application/metastream","mtz":"application/metastream","mvb":"application/x-msmediaview","mzv":"application/metastream","nar":"application/zip","nbmp":"image/nbmp","nc":"application/x-netcdf","ndb":"x-lml/x-ndb","ndwn":"application/ndwn","nif":"application/x-nif","nmz":"application/x-scream","nokia-op-logo":"image/vnd.nok-oplogo-color","npx":"application/x-netfpx","nsnd":"audio/nsnd","nva":"application/x-neva1","nws":"message/rfc822","oda":"application/oda","ogg":"audio/ogg","oom":"application/x-AtlasMate-Plugin","p10":"application/pkcs10","p12":"application/x-pkcs12","p7b":"application/x-pkcs7-certificates","p7c":"application/x-pkcs7-mime","p7m":"application/x-pkcs7-mime","p7r":"application/x-pkcs7-certreqresp","p7s":"application/x-pkcs7-signature","pac":"audio/x-pac","pae":"audio/x-epac","pan":"application/x-pan","pbm":"image/x-portable-bitmap","pcx":"image/x-pcx","pda":"image/x-pda","pdb":"chemical/x-pdb","pdf":"application/pdf",
                            "pfr":"application/font-tdpfr","pfx":"application/x-pkcs12","pgm":"image/x-portable-graymap","pict":"image/x-pict","pko":"application/ynd.ms-pkipko","pm":"application/x-perl","pma":"application/x-perfmon","pmc":"application/x-perfmon","pmd":"application/x-pmd","pml":"application/x-perfmon","pmr":"application/x-perfmon","pmw":"application/x-perfmon","png":"image/png","pnm":"image/x-portable-anymap","pnz":"image/png","pot,":"application/vnd.ms-powerpoint","ppm":"image/x-portable-pixmap","pps":"application/vnd.ms-powerpoint","ppt":"application/vnd.ms-powerpoint","pptx":"application/vnd.openxmlformats-officedocument.presentationml.presentation","pqf":"application/x-cprplayer","pqi":"application/cprplayer","prc":"application/x-prc","prf":"application/pics-rules","prop":"text/plain;charset=utf-8","proxy":"application/x-ns-proxy-autoconfig","ps":"application/postscript","ptlk":"application/listenup","pub":"application/x-mspublisher","pvx":"video/x-pv-pvx","qcp":"audio/vnd.qcelp","qt":"video/quicktime","qti":"image/x-quicktime","qtif":"image/x-quicktime","r3t":"text/vnd.rn-realtext3d","ra":"audio/x-pn-realaudio","ram":"audio/x-pn-realaudio","rar":"application/octet-stream","ras":"image/x-cmu-raster","rc":"text/plain;charset=utf-8","rdf":"application/rdf+xml","rf":"image/vnd.rn-realflash","rgb":"image/x-rgb","rlf":"application/x-richlink","rm":"audio/x-pn-realaudio","rmf":"audio/x-rmf","rmi":"audio/mid","rmm":"audio/x-pn-realaudio","rmvb":"audio/x-pn-realaudio","rnx":"application/vnd.rn-realplayer","roff":"application/x-troff","rp":"image/vnd.rn-realpix","rpm":"audio/x-pn-realaudio-plugin","rt":"text/vnd.rn-realtext","rte":"x-lml/x-gps","rtf":"application/rtf","rtg":"application/metastream","rtx":"text/richtext","rv":"video/vnd.rn-realvideo","rwc":"application/x-rogerwilco","s3m":"audio/x-mod","s3z":"audio/x-mod","sca":"application/x-supercard","scd":"application/x-msschedule","sct":"text/scriptlet","sdf":"application/e-score","sea":"application/x-stuffit","setpay":"application/set-payment-initiation","setreg":"application/set-registration-initiation","sgm":"text/x-sgml","sgml":"text/x-sgml","sh":"application/x-sh","shar":"application/x-shar","shtml":"magnus-internal/parsed-html","shw":"application/presentations","si6":"image/si6","si7":"image/vnd.stiwap.sis","si9":"image/vnd.lgtwap.sis","sis":"application/vnd.symbian.install","sit":"application/x-stuffit","skd":"application/x-Koan","skm":"application/x-Koan","skp":"application/x-Koan","skt":"application/x-Koan","slc":"application/x-salsa","smd":"audio/x-smd","smi":"application/smil","smil":"application/smil","smp":"application/studiom","smz":"audio/x-smd","snd":"audio/basic","spc":"application/x-pkcs7-certificates","spl":"application/futuresplash","spr":"application/x-sprite","sprite":"application/x-sprite","sdp":"application/sdp","spt":"application/x-spt","src":"application/x-wais-source","sst":"application/vnd.ms-pkicertstore","stk":"application/hyperstudio","stl":"application/vnd.ms-pkistl","stm":"text/html","svg":"image/svg+xml","sv4cpio":"application/x-sv4cpio","sv4crc":"application/x-sv4crc","svf":"image/vnd","svg":"image/svg+xml","svh":"image/svh","svr":"x-world/x-svr","swf":"application/x-shockwave-flash","swfl":"application/x-shockwave-flash","t":"application/x-troff","tad":"application/octet-stream","talk":"text/x-speech","tar":"application/x-tar","taz":"application/x-tar","tbp":"application/x-timbuktu",
                            "tbt":"application/x-timbuktu","tcl":"application/x-tcl","tex":"application/x-tex","texi":"application/x-texinfo","texinfo":"application/x-texinfo","tgz":"application/x-compressed","thm":"application/vnd.eri.thm","tif":"image/tiff","tiff":"image/tiff","tki":"application/x-tkined","tkined":"application/x-tkined","toc":"application/toc","toy":"image/toy","tr":"application/x-troff","trk":"x-lml/x-gps","trm":"application/x-msterminal","tsi":"audio/tsplayer","tsp":"application/dsptype","tsv":"text/tab-separated-values","ttf":"application/octet-stream","ttz":"application/t-time","txt":"text/plain;charset=utf-8","uls":"text/iuls","ult":"audio/x-mod","ustar":"application/x-ustar","uu":"application/x-uuencode","uue":"application/x-uuencode","vcd":"application/x-cdlink","vcf":"text/x-vcard","vdo":"video/vdo","vib":"audio/vib","viv":"video/vivo","vivo":"video/vivo","vmd":"application/vocaltec-media-desc","vmf":"application/vocaltec-media-file","vmi":"application/x-dreamcast-vms-info","vms":"application/x-dreamcast-vms","vox":"audio/voxware","vqe":"audio/x-twinvq-plugin","vqf":"audio/x-twinvq","vql":"audio/x-twinvq","vre":"x-world/x-vream","vrml":"x-world/x-vrml","vrt":"x-world/x-vrt","vrw":"x-world/x-vream","vts":"workbook/formulaone","wav":"audio/x-wav","wax":"audio/x-ms-wax","wbmp":"image/vnd.wap.wbmp","wcm":"application/vnd.ms-works","wdb":"application/vnd.ms-works","web":"application/vnd.xara","wi":"image/wavelet","wis":"application/x-InstallShield","wks":"application/vnd.ms-works","wm":"video/x-ms-wm","wma":"audio/x-ms-wma","wmd":"application/x-ms-wmd","wmf":"application/x-msmetafile","wml":"text/vnd.wap.wml","wmlc":"application/vnd.wap.wmlc","wmls":"text/vnd.wap.wmlscript","wmlsc":"application/vnd.wap.wmlscriptc","wmlscript":"text/vnd.wap.wmlscript","wmv":"audio/x-ms-wmv","wmx":"video/x-ms-wmx","wmz":"application/x-ms-wmz","wpng":"image/x-up-wpng","wps":"application/vnd.ms-works","wpt":"x-lml/x-gps","wri":"application/x-mswrite","wrl":"x-world/x-vrml","wrz":"x-world/x-vrml","ws":"text/vnd.wap.wmlscript","wsc":"application/vnd.wap.wmlscriptc","wv":"video/wavelet","wvx":"video/x-ms-wvx","wxl":"application/x-wxl","x-gzip":"application/x-gzip","xaf":"x-world/x-vrml","xar":"application/vnd.xara","xbm":"image/x-xbitmap","xdm":"application/x-xdma","xdma":"application/x-xdma","xdw":"application/vnd.fujixerox.docuworks","xht":"application/xhtml+xml","xhtm":"application/xhtml+xml","xhtml":"application/xhtml+xml","xla":"application/vnd.ms-excel","xlc":"application/vnd.ms-excel","xll":"application/x-excel","xlm":"application/vnd.ms-excel","xls":"application/vnd.ms-excel","xlsx":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","xlt":"application/vnd.ms-excel","xlw":"application/vnd.ms-excel","xm":"audio/x-mod","xml":"text/plain;charset=utf-8","xmz":"audio/x-mod","xof":"x-world/x-vrml","xpi":"application/x-xpinstall","xpm":"image/x-xpixmap","xsit":"text/xml","xsl":"text/xml","xul":"text/xul","xwd":"image/x-xwindowdump","xyz":"chemical/x-pdb","yz1":"application/x-yz1","z":"application/x-compress","zac":"application/x-zaurus-zac","zip":"application/zip" };
                            let html=" ";
                            let blob;
                            let bn=content.files[i].name.split(".");
                            let btype=blobType[bn[bn.length-1]];
                            try{
                                blob=new Blob([html],{type:btype});
                            }catch(e){
                                window.BlobBuilder=window.BlobBuilder || window.WebKitBlobBuilder || window.MozBlobBuilder || window.MSBlobBuilder;
                                if(e.name=="TypeError" && window.BlobBuilder){
                                    let blobBuilder=new BlobBuilder();
                                    blobBuilder.append(html);
                                    blob=blobBuilder.getBlob(btype);
                                }
                            }
                            formData.append((self.upload.name || 'file'), blob, content.files[i].name);  
                        }else{
                            formData.append((self.upload.name || 'file'),content.files[i]);
                        }
                    }                
                    self.doUpload(formData,content);
                }                
            }else{       
                // this.loading=false;	
                content.onError("文件上传失败！"); 
            }
        },
        doUpload(formData,content){
            if(this.upload.fun){
                this.$emit((this.from?"handleHttpRequest":"uploadHttpRequest"),{fun:this.upload.fun,formData,content});
            }else{             
                uploadProcessFiles(formData,this.upload.data).then(res => {
                    content.onSuccess(res);
                }).catch(error => {   
                    content.onError("文件上传失败！");
                });
            }
        }
    },
    watch:{
        'upload.filelist':{
            handler:function(newV,oldV){
                if(newV){
                    //console.log("值更新啦！",newV);
                    this.fileList=newV;
                }
            },
            deep:true,
            immediate: true
        }
    }
}
</script>
<style scoped>
.upload_D{min-height:32px;min-width:170px;}
.upload_Btn{position:relative;z-index:999;min-width:100px;height:32px;}
.upload_Btn input{position:absolute;left:0;z-index:1000;opacity:0;height:32px;cursor:pointer;}
.upload_Btn .uploadB{position:absolute;left:0;z-index:999;}
.el-upload-list--picture-card .el-upload-list__item{margin:8px 8px 8px 0;}
.el-upload-list__item-thumbnail {width: 148px;height: 148px;}
.el-upload-list--picture-card .el-upload-list__item {
    width: 148px;
    height: unset;
}
</style>
