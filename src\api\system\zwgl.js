import request from "@/assets/js/request";
import util from "@/assets/js/public";
import store from "@/store";

// 获取Lie表相关数据
export function getZwList(params) {
    // let url = util.toUrl(`/${process.env.VUE_APP_APPCODE}/uums/sys/position/findAll?`, {})+`&page=${params.page}&size=${params.size}&appcode=${process.env.VUE_APP_APPCODE}`;
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/position/findAll?page=${params.page}&size=${params.size}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 获取左侧树数据
export function findDictValue(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/dictvalue/findDictValue`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

export function addZw(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/position/create`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

export function deleteZwCustom(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/position/deleteByIdCustom?id=${id}`,
        contentType: "application/json; charset=utf-8",
    });
}

export function findPositionAndUser(params) {
    let url = util.toUrl(`/${process.env.VUE_APP_APPCODE}/uums/sys/position/findPositionAndUser`, params || {});
    return request({
        url: url,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}

