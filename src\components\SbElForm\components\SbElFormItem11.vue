<template>
  <div
    :class="
      item.show === false
        ? 'hide'
        : item.inline
        ? 'inlineB ' + (item.class || '')
        : ''
    "
  >
    <el-form-item :label="item.label || ''" :prop="item.key" :label-width="lw">
      <!--展示文本
        <template v-if="item.type==='text'" v-slot="scope">
            <span v-text="scope">aaaaa</span>
        </template>-->
      <!--文本框/多选文本框-->
      <el-input
        v-if="item.type === 'input'"
        :type="item.inputType || 'text'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        @input="handleChange"
        @blur="handleBlur"
        :disabled="item.disabled || false"
        :readonly="item.readonly || false"
        :clearable="item.clearable || false"
        :autosize="item.autosize || { minRows: 2 }"
        :show-word-limit="true"
      >
        <template v-if="item.append" slot="append">{{ item.append }}</template>
        <template v-if="item.prepend" slot="prepend">{{
          item.prepend
        }}</template>
        <template v-if="item.prefix" slot="prefix">{{ item.prefix }}</template>
        <template v-if="item.suffix" slot="suffix">{{ item.suffix }}</template>
      </el-input>
      <!--富文本-->
      <sb-kindeditor
        v-else-if="item.type === 'editor'"
        ref="kindeditor"
        :height="item.height || '280'"
        :disabled="item.disabled || false"
        :content="keyVal"
        @input="getContent"
      ></sb-kindeditor>
      <!--文本框/多选文本框(带按钮，按钮有单击事件)-->
      <el-input
        v-else-if="item.type === 'input-btn'"
        class="inputBtn"
        :type="item.inputType || 'text'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        :readonly="(!!item.inputReadonly)?true:false"
        :disabled="item.disabled || false"
        :autosize="item.autosize || false"
      >
        <el-button
          slot="append"
          :size="item.size || 'small'"
          type="primary"
          :disabled="item.disabled || false"
          @click="handleInputBtn"
          >{{ item.btnText }}
          <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
        </el-button>
      </el-input>
      <!--文本框/多选文本框(带按钮，按钮有单击事件)-->
      <sb-el-input-upload
        v-else-if="item.type === 'input-upload'"
        v-bind="$attrs"
        v-on="$listeners"
        :item="item"
        :on-ok="handleDoFun"
        @uploadHttpRequest="uploadHttpRequest"
      ></sb-el-input-upload>
      <!--文本框/多选文本框(带选择查询)-->
      <sb-choose-username
        v-else-if="item.type === 'chooseUsername'"
        v-bind="$attrs"
        v-on="$listeners"
        :item="item"
        @chooseData="handleChooseData"
      ></sb-choose-username>
      <!--文本框/多选文本框(带选择查询)-->
      <sb-choose-teacher
        v-else-if="item.type === 'chooseTeacher'"
        v-bind="$attrs"
        v-on="$listeners"
        :item="item"
        @chooseData="handleChooseData"
      ></sb-choose-teacher>
      <!--文本框/多选文本框(树)-->
      <sb-choose-org
        v-else-if="item.type === 'chooseOrg'"
        v-bind="$attrs"
        v-on="$listeners"
        :item="item"
        @chooseData="handleChooseData"
        @uploadHttpRequest="uploadHttpRequest"
      ></sb-choose-org>
      <!--数字框-->
      <el-input-number
        v-else-if="item.type === 'number'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        @change="handleChange"
        :disabled="item.disabled || false"
        :readonly="item.readonly || false"
        :min="item.min || 0"
        :max="item.max"
        :step="item.step || 1"
        :precision="item.precision"
      ></el-input-number>
      <!--下拉框-->
      <el-select
        v-else-if="item.type === 'select'"
        v-bind="$attrs"
        placeholder=""
        :filter-method="item.filterMethod ? filterMethod : null"
        :filterable="item.filterMethod ? true : false"
        v-on="$listeners"
        :size="item.size || 'small'"
        @change="handleChange"
        :multiple="item.multiple || false"
        :disabled="item.disabled || false"
        :clearable="item.clearable || false"
        :multiple-limit="item.multipleLimit || 0"
        :props="item.props || { value: 'value', label: 'name' }"
        :on-ok="handleDoFun"
        @visible-change="handleVisibleChange"
      >
        <el-option
          v-for="(o, i) in item.options || ajaxOptions"
          :key="o[item.props ? item.props.value : 'value']"
          :label="o[item.props ? item.props.label : 'name']"
          :value="o[item.props ? item.props.value : 'value']"
          :disabled="o.disabled"
        >
          <template v-if="item.template" v-slot="scope">
            <slot :name="item.template" :obj="{ index: i, item }"></slot>
          </template>
        </el-option>
      </el-select>
      <!--单选框-->
      <el-radio-group
        v-else-if="item.type === 'radio'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        :disabled="item.disabled || false"
        @change="handleChange"
      >
        <component
          v-for="o in item.options || ajaxOptions"
          :is="item.button ? 'el-radio-button' : 'el-radio'"
          :key="o[item.props ? item.props.value : 'value']"
          :label="o[item.props ? item.props.value : 'value']"
          :disabled="o.disabled"
          :border="item.border"
          >{{ o[item.props ? item.props.label : "name"] }}</component
        >
      </el-radio-group>
      <!--省市区-->
      <sb-province
        v-else-if="item.type === 'province'"
        v-bind="$attrs"
        v-on="$listeners"
        :item="item"
        @chooseData="handleChooseData"
      ></sb-province>
      <!--复选框-->
      <el-checkbox-group
        v-else-if="item.type === 'checkbox'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        :disabled="item.disabled || false"
        @change="handleChange"
      >
        <el-checkbox
          v-if="item.multi"
          :indeterminate="indeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
          >全选</el-checkbox
        >
        <component
          v-for="o in item.options || ajaxOptions"
          :is="item.button ? 'el-checkbox-button' : 'el-checkbox'"
          :key="o[item.props ? item.props.value : 'value']"
          :label="o[item.props ? item.props.value : 'value']"
          :disabled="o.disabled"
          :checked="o.checked"
          :border="item.border"
          >{{ o[item.props ? item.props.label : "name"] }}</component
        >
      </el-checkbox-group>
      <!--开关-->
      <el-switch
        v-else-if="item.type === 'switch'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        :active-value="item.activeValue || true"
        :inactive-value="inactiveFun(item.inactiveValue)"
        :disabled="item.disabled || false"
        :readonly="item.readonly || false"
      ></el-switch>
      <!--滑块-->
      <el-slider
        v-else-if="item.type === 'slider'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        :disabled="item.disabled || false"
        :readonly="item.readonly || false"
      ></el-slider>
      <!--级联选择器 :props="{ checkStrictly: true }"父子节点取消选中关联-->
      <el-cascader
        v-else-if="item.type === 'cascader'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        :disabled="item.disabled || false"
        :readonly="item.readonly || false"
        :clearable="true"
        filterable
        :options="item.options || ajaxOptions"
        :show-all-levels="item.showAllLevels || false"
        @change="handleChange"
        :props="item.props || { value: 'value', label: 'name' }"
      ></el-cascader>
      <!--时间选择框-->
      <div v-else-if="item.type === 'time'">        
        <el-time-picker
          v-if="item.isRange"
          :value-format="item.valueFormat || 'HH:mm:ss'"
          v-bind="$attrs"
          v-on="$listeners"
          :size="item.size || 'small'"
          :disabled="item.disabled || false"
          :readonly="item.readonly || false"
          :editable="item.editable || true"
          @change="handleChange"
          range-separator="至"
          start-placeholde="开始时间"
          end-placeholde="结束时间"
          :is-range="item.isRange || false"
        ></el-time-picker>       
        <el-time-select
          v-else-if="item.isSelect"
          v-bind="$attrs"
          v-on="$listeners"
          :size="item.size || 'small'"
          :disabled="item.disabled || false"
          :readonly="item.readonly || false"
          :editable="item.editable || true"
          @change="handleChange"
          :picker-options="item.pickerOptions || true"
        ></el-time-select>           
        <el-time-picker
          v-else
          :value-format="item.valueFormat || 'HH:mm:ss'"
          v-bind="$attrs"
          v-on="$listeners"
          :size="item.size || 'small'"
          :disabled="item.disabled || false"
          :readonly="item.readonly || false"
          :editable="item.editable || true"
          @change="handleChange"
          :picker-options="handleTimePickerOptions(item.pickerOptions)"
        ></el-time-picker>
      </div>
      <!--日期(时间)选择框-->
      <el-date-picker
        v-else-if="item.type === 'date'"
        :type="item.subtype || 'datetime'"
        :value-format="item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
        :format="item.viewFormat || item.valueFormat || 'yyyy-MM-dd HH:mm:ss'"
        :size="item.size || 'small'"
        :disabled="item.disabled || false"
        :readonly="item.readonly || false"
        :editable="item.editable || true"
        v-bind="$attrs"
        v-on="$listeners"
        range-separator="至"
        start-placeholde="开始时间"
        end-placeholde="结束时间"
        :default-time="item.defaultTime || (
          (
            (!item.subtype) || (item.subtype && item.subtype.indexOf('time')>-1)
          )?(item.subtype=='datetimerange'?['00:00:00','23:59:59']:'00:00:00'):null
        )"
        @change="handleChange"
        :picker-options="handlePickerOptions(item.pickerOptions)"
      ></el-date-picker>
      <!--附件上传-->
      <sb-el-upload
        v-else-if="item.type === 'upload'"
        v-bind="$attrs"
        v-on="$listeners"
        :upload="item"
        @uploadData="handleUploadData"
        :on-ok="handleDoFun"
        @uploadHttpRequest="uploadHttpRequest"
      ></sb-el-upload>
      <!--sb附件上传-->
      <sb-upload
        v-else-if="item.type === 'sbUpload'"
        v-bind="$attrs"
        v-on="$listeners"
        :upload="item"
        @uploadData="handleUploadData"
        :on-ok="handleDoFun"
        @uploadHttpRequest="uploadHttpRequest"
      ></sb-upload>
      <!--评分-->
      <el-rate
        v-else-if="item.type === 'rate'"
        v-bind="$attrs"
        :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
        text-color="#ff9900"
        v-on="$listeners"
        :allow-half="item.allowHalf || false"
        :disabled="item.disabled || false"
        :show-text="item.showText || false"
        :texts="item.texts"
        :show-score="item.showScore || false"
      ></el-rate>
      <!--颜色选择器-->
      <el-color-picker
        v-else-if="item.type === 'color'"
        v-bind="$attrs"
        :color-format="item.format"
        v-on="$listeners"
        :size="item.size || 'small'"
        :disabled="item.disabled || false"
      ></el-color-picker>
      <!--图片-->
      <el-image v-else-if="item.type === 'img'" :fit="item.fit || 'contain'"
        ><el-input></el-input
      ></el-image>
      <sb-img v-else-if="item.type === 'sbImg'" :item="item"></sb-img>
      <!--二维码-->
      <sb-qrcode
        v-else-if="item.type === 'qrcode'"
        v-bind="$attrs"
        v-on="$listeners"
        :item="item"
      ></sb-qrcode>
      <!--template-->
      <template
        v-else-if="item.type === 'template'"
        v-bind="$attrs"
        v-on="$listeners"
      >
        <slot :name="item.template" :obj="{ item }"></slot>
      </template>
      <div
        v-else-if="item.type === 'text'"
        v-bind="$attrs"
        v-on="$listeners"
        :prop="item.key"
      >
        <div style="border:1px solid #000;">
          {{ keyVal || "" }}
        </div>
      </div>
      <div v-else v-bind="$attrs" v-on="$listeners" :prop="item.key">
        {{ keyVal || "未知控件类型" }}
      </div>
      <!--<span v-else>未知控件类型</span><slot v-else v-bind="$attrs" v-on="$listeners" :prop="item.key">{{label || '未知控件类型'}}</slot>-->
    </el-form-item>
  </div>
</template>
<script>
import { getDictList, uumsGetDictList } from "@/api/public";
export default {
  name: "SbElFormItem",
  props: {
    item: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    },
    keyVal: {},
    formVal:{},
    lw: {
      type: String
    }
  },
  data() {
    return {
      ajaxOptions: [],
      indeterminate: true,
      checkAll: false,
      currentValue: this.value
    };
  },
  created() {    
    if (this.item.dictType && !this.item.options) this.getRequestDictList();
    if (this.item.fun) {
      switch (this.item.type) {
        case "select":
        case "switch":
        case "checkbox":
        case "cascader":
        case "radio":
          if (
            !this.item.options ||
            (this.item.options && this.item.options.length === 0)
          )
            this.handleInputBtn();
          break;
      }
    }
    //this.nullTextFun();新增一个为空的选项默认为请选择
  },
  methods: {
    getContent: function(content) {
      // this.content = content;
      this.handleChange(content);
      this.handleUploadData(content);
    },
    handleTimePickerOptions:function(){
      if(this.item.pickerOptions){
        let opts=this.item.pickerOptions.replace(new RegExp(' ','g'),'').split("-");
        let s=opts[0],e=opts[1];
        if(s.indexOf("|")>-1){
          let st=s.split("|");
          s=this.formVal[st[0]] || st[1];
        }else if((/[\S\s](\d{1,2}):(\d{1,2}):(\d{1,2})/).test(s)){
          s=s;
        }else{
          s="00:00:00";
        }   
        if(e.indexOf("|")>-1){
          let et=e.split("|");
          e=this.formVal[et[0]] || et[1];
        }else if((/[\S\s](\d{1,2}):(\d{1,2}):(\d{1,2})/).test(e)){
          e=e;
        }else{
          e="23:59:59";
        }      
        return {
          selectableRange:`${s+'-'+e}`
        }
      }
      return false;
    },
    handlePickerOptions: function() {
      if (this.item.disabledDate === "beforeDateNoToday") {
        //只能选择当天以前，不含当天
        return {
          disabledDate(time) {
            return time.getTime() > Date.now() - 8.64e7;
          }
        };
      }
      if (this.item.disabledDate === "beforeDate") {
        //只能选择当天以前，含当天
        return {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        };
      }
      if (this.item.disabledDate === "afterDateNoToday") {
        //只能选择当天以后，不含当天
        return {
          disabledDate(time) {
            return time.getTime() < Date.now();
          }
        };
      }
      if (this.item.disabledDate === "afterDate") {
        //只能选择当天以后，含当天
        return {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          }
        };
      }
      return false;
    },
    dateDisabledDate(time) {
      if (this.item.disabledDate === "beforeDateNoToday")
        return time.getTime() > Date.now() - 8.64e7;
      if (this.item.disabledDate === "beforeDate")
        return time.getTime() > Date.now();
      if (this.item.disabledDate === "afterDateN0Today")
        return time.getTime() < Date.now() - 8.64e7;
      if (this.item.disabledDate === "afterDate")
        return time.getTime() < Date.now();
      return true;
    },
    handleVisibleChange(type) {
      if (this.onOk && type) {
        //val存在
        if (this.item.type === "select" && this.item.filterMethod)
          this.onOk(this.item, "filterMethod", "");
      }
    },
    filterMethod(val) {
      if (this.onOk) {
        //val存在
        this.onOk(this.item, "filterMethod", val);
      }
    },
    inactiveFun(val) {
      let reV = false;
      if (typeof val) {
        reV = val;
      }
      return reV;
    },
    nullTextFun() {
      if (
        this.item.type === "select" &&
        this.item.options &&
        !this.item.hasNoChoose
      ) {
        if (
          this.item.options.length > 0 &&
          this.item.options[0][
            this.item.props ? this.item.props.value : "value"
          ] !== ""
        ) {
          let chooseD = {};
          chooseD[this.item.props ? this.item.props.value : "value"] = "";
          chooseD[this.item.props ? this.item.props.label : "name"] =
            this.item.nullText || "请选择";
          this.item.options.unshift(chooseD);
          // this.item.options=JSON.parse(JSON.stringify(this.item.options));
        }
      }
    },
    getRequestDictList() {
      if (this.item.from) {
        uumsGetDictList(this.item.dictType).then(res => {
          this.dictValueType(res.data);
        });
      } else {
        getDictList(this.item.dictType, this.item.isPublic).then(res => {
          this.dictValueType(res.data);
        });
      }
    },
    dictValueType(data) {
      for (var i in data) {
        if (data[i].valueType && data[i].valueType === "int")
          data[i].value = parseInt(data[i].value);
      }
      //if(data.length>0 && data[0][this.item.props?this.item.props.value:'value']!==""){
      //    let chooseD={};
      //    chooseD[this.item.props?this.item.props.value:'value']="";
      //    chooseD[this.item.props?this.item.props.label:'name']=(this.item.nullText || "请选择");
      //    data.unshift(chooseD);
      //}
      this.ajaxOptions = data;
    },
    handleCheckAllChange(val) {
      let checkAllA = [];
      if (val) {
        for (var i in this.item.options) {
          checkAllA.push(
            this.item.options[i][
              this.item.props ? this.item.props.value : "value"
            ]
          );
        }
      }
      this.checkAll = val ? this.item.options : [];
      this.indeterminate = val;
      this.$emit("uploadUpdate", { key: this.item.key, list: checkAllA });
    },
    handleUploadData(opt) {
      this.$emit("uploadUpdate", { key: this.item.key, list: opt });
    },
    handleChooseData(opt) {
      this.$emit("uploadUpdate", { key: this.item.key, list: opt });
    },
    handleInputBtn() {
      this.$emit("inputBtn", { key: this.item.key, fun: this.item.fun });
    },
    uploadHttpRequest(opt) {
      this.$emit("inputBtn", opt);
    },
    handleDoFun(obj, funName, data) {
      if (this.onOk) {
        if (this.item[funName]) {
          let n = this.onOk(this.item, funName, data);
          if (funName === "beforeFun") return n;
        }
      } else {
        if (this.item[funName]) {
          this.$emit(this.item[funName], { obj, data });
        }
      }
      if (funName === "beforeFun") return true;
    },
    handleChange(vals) {
      if (this.onOk && this.item.changeFun) {
        this.onOk(this.item, "changeFun", vals);
      } else {
        if (this.item.changeFun)
          this.$emit("inputBtn", {
            key: this.item.key,
            fun: this.item.changeFun,
            vals: vals
          });
      }
    },
    handleBlur(obj) {
      if (this.onOk && this.item.blurFun) {
        this.onOk(this.item, "blurFun", obj.target.value);
      } else {
        if (this.item.blurFun)
          this.$emit("inputBtn", {
            key: this.item.key,
            fun: this.item.blurFun,
            vals: obj.target.value
          });
      }
    }
  }
};
</script>
<style>
.inputUpload .el-button--primary {
  background: #f5f7fa;
  border-color: #dcdfe6;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0 none;
  color: #909399;
  float: left;
}
.inputBtn .icon {
  margin-right: 0;
}
</style>
