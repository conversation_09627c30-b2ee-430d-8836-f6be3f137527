<template>
    <div class="fullCalendar">
        <FullCalendar ref="fullCalendar" :options="calendarOptions"></FullCalendar>
		<div class="readSchedule" v-if="showReadSchedule.value" v-Clickoutside ="closeReadSchedule" :style="`top:${showReadSchedule.top}px;left:${showReadSchedule.left}px;`">
			<el-card class="box-card">
				<div class="title">{{scheduleDetails.title}}</div>
				<div class="date">{{`${scheduleDetails.start}${scheduleDetails.end?" -- "+scheduleDetails.end:""}`}}</div>
				<div class="describe">
					<div class="tip">描述：</div>
					<div class="text">{{scheduleDetails.describe}}</div>
				</div>
				<div class="btn">
					<el-button type="primary" size="small" link @click="editSchedule">编辑</el-button>
					<el-button type="danger" size="small" @click="deleteSchedule" link>删除</el-button>
				</div>
			</el-card>
		</div>
		<el-dialog  :title="scheduleDialog.title" :visible.sync="scheduleDialog.visible" v-dialogDrag :close-on-click-modal="false" append-to-body width="800px">
			<el-form ref="ruleFormRef" :model="ruleForm.obj" :rules="rules" label-width="100px" class="demo-ruleForm" status-icon>
				<el-form-item label="主题：" prop="title">
					<el-input size="small" v-model="ruleForm.obj.title" style="width:100%" />
				</el-form-item>
				<el-form-item label="开始时间：" prop="startDate">
					<el-date-picker size="small"  style="width:200px" v-model="ruleForm.obj.startDate" type="date" placeholder="请选择开始日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
					<el-time-picker size="small" style="margin-left:20px;width:200px" v-model="ruleForm.obj.ssTime" placeholder="请选择结束时间" format="HH:mm:ss" value-format="HH:mm:ss" />
				</el-form-item>
				<el-form-item label="结束时间：" prop="eeTime">
					<el-date-picker size="small"  style="width:200px" v-model="ruleForm.obj.endDate" type="date" placeholder="请选择结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD"  />
					<el-time-picker size="small" style="margin-left:20px;width:200px" v-model="ruleForm.obj.eeTime" placeholder="请选择结束时间" format="HH:mm:ss" value-format="HH:mm:ss" />
				</el-form-item>
				<el-form-item label="描述：" prop="describe">
					<el-input v-model="ruleForm.obj.describe" :rows="10" type="textarea" />
				</el-form-item>
			</el-form>
			<div class="footer">
    			<el-button type="primary" size="small" @click="saveSchedule">确定</el-button>
				<el-button size="small" @click="scheduleDialog.visible = false">取消</el-button>
			</div>
    	</el-dialog>
    </div>
</template>
<script>
    import FullCalendar from "@fullcalendar/vue";
	import dayGridPlugin from '@fullcalendar/daygrid';
	import timeGridPlugin from '@fullcalendar/timegrid';
	import interactionPlugin from '@fullcalendar/interaction';
	import listPlugin from '@fullcalendar/list';
	import Clickoutside from "element-ui/src/utils/clickoutside"
	import { uitlAxios } from "@/api/utilAxios.js";
    export default {
        name:'schedule',
        components:{FullCalendar},
        directives:{Clickoutside},
        props:{
            control:{
                type:Object,
                default:()=>{
                    return {
                        findAll: "",
                        create: "",
                        update: "",
                        delete: ""
                    }
                }
            }
        },
        data(){
            return{
                scheduleDetails:{
                   title:"",
                   start:"",
                   end:"",
                   describe:""
                },
                showReadSchedule:{
                    value:false,
                    top:0,
                    left:0
                },
                scheduleDialog:{
                    title:'添加日程',
		            visible:false
                },
                ruleForm:{
                    obj:{
                        title:"",       // 主题
                        startDate:'',   // 开始日期
                        ssTime:'',   // 开始时间
                        endDate:'',     // 结束日期
                        eeTime:'',     // 结束时间
                        describe:''     // 描述
                    }
                },
                rules:{
                    title: [
                        { required: true, message: '请填写主题', trigger: 'blur'},
                    ],
                    startDate:[
                        { required: true, message: '请选择开始时间', trigger: 'blur'},
                    ]
                },
                calendarOptions:{
                    plugins: [dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin],
                    initialView: 'dayGridMonth', //初始化视图类型
                    locale: 'zh-cn',
                    height:'1000px',
                    width:'100%',
                    selectable: false, //是否可以选择
                    droppable: true,
                    editable: false, //是否可以拖拽编辑
                    allDayText:"全天",
                    events: [],
                    buttonText: {
                        day: '日',
                        week: '周',
                        month: '月',
                        prev:'＜',
                        next:'＞',
                        today:'今天',
                    },
                    headerToolbar: {
                        left: 'prev,next today', 
                        center: 'title', 
                        right: 'dayGridMonth,timeGridWeek,timeGridDay',
                    },
                    dateClick:this.handleDayClick,
                    eventClick:this.handleEventClick
                }
            }
        },
        created(){
            this.findAll()
        },
        methods:{
            handleDayClick(data){
                if(this.showReadSchedule.value) return false
                this.scheduleDialog.title = "添加日程"
                this.scheduleDialog.visible = true
                this.scheduleDetails = {};
                this.ruleForm.obj.startDate = data.dateStr
            },
            handleEventClick(event){
                if(this.showReadSchedule.value) return false
                // 根据id获取详情
                const obj = this.calendarOptions.events.find(item=>{
                    return item.id == event.event._def.publicId
                })
                this.scheduleDetails = obj;
                const screenHeight = document.documentElement.clientHeight;
                const screenWidth = document.documentElement.clientWidth;
                if(event.jsEvent.clientX+350>=screenWidth){
                    this.showReadSchedule.left = screenWidth - 350
                }else{
                    this.showReadSchedule.left = event.jsEvent.clientX
                }
                if(event.jsEvent.clientY+300>=screenHeight){
                    this.showReadSchedule.top = event.jsEvent.clientY - 300
                }else{
                    this.showReadSchedule.top = event.jsEvent.clientY + 10
                }
                this.showReadSchedule.value = true
            },
            closeReadSchedule(){
                if(this.showReadSchedule.value){
                    setTimeout(()=>{
                        this.showReadSchedule.value = false
                    },50)
                } 
            },
            findAll(){
                uitlAxios(this.control.findAll).then(res=>{
                    if(res.data){
                        res.data.forEach(item=>{
                            item.start = item.startDate+(item.ssTime?" "+item.ssTime:"");
                            item.end = (item.endDate||"")+(item.eeTime?" "+item.eeTime:"");
                        })
                        this.calendarOptions.events = res.data
                    }
                })
            },
            deleteSchedule(){
                this.$confirm('请确认是否删除该日程?','提醒',{confirmButtonText: '确认',cancelButtonText: '取消',type: 'warning',}).then(() => {
                    uitlAxios(this.control.delete,{id:this.scheduleDetails.id}).then(res=>{
                        this.showReadSchedule.value = false
                        this.findAll()
                    })
                }).catch(() => {})
            },
            editSchedule(){
                this.ruleForm.obj = JSON.parse(JSON.stringify(this.scheduleDetails))
                this.showReadSchedule.value = false
                this.scheduleDialog.visible = true;
                this.scheduleDialog.title = "编辑日程"
            },
            saveSchedule(){
                this.$refs.ruleFormRef.validate((valid, fields) => {
                    if (valid) {
                        const start = new Date(this.ruleForm.obj.startDate+(this.ruleForm.obj.ssTime?" "+this.ruleForm.obj.ssTime:"")).getTime();
                        const end =  (this.ruleForm.obj.endDate||"")+(this.ruleForm.obj.eeTime?" "+this.ruleForm.obj.eeTime:"")?new Date((this.ruleForm.obj.endDate||"")+(this.ruleForm.obj.eeTime?" "+this.ruleForm.obj.eeTime:"")).getTime():null;
                        if(end&&end<=start){
                            this.$message({
                                type:"error",
                                message:"结束时间必须大于开始时间"
                            })
                            return false
                        }
                        if(this.ruleForm.obj.id){
                            // 编辑
                            uitlAxios(this.control.create,{id:this.ruleForm.obj.id},this.ruleForm.obj).then(res=>{
                                this.scheduleDialog.visible = false;
                                this.findAll()
                            })
                        }else{
                            // 新增
                            uitlAxios(this.control.update,null,this.ruleForm.obj).then(res=>{
                                this.scheduleDialog.visible = false;
                                this.findAll()
                            })
                        }
                    } else {
                        this.$message({
                            type:"error",
                            message:"表单校验不通过"
                        })
                    }
                })
            }
        }
    }
</script>
<style scoped>
    .fullCalendar{
		padding:20px;
		background: #fff;
        width: 100%;
	}
    .mask{
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 9999;
    }
	.footer{
		display: flex;
		justify-content: center;
        padding: 20px 0;
	}
	.readSchedule{
		position: fixed;
		background: #fff;
		width: 400px;
		font-size: 14px;
		z-index: 999;
	}
    .readSchedule .title{
		font-size: 21px;
		color: #000;
		font-weight: bold;
	}
    .readSchedule .date{
		color: #999;
	}
    .readSchedule .describe{
		display: flex;
		border: 1px solid #eee;
		margin-top: 20px;
		margin-bottom: 20px;
		border-right: none;
		border-left: none;
		padding: 10px 0;
	}
    .readSchedule .describe .tip{
		width: 45px;
	}
    .readSchedule .describe .text{
		width: calc(100% - 45px);
	}
	.readSchedule .btn{
		display: flex;
		justify-content: flex-end;
	}
    .el-form-item{
        margin-bottom: 0;
    }
</style>