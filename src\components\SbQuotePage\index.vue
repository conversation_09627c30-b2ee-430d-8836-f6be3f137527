<template>
  <div class="app-container">
    <sb-el-table
      class="tableWarp"
      :table="table"
      :on-ok="handleDoFun"
      @updateTableData="updateTableData"
      @getList="getList"
      @handleCreate="handleCreate"
      @handleDelete="handleDelete"
      @handleUpdateGetRow="handleUpdateGetRow"
      @handleUpdate="handleUpdate"
      @exportSelect="exportSelect"
      @allChangeFun="allChangeFun"
      @allblurFun="allblurFun"
      @allHandleFun="allHandleFun"
      @afterUpload="afterUpload" 
      @handleRemove="handleRemove"

    >
    </sb-el-table>
    <!-- 通用列表 -->
    <!-- cutomcode start -->

    <!-- cutomcode end -->
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";
import { Message, MessageBox } from "element-ui";
import {
  getTableList,
  getAddFormSubmit,
  getUpdateFormSubmit,
  getDelete,
  exportExcel
} from '@/api/public'

export default {
  name: 'app',
  props: {
    control: {
        type: Object,
        default: {}
    }
  },
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),
      table: {
        border: true, //是否带纵向边框
        modulName: 'build-数据', //列表中文名称
        stripe: true, //是否为斑马条样式
        hasSelect: false, //是否有复选框
        showIndex: true, //是否显示序号
        data: [], //数据
        addAndUpdateType: 'dialog',
        hasShowSummary: this.$props.control.data.showPage.hasShowSummary, //是否合计
        total: null,
        hasQueryForm: true, //是否有查询条件
        showType: '',
        queryForm: {
          inline: true,
          labelWidth: '120px',
          formItemList: []
        },
        tr: [],
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "800px",
          inline: true,
          labelPosition:"",
          labelWidth: "80px",
          formItemList: [],
        },
        hasOperation: true, //是否有操作列表
        operation: {
          width: '200px',
          align: 'center',
          data: [
          ]
        },
        listFormModul: {},
        hasPagination: true,
        listQuery: {
          size: 10,
          page: 1
        },
        hasOtherQueryBtn: true,
        otherQueryBtn: {
          data: []
        },
        hasBatchOperate: false //有无批量操作
      },
      tableConfig: this.$props.control.data,
      listFun:[],
      // cutomcode start

      // cutomcode end
    }
  },
  created() {
    this.tableConfig.tableData.forEach((item) => {
      const tableInfo = {
        id: item.field,
        label: item.colName,
        prop: item.field,
        align: item.align,
      }
      if(item.sorts) tableInfo.sortable = 'true'
      if(item.fixed) tableInfo.fixed = 'left'
      if(item.showType && item.showType != 'text'){
        tableInfo.showType = item.showType
        if(item.showType == 'custom'){
          tableInfo.showTypeFun = item.showTypeFun
        }
        if(item.showType == 'currentDictType'){
          tableInfo.currentDictType = item.currentDictType
        }
      } 
      if(item.edits){
        tableInfo.edits = true
        if(item.editsType == 'select'){
          tableInfo.editsType = 'select'
          if(item.selectype == 'async'){
            let params = {}
            if(item.protParamName){
              params[item.protParamName] = item.protParam
            }
            tableInfo.apiUrl = item.sourceFun,
            tableInfo.request = item.request,
            tableInfo.paramsObj = params
          } else if (item.selectype == 'dictType'){ 
            tableInfo.dictType = item.dictType
            tableInfo.options = ''
          } else {
            tableInfo.options = item.options
          }
        }else if(item.editsType == 'date'){
          tableInfo.editsType = 'date'
          tableInfo.valueFormat = item.valueFormat
          if(tableInfo.valueFormat == 'yyyy-MM' || tableInfo.valueFormat == 'yyyyMM'){
            tableInfo.subtype = 'month'
          }
          if(tableInfo.valueFormat == 'yyyy'){
            tableInfo.subtype = 'year'
          }
        }else{
          tableInfo.editsType = 'input'
        }
      }
      this.table.tr.push(tableInfo)
    })
    this.table.hasPagination = this.tableConfig.showPage.isPage
    this.table.listQuery.size = this.tableConfig.showPage.pageNumber

    const controlList = this.tableConfig.controlBtn.filter(
      (item) => item.btnShow
    )
    const operation = controlList.filter(
      (item) => item.id == 'add' || item.id == 'edit' || item.id == 'del' || item.id == 'read'
    )
    operation.forEach((item) => {
      const btnParam = {
        id: '',
        name: '',
        fun: ''
      }
      btnParam.name = item.btnText
      btnParam.id = item.id
      if (item.id == 'add') {
        btnParam.fun = 'handleCreate'
        btnParam.beforeFun = 'handleAddBefore'
      } else if (item.id == 'edit') {
        btnParam.id = 'update'
        btnParam.fun = 'handleUpdate'
      } else if (item.id == 'read') {
        btnParam.fun = 'handleUpdateGetRow'
      } else {
        btnParam.id = 'delete'
        btnParam.type = 'danger'
        btnParam.fun = 'handleDelete'
      }
      this.table.operation.data.push(btnParam)
    })
    const otherQueryBtn = controlList.filter(
      (item) => item.id == 'import' || item.id == 'export'
    )
    if (otherQueryBtn.length > 0) {
      otherQueryBtn.forEach((item) => {
        const otherInfo = {
          id: item.id,
          name: item.btnText,
          fun: item.id + 'Select'
        }
        this.table.otherQueryBtn.data.push(otherInfo)
      }) 
    }
    const custombtnList = this.tableConfig.custombtn.filter(
      (item) => item.btnShow 
    )
    if (custombtnList.length > 0) {
      custombtnList.forEach((item) => {
        const btnParam = {
          id: item.id,
          name: item.btnText,
          fun: 'allHandleFun',
        }
        if(item.position == 'right'){
          this.table.operation.data.push(btnParam)
        }else{
          this.table.otherQueryBtn.data.push(btnParam)
        }
      })
    }
    this.tableConfig.searchData.forEach((item) => {
      const searchInfo = {
        label: item.colName,
        key: item.field,
        type: item.type,
        labelWidth: item.labelWidth + 'px',
        paramType:item.paramType,
      } 
      if (['radio', 'multiple'].includes(item.type)) {
        searchInfo.type = 'select'
        if (item.type === 'multiple') {
          searchInfo.multiple = true
        }
        if(item.selectype == 'async'){
          let params = {}
          if(item.protParamName){
            params[item.protParamName] = item.protParam
          }
          searchInfo.apiUrl = item.sourceFun,
          searchInfo.request = item.request,
          searchInfo.paramsObj = params
        } else if (item.selectype == 'dictType'){ 
          searchInfo.dictType = item.dictType
          searchInfo.options = ''
        } else {
          searchInfo.options = item.options
        }
      }
      if (item.type == 'date') {
        searchInfo.valueFormat = item.valueFormat
        if(item.valueFormat == 'yyyy-MM' || item.valueFormat == 'yyyyMM'){
          searchInfo.subtype = 'month'
        }
        if(item.valueFormat == 'yyyy'){
          searchInfo.subtype = 'year'
        }
      }
      if(item.type == 'date2'){
        searchInfo.type = 'date'
        searchInfo.subtype = 'datetimerange'
        searchInfo.valueFormat = item.valueFormat
        searchInfo.datetimerange = item.valueFormat
        // if(item.relevanceValue){
        //   searchInfo.rangeName = [item.field,item.relevanceValue]
        // }else{
        //   searchInfo.rangeName = [item.field]
        // }
        // searchInfo.key = item.field + '123'
      } 
      if (item.required && item.defaultValue) {
        this.table.listQuery[item.field] = item.defaultValue
      }
      if (item.hiddenParams) {
        this.table.listQuery[item.field] = item.defaultValue
      }else{
        this.table.queryForm.formItemList.push(searchInfo)
      }
    })
    this.getList()
    // 改变事件方法另存
    if(this.table.form.formItemList.length>0){
      this.table.form.formItemList.forEach(element => {
        if(element.changeFunInfo || element.blurFunInfo){
          this.listFun.push({
            key:element.key,
            changeFunInfo:element.changeFunInfo,
            blurFunInfo:element.blurFunInfo
          })
        }
      });
    }
  },
  mounted() {

  },
  methods: {
    getList() {
      this.table.loading = true;
      let queryObj = {}
      if(this.table.listQuery){
        let listQuerys = this.table.listQuery
        for (const key in listQuerys) {
          if(key == 'page' || key == 'size'){
            queryObj[key] = listQuerys[key]
          }else{
            var index = this.table.queryForm.formItemList.findIndex(item => item.key === key);
            queryObj[key] = {
              paramValue: Array.isArray(listQuerys[key]) ? (listQuerys[key]).join(",") : listQuerys[key],
              paramType:index > -1 ? this.table.queryForm.formItemList[index].paramType || 'LIKE' : 'LIKE'
            }
          }
        }
      }
      if(this.tableConfig.filterArr && this.tableConfig.filterArr.length>0){
        let fieldDefaultFilterRule = []
        this.tableConfig.filterArr.forEach(element => {
          let objs = {
            field:element.field,
            fieldName:element.filterRules,
          }
          if(element.filterRules == 'role'){
            objs.fieldValue = (element.rulesArr.map((item) => item.roleCode)).join()
          }
          if(element.filterRules == 'position'){
            objs.fieldValue = (element.rulesArr.map((item) => item.id)).join()
          }
          if(element.filterRules == 'group'){
            objs.fieldValue = (element.rulesArr.map((item) => item.sid)).join()
          }
          fieldDefaultFilterRule.push(objs)
        });
        queryObj.fieldDefaultFilterRule = fieldDefaultFilterRule
      }
      getTableList(
        this.tableConfig.appCode,
        this.tableConfig.tableName,
        queryObj
      ).then((res) => {
        this.table.loading = false;
        this.table.data = res.data.content
        this.table.total = res.data.totalElements
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    handleCreate() {
      getAddFormSubmit(
        this.tableConfig.appCode,
        this.tableConfig.tableName,
        this.table.listFormModul
      ).then((res) => {
        this.table.dialogVisible = false
        this.getList()
      })
    },
    handleAddBefore(row){
      for(var i in this.table.form.formItemList){
        this.table.form.formItemList[i].disabled = false;
      }
      return true;
    },
    handleUpdateGetRow(row) {
      for(var i in this.table.form.formItemList){
        this.table.form.formItemList[i].disabled = row.read?true:false;
      }
      this.table.form = JSON.parse(JSON.stringify(this.table.form));
      this.table.listFormModul = row
    },
    handleUpdate(data) {
      if(data){
        this.table.listFormModul = Object.assign(this.table.listFormModul, data);
      }
      getUpdateFormSubmit(
        this.tableConfig.appCode,
        this.tableConfig.tableName,
        this.table.listFormModul
      ).then(() => {
        this.table.dialogVisible = false
        this.getList()
      })
    },
    async handleDelete(row) {
      await getDelete(
        this.tableConfig.appCode,
        this.tableConfig.tableName,
        row.id
      )
      this.getList()
    },
    exportSelect() { 
      exportExcel(this.tableConfig.appCode,this.tableConfig.tableName,this.table.listQuery)
               .then((res) => {
                   this.util.blobDownload(
                       res.data,
                       this.util.replaceXlsxExtension(res.filename)
                   );
               })
               .catch((err) => {});
    },
    importSelect() {},
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data)
      return n
    },
    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    // 表单的值发生变化时触发
    allChangeFun(obj, value) {
      var index = this.listFun.findIndex((item) => item.key === obj.key)
      if (index > -1) {
        this.listFun[index].changeFunInfo(obj, value)
      }
    },
    // 失去焦点时触发
    allblurFun(obj, value) {
      var index = this.listFun.findIndex((item) => item.key === obj.key)
      if (index > -1) {
        this.listFun[index].blurFunInfo(obj, value)
      }
    },
    // 操作按钮全部事件
    allHandleFun(obj){
      // console.log(obj)
    },
    // 附件操作
    afterUpload(obj,data){
        let idsArr = []
        if(obj.multiple){
            idsArr = this.listFormModul[obj.fileId] ? (this.listFormModul[obj.fileId]).split(",") : []
        }
        data.sysFiles.forEach(element => {
            idsArr.push(element.id)
        });
        this.listFormModul[obj.fileId] = idsArr.join(",")
    },
    handleRemove(obj){
        let sysFiles = this.listFormModul[obj.key]
        let idsArr = []
        if(sysFiles.length>0){
             sysFiles.forEach(element => {
                idsArr.push(element.id)
            });
        }
        this.listFormModul[obj.fileId] = idsArr.join(",")
    },


    // cutomcode start

    // cutomcode end
  }
}
</script>
<style scoped>
.app {
  height: calc(100vh - 100px);
  padding: 10px;
}
</style>