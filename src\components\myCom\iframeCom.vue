<script>
export default {
  name: "iframeCom",
  props: {
    url: {
      type: String,
      default: ''
    },
    fileType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      timer: null
    }
  },
  beforeDestroy() {
    clearInterval(this.timer)
  },
  methods: {
    onIframeLoad() {
      let iframe = document.getElementById('iframe-box')
      this.timer = setInterval(() => {
        try {
          var bHeight = iframe.contentWindow.document.body.scrollHeight
          if(this.fileType=="xlsx") bHeight=1020;
          iframe.style.height = bHeight + 'px'
        } catch (ex) {}
      }, 300)
    },
  }
}
</script>

<template>
  <div class="w100">
    <iframe
        ref="myIframe"
        @load="onIframeLoad"
        id="iframe-box" :style="fileType=='xlsx'?'':'pointer-events:none'"
        scrolling="no"
        :src="url">
    </iframe>
  </div>
</template>

<style scoped>
#iframe-box {
  border: none;
  width: 100%;
  height: 300px;
}
</style>