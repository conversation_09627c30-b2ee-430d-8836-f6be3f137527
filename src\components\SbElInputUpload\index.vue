<template>
  <div class="inputBtn w100 flexNowrap">
    <div class="inlineB fl el-input-group--append" style="width:141px;">
      <el-input
        :type="item.inputType || 'text'"
        v-bind="$attrs"
        v-on="$listeners"
        :size="item.size || 'small'"
        :placeholder="item.placeholder || item.label || '请输入'"
        :disabled="item.disabled || false"
        :readonly="item.readonly || false"
        :autosize="item.autosize || false"
      ></el-input>
    </div>
    <div class="inlineB fl inputUpload" style="width:58px;">
      <sb-el-upload
        v-bind="$attrs"
        v-on="$listeners"
        :on-ok="handleDoFun"
        :from="true"
        :upload="item"
        @uploadData="handleUploadData"
        @handleHttpRequest="uploadHttpRequest"
      ></sb-el-upload>
    </div>
  </div>
</template>
<script>
export default {
  name: "SbElInputUpload",
  props: {
    item: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    }
  },
  data() {
    return {};
  },
  methods: {
    handleUploadData(opt) {
      this.$emit("handleUploadData", { key: this.item.key, list: opt });
    },
    uploadHttpRequest(opt) {
      this.$emit("uploadHttpRequest", opt);
    },
    handleDoFun(obj, funName) {
      if (this.onOk && this.item[funName]) {
        let n = this.onOk(this.item, funName);
        if (funName === "beforeFun") return n;
      }
      if (funName === "beforeFun") return true;
    }
  }
};
</script>
<style>
.inputUpload .el-upload-list {
  margin-left: -141px;
}
.el-input-group--append .el-input {
  float: left;
}
</style>
