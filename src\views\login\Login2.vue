<template>
  <div class="login-warpper" v-wechat-title="title">
    <el-form autoComplete="on" :model="loginForm" ref="loginForm" :rules="loginFormRules" label-position="center" class="loginContainer">
      <h3 class="loginTitle">
        {{tits}}
      </h3>
      <el-form-item prop="username" class="mt32 plf15">
        <el-input name="username" type="text" v-model="loginForm.username" autoComplete="on" placeholder="请输入用户名">
          <span slot="prefix">
            <img src="./images/user.png" />
          </span>
        </el-input>
      </el-form-item>
      <el-form-item prop="passwordm" class="plf15">
        <el-input name="passwordm" :type="pwdType" v-model="loginForm.passwordm" autoComplete="on" placeholder="请输入密码">
          <span slot="prefix">
            <img src="./images/lock.png" />
          </span> 
          <span slot="suffix" @click="showPwd">
            <svg-icon icon-class="chakan" class-name="chakan"></svg-icon>
          </span>
        </el-input>
      </el-form-item>
      <div class="plf15 of_hidden">
        <el-form-item prop="verifyCode" style="width:130px;" class="fl">
          <el-input name="verifyCode" type="text" v-model="loginForm.verifyCode" @keyup.enter.native="handleLogin" autoComplete="on" placeholder="请输入验证码">
            <span slot="prefix">
              <img src="./images/code.png" />
            </span>
          </el-input>
        </el-form-item>
        <img width="115" class="fr" height="40" id="captcha" @click="getCode" :src="imgCode" title="点击更换" />
      </div>
      <el-form-item class="plf15">
        <el-button class="w100" type="primary" :loading="loading" @click.native.prevent="handleLogin">登录</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import util from "@/assets/js/public";
import { captcha } from "@/api/login.js";
export default {
  name: "login",
  computed: {
    title: function () {
      return this.$route.meta.title + "-" + process.env.VUE_APP_APPNAME;
    }
  },
  data() {
    let imgSrc;
    if (process.env.NODE_ENV == "development") {
      //开发环境
      // imgSrc = process.env.VUE_APP_DEVBASEURL;
      imgSrc = window.location.origin + '/' + process.env.VUE_APP_APPCODE;
    } else if (process.env.NODE_ENV == "debug") {
      //测试环境
      imgSrc = process.env.VUE_APP_DEBBASEURL;
    } else if (process.env.NODE_ENV == "production") {
      //生产环境
      imgSrc = process.env.VUE_APP_PROBASEURL;
    }
    return {
      imgCode: imgSrc + "/captcha",
      loginForm: {
        username: '',
        passwordm: '',
        verifyCode: ''
      },
      loginFormRules: this.getformrules({
        username: { required: true },
        passwordm: { required: true },
        verifyCode: { required: true }
      }),
      loading: false,
      pwdType: "password",
      tits: (process.env.VUE_APP_APPNAME).replace("系统","") + '系统',
    };
  },
  created() {
    // this.captchaFun();
    this.getCode();
  },
  methods: {
    captchaFun() {
      captcha().then(res => {
        let url = window.URL.createObjectURL(res.data);
        this.imgCode = url;
        // this.util.blobDownload(res.data,"gy.jpg");
      });
    },
    showPwd() {
      if (this.pwdType === "password") {
        this.pwdType = "";
      } else {
        this.pwdType = "password";
      }
    },
    getCode() {
      this.imgCode = this.imgCode.split("?")[0] + "?tm=" + (new Date()).getTime();
      // this.captchaFun();
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true;
          this.loginForm.password = util.encrypt(this.loginForm.passwordm);
          this.$store.dispatch("Login", this.loginForm).then(res => {
            this.loading = false;
            if(process.env.VUE_APP_flow && process.env.VUE_APP_flow  == 'noflow'){
              this.$router.push({path: "/welcome"});
            }else{
              this.$router.push({path: "/mywork/processTask"});
            }
          }).catch(() => {
            this.loading = false;
            this.passwordm = "";
            this.verifyCode = "";
            this.getCode();
          });
        } else {
          return false;
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.loginContainer {
  border-radius: 15px;
  background-clip: padding-box;
  margin: 180px auto;
  width: 350px;
  padding: 15px 35px 15px 35px;
  background: aliceblue;
  border: 1px solid blueviolet;
  box-shadow: 0 0 25px #f885ff;
}
.loginTitle {
  margin: 20px auto;
  text-align: center;
//   font-size: 40px;
}
.loginRemember {
  text-align: left;
  margin: 0px 0px 15px 0px;
}
.login-warpper {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  z-index: 999;
  background: url("./images/back.png") center 0 no-repeat;
  background-size: cover;
}
</style>

