<template>
  <div class="app-container" v-loading="imgLoad" style="padding-right: 220px;">
    <img width="100%" :src="processImg" @load='imgOnError' />
  </div>
</template>
<script>
import { getDiagram } from "@/api/process";
import { Loading } from 'element-ui';
export default {
  name: "ProcessDiagram",
  props: {
    gps: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      processImg: "",
      imgLoad:false
    };
  },
  created() {
    this.imgLoad = true
    // let loading = this.$loading({ lock: true,
    //       text: '加载中，请稍候...'});
    getDiagram(this.gps.processInstId).then((res) => {
      this.processImg = window.URL.createObjectURL(res.data);
      this.imgLoad = false
    });
  },
  mounted() {

  },
  methods: {
    imgOnError() {
      // console.log("完毕")
    },
  }
};
</script>
<style scoped>
.app-container {
  min-height: 500px;
}
</style>