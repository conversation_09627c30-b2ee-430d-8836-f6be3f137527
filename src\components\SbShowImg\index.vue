<template>
  <div class="demo-image">
    <div  v-for="img in item.control.imgList" :key="img.url" class="block">
      <el-image
          fit="fill"
          :src="img.type==='request'?img.url:`src/assets/images/${img.url}`"
          :style="`width:${
                img.width ? img.width +'px' : '100px'
            };height:${
                img.height ? img.height +'px': '100px'
            };`"/>
    </div>
  </div>
</template>


<script>
export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
    control: {
      type: Object,
      required: true,
    },
  }
}

</script>


<style scoped>
.demo-image {
  width: 100%;
  max-height: 330px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  overflow: scroll;
}
.demo-image .block {
  padding: 0;
  margin: 5px;
}
.demo-image .block:last-child {
  border-right: none;
}
</style>
