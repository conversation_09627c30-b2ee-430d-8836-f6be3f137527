import request from "@/assets/js/request";
import util from '@/assets/js/public';

export function login(username, password, verifyCode) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/restuumslogin`,
    data: {
      username,
      password,
      verifyCode,
      appcode: process.env.VUE_APP_APPCODE
    }
  });
}

export function captcha() {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/captcha`,
    method: "get",
    responseType: "blob",
    contentType: "application/json;charset=UTF-8"
  });
}

export function getInfo() {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/getCurrentUser`
  });
}

export function getInfoSSO(params){
  return request({
    url: util.toUrl(`/${process.env.VUE_APP_APPCODE}/getCurrentUser/sso`,params),
    method: "post",
    contentType: "application/json;charset=UTF-8"
  });
}
// 集约化sso
export function getInfoHqSSO(params){
  return request({
    url: util.toUrl(`/${process.env.VUE_APP_APPCODE}/getCurrentUser/hqsso`,params),
    method: "post",
    contentType: "application/json;charset=UTF-8"
  });
}

export function getMenus(username) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/findPermissionByAppUser`,
    data:{
      username,
      appcode: process.env.VUE_APP_APPCODE
    }
  });
}

export function logout() {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/restuumslogout`
  });
}


