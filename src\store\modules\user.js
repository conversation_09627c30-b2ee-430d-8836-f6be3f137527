import { login, logout, getInfo, getMenus, getInfoSSO,getInfoHqSSO } from "@/api/login";
import { getToken, setToken, removeToken } from "@/assets/js/auth";
import avatar_default from "@/assets/images/logo.png";
import tab from "./tab";
import router from "@/router";
import util from "@/assets/js/public";

const user = {
    state: {
        tabnav: tab.tabnav,
        token: getToken(),
        // name: '',
        // avatar: '',
        user: {},
        roles: [],
        menus: [],
        userMess: {},
        permissions: []
    },
    mutations: {
        SET_TOKEN: (state, token) => {
            state.token = token;
        },
        // SET_NAME:(state,name) => {
        //    state.name=name
        // },
        // SET_AVATAR:(state,avatar) => {
        //    state.avatar=avatar
        // },
        SET_USER: (state, user) => {
            state.user = user;
        },
        SET_ROLES: (state, roles) => {
            state.roles = roles;
        },
        SET_MENUS: (state, menus) => {
            state.menus = menus;
        },
        CLOSE_TABALL: (state) => {
            state.tabnav = [];
        },
        SET_userMess: (state, userMess) => {
            state.userMess = userMess;
        },
        SET_PERMISSIONS: (state, permissions) => {
            state.permissions = permissions;
        },
    },
    actions: {
        // 登录
		Login({ commit }, userInfo) {
			const username = userInfo.username.trim();
			const verifyCode = userInfo.verifyCode.trim();
			return new Promise((resolve, reject) => {
				login(username, userInfo.password, verifyCode).then(response => {
                    const data = response;
                    const tokenStr = data.tokenHead + data.token;
                    setToken(tokenStr);
                    commit("SET_TOKEN", tokenStr);
                    resolve(response);
                }).catch(error => {
                    reject(error);
                });
			});
		},
        // 获取用户信息
		GetInfo({ commit, state }) {
			return new Promise((resolve, reject) => {
				getInfo().then(response => {						
                    const data = response.data;
                    if (data.authRoles && data.authRoles.length > 0) {
                        commit("SET_ROLES", data);
                    } else {
                        reject("getinfo: roles must be a non-null array !");
                    }
                    // commit('SET_NAME',data.username);
                    // commit('SET_AVATAR',data.icon || avatar_default);
                    data.avatar = data.icon || avatar_default;
                    commit("SET_USER", data);
                    resolve(response);
                }).catch(error => {
                    reject(error);
                });
			});
        },
        // 获取用户信息（单点）
        GetInfoSSO({ commit, state }) {
            return new Promise((resolve, reject) => {
                let href = util.getQueryString(location.href);
                let loginuser = href.loginuser || href.uid;
                getInfoSSO({loginuser:loginuser,appcode:process.env.VUE_APP_APPCODE}).then((response) => {
                    let data = response.data;
                    let authPermissions = response.data.authPermissions;
                    let authP = []
                    for(var i=0;i<authPermissions.length;i++) {
                        if(authPermissions[i].type !== "按钮") {
                            authP.push(authPermissions[i])
                        }
                    } 
                    data.authPermissions = authP;
                    if (data.authRoles && data.authRoles.length > 0) {
                        commit("SET_ROLES", data);
                    } else {
                        reject("没有权限！");
                    }
                    setToken("tokenStr");
                    // commit('SET_NAME',data.username);
                    // commit('SET_AVATAR',data.icon || avatar_default);
                    data.avatar = data.icon || avatar_default;
                    commit("SET_USER", data);
                    resolve(response);
                }).catch((error) => {
                    reject(error);
                });
            });
        },
         // oa集约化获取用户信息(单点)
        GetInfoHqSSO({ commit, state }) {
            return new Promise((resolve, reject) => {
                let href = util.getQueryString(location.href);
                let ticket = href.ticket;
                let hqClientId =  href.hqClientId ||  href.appId || 'uni_2476_ha_ljcns';
                getInfoHqSSO({ticket:ticket,appcode:process.env.VUE_APP_APPCODE,hqClientId:hqClientId}).then((response) => {
                    let data = response.data;
                    let authPermissions = response.data.authPermissions;
                    let authP = []
                    for(var i=0;i<authPermissions.length;i++) {
                        if(authPermissions[i].type !== "按钮") {
                            authP.push(authPermissions[i])
                        }
                    } 
                    data.authPermissions = authP;
                    if (data.authRoles && data.authRoles.length > 0) {
                        commit("SET_ROLES", data);
                    } else {
                        reject("没有权限！");
                    }
                    setToken("tokenStr");
                    // commit('SET_NAME',data.username);
                    // commit('SET_AVATAR',data.icon || avatar_default);
                    data.avatar = data.icon || avatar_default;
                    commit("SET_USER", data);
                    resolve(response);
                }).catch((error) => {
                    reject(error);
                });
            });

        },
        // 登出
        LogOut({ commit, state }) {
            return new Promise((resolve, reject) => {
                logout(state.token).then((response) => {
                    commit("SET_TOKEN", "");
                    commit("SET_USER", {});
                    commit("SET_MENUS", []);
                    commit("CLOSE_TABALL");
                    if (router.options.routes.length > 3)
                        router.options.routes.splice(3);
                    resolve(response);
                }).catch((error) => {
                    reject(error);
                });
            });
        },

        // 前端 登出
        FedLogOut({ commit }) {
            return new Promise((resolve) => {
                commit("SET_TOKEN", "");
                commit("SET_MENUS", []);
                commit("CLOSE_TABALL");
                removeToken();
                resolve();
            });
        },

        // 获取菜单
        GetMenus({ commit, state }) {
            return new Promise((resolve, reject) => {
                getMenus(state.user.username).then((response) => {
                    let authPermissions = response.data;
                    let authP = []
                    let btnP = []
                    for(var i=0;i<authPermissions.length;i++) {
                        if(authPermissions[i].type !== "按钮") {
                            authP.push(authPermissions[i])
                        } else if (authPermissions[i].type === "按钮") {
                            btnP.push(authPermissions[i].permissionCode)
                        }
                    }
                    response.data = authP
                    for(var i in response.data){
                            if(response.data[i].url.indexOf("?") > -1){
                                    response.data[i].query = util.getQueryString(response.data[i].url);
                                    response.data[i].url = response.data[i].url.split("?")[0];
                            }
                    }
                    commit("SET_MENUS", []);
                    commit("CLOSE_TABALL");
                    commit("SET_PERMISSIONS", btnP);
                    resolve(response);
                }).catch((error) => {
                    reject(error);
                });
            });
        },
    },
};
export default user;
