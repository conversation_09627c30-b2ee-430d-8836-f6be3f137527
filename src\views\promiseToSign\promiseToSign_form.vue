<template>
  <div :class="gps.location?'w99':'p10'">
      <!-- <process-btn
          ref="processBtn"
          :gps="gps"
          :formData="appFormValue"
          :decisionTypeArr="decisionTypeArr"
          :formBtnsArr='formBtnsArr' 
          :dialogClose="dialogClose"
          :on-ok="handleDoFun"
      ></process-btn> -->

      <div class="message tableWarp tableForm">
          <sb-el-form ref="appForm" :key="formIndex" :gps="gps" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled"></sb-el-form>
          <div></div>
      </div>

      <div
        :style="{height:'40px',textAlign:'left',overflow:'hidden',whiteSpace:'nowrap',textOverflow:'ellipsis',paddingLeft:'5px',lineHeight:'40px',borderLeft:'5px solid #39aef5',fontSize: '16px',color: '#333',fontWeight: '700',margin: '10px 20px'}"
      >
        <span>廉洁从业承诺书</span>
        <el-button type="primary" size="small" style="float: right;margin-top: 10px;" @click="centerDialogVisible = true">点击签署</el-button>
      </div>
      <div>
        <!-- 文件预览 -->
        
      </div>

      <el-dialog
        title="温馨提示"
        :visible.sync="centerDialogVisible"
        width="40%"
        center>
        <div style="font-size: 20px;text-align: center;">
          请您登录豫移办公-审批待办进行签署
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="centerDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="centerDialogVisible = false">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 流程表单 -->
      <!-- cutomcode start -->

      <!-- cutomcode end -->
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";
import { Message, MessageBox } from "element-ui";
import ProcessBtn from "@/components/Process/ProcessBtn";
import { getDictList,getApiList } from "@/api/public";
import { deleteDraft } from "@/api/process";
import { getFormDetail, saveDraft, deleteProcess, stopProcess,initForm } from "@/api/apply/application";
import ConfigDialog from "../system/component/configDialog";


let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  creator: store.getters.user.truename
}; 

export default {
  name: 'application',
  components: { ProcessBtn, ConfigDialog },
  props: {
    href: {
      type: Object,
      default(){
        return {
            type: "read"
        };
      }
    },
    // 关闭
    dialogClose: {
      type: Function
    }
  },

  data() {
    return {
      Flag: true,
      centerDialogVisible: false,
      formIndex:0,
      gps: this.href,
      processType:  "A",
      processDefKey:  "Process_1730691457218",
      processBtnArr:  null,
      decisionTypeArr:  null,
      formBtnsArr:  null,

      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({},defaultAppFormValue),
      appForm:{
          formDisabled: false,
          labelWidth: "160px",
          size:"default",
          inline: true,
          formItemList: [],
          size:'small',
          labelPosition:"right", 
      },
      
      FormList: [
        {
          class: "c12",
          label: "",
          key: "txtHeader",
          type: "txt",
          showLabel: false,
          disabled: false,
          rule:
          {
            required: false
          },
          item:
          {
            label: "头部标题",
            showLabel: false
          },
          control:
          {
            modelValue: "廉洁从业承诺书签订",
            fontSize: 18,
            align: "center",
            color: "#0070c3",
            labelAlign: "right"
          }
        },
        {
          class: "c4",
          label: "签订年份",
          key: "createdTime",
          type: "date",
          showLabel: false,
          disabled: true,
          listConfig:
          {
            isTodo: 0,
            isDone: 0,
            spareName: null
          },
          rule:
          {
            required: true
          },
          modelValue: null,
          dateType: "currTime",
          subtype: "year",
          valueFormat: "yyyy"
        },
        {
          class: "c4",
          label: "签订截止日期",
          key: "datePicker1730702492052",
          type: "date",
          showLabel: false,
          disabled: true,
          rule:
          {
            required: true
          },
          modelValue: "",
          dateType: "fixedTime",
          subtype: "date",
          valueFormat: "yyyy-MM-dd"
        },
        {
          class: "c4",
          label: "派发人",
          key: "creator",
          type: "input",
          showLabel: false,
          disabled: true,
          listConfig:
          {
            isTodo: 0,
            isDone: 0,
            spareName: null
          },
          rule:
          {
            required: true
          }
        },
      
      ],
          // cutomcode start

          // cutomcode end
    }
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps,query);
    // console.log('gps', JSON.parse(JSON.stringify(this.gps))); 

    this.initValue = {
      applyUser: this.$store.getters.user.truename,
      applyUserName: this.$store.getters.user.username,
            belongCompanyName: this.$store.getters.user.belongCompanyTypeDictValue == '03'  ? this.$store.getters.user.belongCompanyNameParent : this.$store.getters.user.belongCompanyName,
            belongDepartmentName: this.$store.getters.user.belongDepartmentName,
            applyPhone: this.$store.getters.user.preferredMobile,
      applyTime: this.nowTime
    };
    this.appFormValue = Object.assign(defaultAppFormValue,this.initValue);
        
        if(this.createdResponse){  //表单设计器自定义初始化
            this.createdResponse()
        }
        const list = this.getFlowFormList()
        this.appForm.formItemList = list
        this.appForm.formItemList.forEach(item => {
          if(item.type=="date" && item.modelValue) {
            this.appFormValue[item.key] = this.util.getNow(item.valueFormat); //item.modelValue
          }
        })

    //廉洁从业承诺书表格
    

    this.initFun();//初始化
  },
  mounted() {},
  methods: {
    // 初始化
    initFun(){
       this.loadForm();
                
    },
    // 获取formList
    getFlowFormList() {
      return this.FormList.map(item => {
        if (item.type == 'uploadFile') {
          if (!this.gps.location || this.gps.type == "draft") {
            item.disabled = false;
          }
          if (this.gps.type == 'join' || this.gps.type == 'doRead' || this.gps.type == 'toRead') {
            item.disabled = true
          }
          if (!this.gps.modify && this.gps.type == "task" && this.gps.location !== "ljcns.start") {
            item.disabled = currentProcess ? !this.getChangeData('changeData', currentProcess)  : item.control.readonly
          }
        }
        return item
      })
    },
    getFile() {
        const filesArr = this.FormList.filter((item) => item.type == 'sbUpload')
        let filesObj = {}
        if(filesArr.length > 0){
          filesArr.forEach(element => {
              filesObj[element.fileId] = element.key
          });
        }
        return filesObj
    },
    // 获取工单详情
    loadForm(){
      var data = {
        pmInsId: this.gps.pmInsId,
        processDefKey: this.gps.processDefKey,
                file:this.getFile()
      };
      getFormDetail(data).then((res) => {
        this.appFormValue = res.data;
                this.formIndex++
                if(this.afterResponse){
                    this.afterResponse(res.data)
                }
      });
    },

    // 判断是否有增加列表业务字段
    listConfigFun(){
      var list = []
      this.FormList.forEach(element => {
        if(element.listConfig && (element.listConfig.isTodo == 1 || element.listConfig.isDone == 1) && element.listConfig.spareName){
          list.push({
            "act_field":element.listConfig.spareName,
            "us_field":element.key
          })
        }
      });
      return list
    },
    
  },
  
  mounted(){
      if (this.mountedResponse) {
        this.mountedResponse()
      }
  },
  
}
</script>
<style scoped>
.app {
    height: calc(100vh - 100px);
    padding: 10px;
}
.w99 {
  width: 99%;
  margin: 0 auto;
}
.p10 {
  padding: 0px;
}
.toptext {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.message {
  margin: 20px;
}
</style>
<style>
  
</style>                                                                                                                                                                                                                                                