import request from "@/assets/js/request";
import util from "@/assets/js/public";

// 获取Lie表相关数据
export function getRoleList(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/findRoleNameIsARoleDim?page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 获取Lie表相关数据
export function findRootAndNextRoot(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findRootAndNextRoot?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        // data: params
    });
}
// 获取Lie表相关数据
export function findOrgCodeOrgNameDim(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findSonByParentOrgId?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function addRole(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/addRole?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function updateRoleCustom(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/updateRole?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function deleteRoleCustom(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/delRole?id=${id}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
    });
}
export function findByIdRole(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/findById?id=${id}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
    });
}
export function findDimUserTree(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/findDimUserTree?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 添加用户角色
export function createRoleUsers(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/user/role/createRoleUsers?roleId=${params.roleId}&usernames=${params.usernames}`,
        contentType: "application/json; charset=utf-8",
    });
}
// shanchu用户角色
export function deleteByIdCustom(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/user/role/deleteByIdCustom?id=${id}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 启用、弃用
export function updateSysRole(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/updateSysRole?id=${params.id}&status=${params.status}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 、、查角色下用户
export function findUserByRoleR(params) {
    let url = util.toUrl(`/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/findUserByRole`, params || {});
    return request({
        url: `${url}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
//人员树
export function findOneStep(orgCode) {
    return request({
      url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/findOneStep?appcode=${process.env.VUE_APP_APPCODE}&orgCode=` + orgCode,
      contentType: 'application/json;charset=UTF-8',
	  data: {
		"appcode":process.env.VUE_APP_APPCODE
	  },
    });
  }