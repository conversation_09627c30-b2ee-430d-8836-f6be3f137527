/*******************************************************************************
 * KindEditor - WYSIWYG HTML Editor for Internet
 * Copyright (C) 2006-2011 kindsoft.net
 *
 * <AUTHOR> <<EMAIL>>
 * @site http://www.kindsoft.net/
 * @licence http://www.kindsoft.net/license.php
 * Translated to Russian by <PERSON><PERSON> (http://codersclub.org/)
 *******************************************************************************/

KindEditor.lang(
  {
    source: "Source",
    preview: "Preview",
    undo: "Отмена(Ctrl+Z)",
    redo: "Повтор(Ctrl+Y)",
    cut: "Вырезать(Ctrl+X)",
    copy: "Копировать(Ctrl+C)",
    paste: "Вставить(Ctrl+V)",
    plainpaste: "Вставить как простой текст",
    wordpaste: "Вставить из Word",
    selectall: "Выбрать все",
    justifyleft: "Выравнивание влево",
    justifycenter: "Выравнивание по центру",
    justifyright: "Выравнивание вправо",
    justifyfull: "Выравнивание по обеим сторонам",
    insertorderedlist: "Нумерованый список",
    insertunorderedlist: "Ненумерованый список",
    indent: "Добавить отступ",
    outdent: "Убрать отступ",
    subscript: "Надстрочный",
    superscript: "Подстрочный",
    formatblock: "Формат параграфа",
    fontname: "Шрифт",
    fontsize: "Размер",
    forecolor: "Цвет текста",
    hilitecolor: "Цвет фона",
    bold: "Жирный(Ctrl+B)",
    italic: "Наклонный(Ctrl+I)",
    underline: "Подчёркнутый(Ctrl+U)",
    strikethrough: "Перечёркнутый",
    removeformat: "Удалить формат",
    image: "Изображение",
    multiimage: "Мульти-загрузка",
    flash: "Flash",
    media: "Встроенные данные",
    table: "Таблица",
    tablecell: "Ячейка",
    hr: "Горизонтальный разделитель",
    emoticons: "Смайл",
    link: "Ссылка",
    unlink: "Убрать ссылку",
    fullscreen: "На весь экран",
    about: "О программе",
    print: "Печать",
    filemanager: "Файлы",
    code: "Код",
    map: "Карта Google",
    baidumap: "Карта Baidu",
    lineheight: "Межстрочный интервал",
    clearhtml: "Очистить HTML код",
    pagebreak: "Разрыв страницы",
    quickformat: "Быстрый формат",
    insertfile: "Вставить файл",
    template: "Вставить шаблон",
    anchor: "Якорь",
    yes: "OK",
    no: "Отмена",
    close: "Закрыть",
    editImage: "Свойства изображения",
    deleteImage: "Удалить изображение",
    editFlash: "Свойства Flash",
    deleteFlash: "Удалить Flash",
    editMedia: "Свойства Media",
    deleteMedia: "Удалить Media",
    editLink: "Свойства ссылки",
    deleteLink: "Удалить ссылку",
    editAnchor: "Anchor properties",
    deleteAnchor: "Delete Anchor",
    tableprop: "Свойства таблицы",
    tablecellprop: "Свойства ячейки",
    tableinsert: "Вставить таблицу",
    tabledelete: "Удалить таблицу",
    tablecolinsertleft: "Добавить столбец слева",
    tablecolinsertright: "Добавить столбец справа",
    tablerowinsertabove: "Добавить строку выше",
    tablerowinsertbelow: "Добавить строку ниже",
    tablerowmerge: "Объединить вниз",
    tablecolmerge: "Объединить вправо",
    tablerowsplit: "Разделить строку",
    tablecolsplit: "Разделить столбец",
    tablecoldelete: "Удалить столбец",
    tablerowdelete: "Удалить строку",
    noColor: "По умолчанию",
    pleaseSelectFile: "Выберите файл.",
    invalidImg:
      "Укажите корректный URL изображения.\nРазрешённые форматы: jpg,gif,bmp,png",
    invalidMedia:
      "Укажите корректный тип медиа-объекта.\nРазрешённые типы: swf,flv,mp3,wav,wma,wmv,mid,avi,mpg,asf,rm,rmvb",
    invalidWidth: "Ширина должна быть числом.",
    invalidHeight: "Высота должна быть числом.",
    invalidBorder: "Ширина рамки должна быть числом.",
    invalidUrl: "Укажите корректный URL.",
    invalidRows: "Неверные строки.",
    invalidCols: "Неверные столбцы.",
    invalidPadding: "padding должен быть числом.",
    invalidSpacing: "spacing должен быть числом.",
    invalidJson: "Неверная JSON строка.",
    uploadSuccess: "Загрузка завершена.",
    cutError:
      "Данная опция не поддерживается вашим браузером, воспользуйтесь комбинацией клавиш (Ctrl+X).",
    copyError:
      "Данная опция не поддерживается вашим браузером, воспользуйтесь комбинацией клавиш (Ctrl+C).",
    pasteError:
      "Данная опция не поддерживается вашим браузером, воспользуйтесь комбинацией клавиш (Ctrl+V).",
    ajaxLoading: "Загрузка ...",
    uploadLoading: "Загрузка ...",
    uploadError: "Сбой загрузки",
    "plainpaste.comment":
      "Для вставки скопированного текста воспользуйтесь комбинацией клавиш (Ctrl+V).",
    "wordpaste.comment":
      "Для вставки скопированного текста воспользуйтесь комбинацией клавиш (Ctrl+V).",
    "code.pleaseInput": "Введите код.",
    "link.url": "URL",
    "link.linkType": "Открывать ссылку",
    "link.newWindow": "в новом окне",
    "link.selfWindow": "в том же окне",
    "flash.url": "URL",
    "flash.width": "Ширина",
    "flash.height": "Высота",
    "flash.upload": "Загрузить",
    "flash.viewServer": "Выбрать",
    "media.url": "URL",
    "media.width": "Ширина",
    "media.height": "Высота",
    "media.autostart": "Автостарт",
    "media.upload": "Загрузить",
    "media.viewServer": "Выбрать",
    "image.remoteImage": "Вставить URL изображения",
    "image.localImage": "Загрузить",
    "image.remoteUrl": "URL",
    "image.localUrl": "Файл",
    "image.size": "Размер",
    "image.width": "Ширина",
    "image.height": "Высота",
    "image.resetSize": "Сбросить размеры",
    "image.align": "Выравнивание",
    "image.defaultAlign": "По умолчанию",
    "image.leftAlign": "Влево",
    "image.rightAlign": "Вправо",
    "image.imgTitle": "Название",
    "image.upload": "Загрузить",
    "image.viewServer": "Выбрать",
    "multiimage.uploadDesc":
      "Максимальное кол-во изображений: <%=uploadLimit%>, Максимальный размер одного изображения: <%=sizeLimit%>",
    "multiimage.startUpload": "Начать загрузку",
    "multiimage.clearAll": "Очистить все",
    "multiimage.insertAll": "Вставить все",
    "multiimage.queueLimitExceeded": "Превышен лимит очереди.",
    "multiimage.fileExceedsSizeLimit": "Превышен максимальный размер файла.",
    "multiimage.zeroByteFile": "Файл нулевой длины.",
    "multiimage.invalidFiletype": "Недопустимый тип файла.",
    "multiimage.unknownError": "Непредвиденная ошибка загрузки.",
    "multiimage.pending": "Ожидает ...",
    "multiimage.uploadError": "Ошибка загрузки",
    "filemanager.emptyFolder": "Папка пуста",
    "filemanager.moveup": "Наверх",
    "filemanager.viewType": "Тип показа: ",
    "filemanager.viewImage": "Превьюшки",
    "filemanager.listImage": "Список",
    "filemanager.orderType": "Сортировка: ",
    "filemanager.fileName": "По имени",
    "filemanager.fileSize": "По размеру",
    "filemanager.fileType": "По типу",
    "insertfile.url": "URL",
    "insertfile.title": "Название",
    "insertfile.upload": "Загрузить",
    "insertfile.viewServer": "Выбрать",
    "table.cells": "Ячейки",
    "table.rows": "Строки",
    "table.cols": "Столбцы",
    "table.size": "Размеры",
    "table.width": "Ширина",
    "table.height": "Высота",
    "table.percent": "%",
    "table.px": "px",
    "table.space": "Space",
    "table.padding": "Padding",
    "table.spacing": "Spacing",
    "table.align": "Выравнивание",
    "table.textAlign": "По горизонтали",
    "table.verticalAlign": "По вертикали",
    "table.alignDefault": "По умолчанию",
    "table.alignLeft": "Влево",
    "table.alignCenter": "По центру",
    "table.alignRight": "Вправо",
    "table.alignTop": "Вверх",
    "table.alignMiddle": "Посередине",
    "table.alignBottom": "Вниз",
    "table.alignBaseline": "По базовой линии",
    "table.border": "Рамка",
    "table.borderWidth": "Ширина",
    "table.borderColor": "Цвет",
    "table.backgroundColor": "Цвет фона",
    "map.address": "Адрес: ",
    "map.search": "Поиск",
    "baidumap.address": "Адрес: ",
    "baidumap.search": "Поиск",
    "baidumap.insertDynamicMap": "Динамическая карта",
    "anchor.name": "Имя якоря",
    "formatblock.formatBlock": {
      h1: "Заголовок 1",
      h2: "Заголовок 2",
      h3: "Заголовок 3",
      h4: "Заголовок 4",
      p: "Обычный текст"
    },
    "fontname.fontName": {
      Arial: "Arial",
      "Arial Black": "Arial Black",
      "Comic Sans MS": "Comic Sans MS",
      "Courier New": "Courier New",
      Garamond: "Garamond",
      Georgia: "Georgia",
      Tahoma: "Tahoma",
      "Times New Roman": "Times New Roman",
      "Trebuchet MS": "Trebuchet MS",
      Verdana: "Verdana"
    },
    "lineheight.lineHeight": [
      { "1": "1" },
      { "1.5": "1.5" },
      { "2": "2" },
      { "2.5": "2.5" },
      { "3": "3" }
    ],
    "template.selectTemplate": "Шаблон",
    "template.replaceContent": "Заменить текущий шаблон",
    "template.fileList": {
      "1.html": "Текст и изображения",
      "2.html": "Таблица",
      "3.html": "Список"
    }
  },
  "en"
);

KindEditor.each(KindEditor.options.items, function(i, name) {
  if (name == "baidumap") {
    KindEditor.options.items[i] = "map";
  }
});
KindEditor.options.langType = "ru";
