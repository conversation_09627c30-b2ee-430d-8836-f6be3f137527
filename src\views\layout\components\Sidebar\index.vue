<template>
    <div>
        <div v-if="sidebar.opened" class="menuLogo">
            <!-- <img v-if="logo" :src="logo" alt style="width:139px;height:40px;" />
            <img height="40" v-else :src="require('@/assets/images/logo2.png')" alt /> -->
            <span v-if="appType == 2" style="width:15px"></span>
            <template v-else>
                <img class="titPic" src="../../../../assets/images/logo_all.png" th:src="@{/images/logoPic.png}" :alt="tits"/>
                <span class="tit"></span>
            </template>
            <span class="tits">{{tits}}</span>
        </div>
        <scroll-bar class="menuLeft">
            <!-- :default-openeds="['customer']" -->
            <!-- background-color="#fff" -->
            <el-menu
                ref="menu"
                mode="vertical"
                :unique-opened="true"
                :show-timeout="200"
                :default-active="$route.path"
                :collapse="isCollapse"
                text-color="#515151"
                active-text-color="#507AF6"
                >
                <sidebar-item :menus="leftMenus"></sidebar-item>
            </el-menu>
        </scroll-bar>
    </div>
</template>

<script>
import { mapGetters } from "vuex";
import SidebarItem from "./SidebarItem";
import ScrollBar from "@/components/ScrollBar";

export default {
    components: { SidebarItem, ScrollBar },
    computed: {
        ...mapGetters(["sidebar", "menus", "tabnav"]),
        leftMenus() {
            var path = this.$route.path.split("/")[1];
            var item = this.menus.find((ti) => ti.name == path);
            if (path == "home") item = this.menus[0];
            var leftM = this.$store.state.user.menus || [];
            if(process.env.VUE_APP_Sidebar == "top"){
                if (item) {
                    if (item.children) leftM = item.children;
                    else leftM = [item];
                }
            }
            return leftM;
        },
        isCollapse() {
            return !this.sidebar.opened;
        },
        appType() {
            return process.env.VUE_APP_Type;
        },
    },
    data() {
        return {
            tits: (process.env.VUE_APP_APPNAME).replace("系统",""),
            logo: this.$store.getters.user.pcLogoUrl,
        };
    },
    //监视
    watch: {
        //检测路由参数发生改变时，刷新当前页面 调用
        //  '$route': function(){
        //       this.reload();
        //   }
    },
    methods: {
        handleselect: function(a, b) {
            // 用handleselect方法时，点击面包屑页面会刷新两次---加上点击tab不会刷新，点击一个菜单会把反有的tab都刷新
            // this.reload(); // 点击侧边栏重新载入页面
        },
    },
};
</script>
<style scoped>
.menuLogo {
    display: flex;
    align-items: center;
    width: 380px;
    height: 54px;
    text-align: center;
    background: rgba(192, 0, 0, 1);
}
.titPic {
    width: 127px;
    height: 32px;
}
.tit {
    width: 1px;
    height: 30px;
    background: #e9e9e9;
    margin: 0 6px 0 8px;
}
.tits {
    font-size: 21px;
    color: #f7f7f7;
    font-weight: bold;
}
.menuLeft {
    width: 100%;
    background: #fff;
    border: 1px solid #d9d9d9;
    height: 95%;
}
.menuLeft ::v-deep  .scroll-wrapper {
    padding-top: 24px;
}
.menuLeft ::v-deep .el-menu-item.is-active,
.menuLeft ::v-deep .el-menu-item.is-active:hover {
  background: rgba(192, 0, 0, .25) !important;
  color: rgba(192, 0, 0, 1) !important;
  position: relative;
}
.menuLeft ::v-deep .el-menu-item.is-active:before {
  position: absolute;
  left: 0;
  top: 0;
  content: '';
  width: 4px;
  height: 100%;
  background: rgba(192, 0, 0, 1);
  z-index: 10;
}
.menuLeft ::v-deep  .el-menu-item.is-active.elMenuItem {
    border-left: 0 none;
}
.menuLeft ::v-deep .sidebar-container .el-submenu .el-menu-item {
  background: rgba(192, 0, 0, 1) !important;
  color: #fff !important;
}
.menuLeft ::v-deep  .el-submenu.is-active .el-menu {
    background: #f2f2f2 !important;
}

</style>
