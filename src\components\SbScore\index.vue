<template>
	<div class="w100 s-box">
		<div class="flex" v-for="(item, index) in dataList" :key="index">
			<div class="s-1">{{ item.title }}<span style="color: rgba(255, 59, 48, 1);">*</span></div>
			<div class="s-2">
				<el-radio-group v-model="item.checkVal" @change="radioChange">
					<el-radio class="s-3" :label="item.id + ',' + a.value + ',' + a.name" v-for="(a, i) in item.scoreList" :key="i">{{a.value}} {{ a.name }}</el-radio>
				</el-radio-group>
			</div>
		</div>
		<div class="flex">
			<div class="s-1">总分</div>
			<div class="s-2 s-4">
				{{ allScore > 0 ? allScore + '分' : '' }}
			</div>
		</div>
		<div class="flex">
			<div class="s-1">意见或建议 <span style="color: rgba(255, 59, 48, 1);">*</span></div>
			<div class="s-2">
				<el-input @input="textareaInput" type="textarea" :autosize="{ minRows: 2, maxRows: 10}" placeholder="具体描述待改进的地方" v-model="formItem.suggest">
				</el-input>
			</div>
		</div>
	</div>
</template>
<script>
	import { getDataInfoByFindAll } from '@/api/public'
	export default {
		name: "time-status",
		props: {
			// 查询接口地址
			findAll: {
				type: String,
				default: ''
			},
			// 字典数据
			options: {
				type: Array,
				default: []
			},
			// 组件绑定的值
			item: {
				type: Object,
				default: {}
			}
		},
		data() {
			return {
				dataList: [],
				formItem: {},
				inputTimer: null
			}
		},
		computed: {
			allScore() {
				return this.dataList.reduce((prev, cur) => {
					if (cur.checkVal) {
						return prev + Number(cur.checkVal.split(',')[1])
					} else {
						return prev
					}
				}, 0)
			}
		},
		created() {
			this.formItem = { ...this.item }
			this.$emit('handleScoreData', {
				allScore: this.allScore,
				suggest: this.formItem.suggest,
				selectIds: '',
			})

			if (this.findAll) {
				this.getList()
			}
		},
		methods: {
			textareaInput(evt) {
				clearTimeout(this.inputTimer)
				
				this.inputTimer = setTimeout(() => {
					this.$emit('handleScoreData', {
						allScore: this.allScore,
						suggest: this.formItem.suggest,
						selectIds: this.dataList?.filter(a => a.checkVal)?.map(a => a.checkVal)?.join('/'),
					})
				}, 1000)
			},
			radioChange(evt) {
				this.$emit('handleScoreData', {
					allScore: this.allScore,
					suggest: this.formItem.suggest,
					selectIds: this.dataList?.filter(a => a.checkVal)?.map(a => a.checkVal)?.join('/'),
				})
			},
			getList() {
				getDataInfoByFindAll(this.findAll).then(res => {
					res.data.dataList.forEach(item => {
						item.scoreList = this.options
					})
					this.dataList = res.data.dataList
				})
			}
		}
	};
</script>
<style scoped>
	.s-box {
		border-top: 1px solid #eee;
		border-left: 1px solid #eee;
	}

	.s-1 {
		width: 30%;
		padding: 10px 15px;
		border-right: 1px solid #eee;
		border-bottom: 1px solid #eee;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.s-2 {
		border-right: 1px solid #eee;
		border-bottom: 1px solid #eee;
		width: 70%;
	}

	.s-3 {
		flex: 1;
		padding: 10px 15px;
		text-align: center;
	}

	.s-4 {
		padding: 10px 0;
		font-size: 16px;
		text-align: center;
	}

	.bg-gray {
		background: #eee;
	}

	.bg-blue {
		background: #99d7fc;
	}

	.flex {
		display: flex;
	}

	.a-c {
		align-items: center;
	}

	.j-c {
		justify-content: center;
	}

	.j-s {
		justify-content: space-between;
	}

	.el-radio-group {
		display: flex;
	}

	.h20 {
		height: 20px;
	}
</style>