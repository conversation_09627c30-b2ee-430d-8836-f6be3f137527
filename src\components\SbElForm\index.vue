<template>
    <el-form
        :key="keyFlag"
        :model="value"
        :inline="form.inline || false"
        :size="form.size || 'small'"
        :rules="formRules"
        :label-width="form.labelWidth"
        :label-position="form.labelPosition || 'left'"
        :disabled="form.formDisabled || false"
        :hide-required-asterisk="true"
    >
        <sb-el-form-item
            v-for="item in form.formItemList"
            :key="item.key"
            :lw="item.class && item.class.indexOf('labelACol') > -1? '100%': form.labelWidth || '130px'"
            :item="item"
            :value="value[item.key]"
            :keyVal="value[item.key]"
            :formVal="value"
            :gps="gps"
            @input="handleInput($event, item)"
            @uploadUpdate="uploadUpdate"
            @inputBtn="inputBtn"
            :on-ok="handleDoFun"
        >
            <template v-if="item.template" v-slot:[item.template]="obj">
                <slot
                    :name="item.template"
                    :obj="handleTemplateProp(obj)"
                ></slot>
            </template>
        </sb-el-form-item>
        <!--v-if="value[item.key]!==undefined"  :rules="form.rules"-->
    </el-form>
</template>
<script>
import SbElFormItem from "./components/SbElFormItem";
export default {
    name: "sb-el-form",
    components: { SbElFormItem },
    props: {
        form: {
            type: Object,
            required: true,
        },
        value: {
            type: Object,
            required: true,
        },
        onOk: {
            type: Function,
        },
        from: {
            type: Boolean,
            default: false,
        },
        gps: {
            type: Object,
        },
    },
    computed: {
        formRules: function() {
            let rs = {};
            if (this.form) {
                for (let i in this.form.formItemList) {
                    if (this.form.formItemList[i].rule)
                        rs[
                            this.form.formItemList[i].key
                        ] = this.form.formItemList[i].rule;
                }
            }
            return this.getformrules(rs);
        },
    },
    data(){
        return {
            keyFlag: 0
        }
    },
    methods: {
        handleTemplateProp(obj) {
            let nObj = obj.obj;
            nObj.value = this.value[nObj.item.key];
            nObj.formValue = this.value;
            return nObj;
        },
        handleInput(val, item) {
            if (
                (item.type === "date" || item.type === "time") &&
                item.rangeName
            ) {
                for (let i in item.rangeName) {
                    this.value[item.rangeName[i]] = val ? val[i] : "";
                    //this.$emit('input',{...this.value,[item.rangeName[i]]:val[i]});
                }
            }

            if (
                (item.type === "card") &&
                item.list
            ) {
                for (let i in item.list) {
                    // this.value[item.list[i].name] = val ? val[i] : "";
                    if(val[item.list[i].name]){
                        this.$emit("input", { ...this.value, [item.list[i].name]: val[item.list[i].name] });
                    }
                    
                }
                // delete this.value[item.key]
            }else{
                 // 这里element-ui没有上报event，直接就是value了
                this.$emit("input", { ...this.value, [item.key]: val });
            }
           
        },
        setDefalutValue() {
            const formData = { ...this.value };
            this.form.formItemList.forEach((item) => {
                const { key, value } = item;
                if (
                    formData[key] === undefined ||
                    formData[key] === null ||
                    formData[key] === ""
                ) {
                    formData[key] = value;
                }
                if (item.type === "upload" || item.type === "sbUpload") {
                    if (
                        typeof formData[key] === "string" &&
                        item.listType && item.listType.indexOf("picture") > -1
                    ) {
                        formData[key] =
                            formData[key] !== ""
                                ? formData[key].split(",")
                                : [];
                    }
                    item.filelist = formData[key];
                }
                if (item.type === "qrcode") {
                    item.text = formData[key];
                }
                if (item.type === "province") {
                    let rangeName = ["province", "city", "county", "location"];
                    if (item.rangeName) rangeName = item.rangeName.split(",");
                    if (formData[rangeName[0]]) {
                        formData[key] = {
                            province: formData[rangeName[0]],
                            city: formData[rangeName[1]],
                            county: formData[rangeName[2]],
                            location: formData[rangeName[3]],
                        };
                    }
                    item.defValue = formData[key];
                }
            });
            this.form.formItemList = JSON.parse(
                JSON.stringify(this.form.formItemList)
            );
            this.$emit("input", formData);
            //this.$emit('handleInputBtn',{fun:'updateTableData',form:{...this.form}});
        },
        uploadUpdate(data) {
            this.value[data.key] = data.list;
            //this.$set(this.value,data.key,data.list);a
            //this.$nextTick(function () {
            //    console.log(data.list) // => '更新完成'
            //})
            this.$emit("input", { ...this.value, [data.key]: data.list });
            let index = this.form.formItemList.findIndex(
                (item) => item.key === data.key
            );
            if (index > -1) {
                if (
                    this.form.formItemList[index].type === "sbUpload" ||
                    this.form.formItemList[index].type === "upload"
                ) {
                    if (
                        typeof data.list === "string" &&
                        this.form.formItemList[index].listType &&
                            this.form.formItemList[index].listType.indexOf(
                                "picture"
                            ) > -1
                    ) {
                        data.list =
                            data.list !== "" ? data.list.split(",") : [];
                    }
                    this.form.formItemList[index].filelist = data.list;
                }
                if (this.form.formItemList[index].type === "qrcode") {
                    this.form.formItemList[index].text = data.list;
                }
                if (this.form.formItemList[index].type === "province") {
                    this.form.formItemList[index].defValue = data.list;
                }
                this.form.formItemList = JSON.parse(
                    JSON.stringify(this.form.formItemList)
                );
                //this.$emit('handleInputBtn',{fun:'updateTableData',form:{...this.form}});
            }else{
                if(JSON.stringify(this.form.formItemList).indexOf("data.key") > -1){
                    this.form.formItemList = JSON.parse(
                        JSON.stringify(this.form.formItemList)
                    );
                }
            }
        },
        inputBtn(data) {
            if (this.from) {
                this.$emit("handleInputBtn", data);
            } else {
                this.$emit(data.fun, data);
            }
        },
        handleDoFun(obj, fun, data) {
            if (this.onOk) {
                if (obj[fun]) {
                    let n = this.onOk(obj, fun, data);
                    if (fun === "beforeFun") return n;
                }
            } else {
                if (obj[fun]) this.$emit(obj[fun], { obj, data });
            }
            if (fun === "beforeFun") return true;
        },
    },
    mounted() {
        if (this.form.inline) {
            for (let i in this.form.formItemList) {
                this.form.formItemList[i].inline = this.form.inline;
            }
        }
        this.setDefalutValue();

        // 防止一进页面就校验报红
        this.keyFlag++;


        console.log(this.value);
    },
    watch: {
        value: {
            handler: function(newV, oldV) {
                if (newV) {
                    // console.log("form值更新啦！",JSON.stringify(newV));
                    for (let i in this.form.formItemList) {
                        if (
                            this.form.formItemList[i].type === "time" &&
                            this.form.formItemList[i].rangeName &&
                            !newV[this.form.formItemList[i].key]
                        ) {
                            //修改时组合控件值
                            let times = [];
                            let now = this.util
                                .getNow("yyyy-MM-dd", true)
                                .split("-");
                            for (let j in this.form.formItemList[i].rangeName) {
                                if (
                                    newV[this.form.formItemList[i].rangeName[j]]
                                ) {
                                    let rd = newV[
                                        this.form.formItemList[i].rangeName[j]
                                    ].split(":");
                                    times.push(
                                        new Date(
                                            now[0],
                                            now[1],
                                            now[2],
                                            rd[0],
                                            rd[1],
                                            rd[2] || 0
                                        )
                                    );
                                } else {
                                    times.push(
                                        new Date(
                                            now[0],
                                            now[1],
                                            now[2],
                                            0,
                                            0,
                                            0
                                        )
                                    );
                                }
                            }
                            this.value[this.form.formItemList[i].key] = times;
                        }
                        if (
                            this.form.formItemList[i].type === "date" &&
                            this.form.formItemList[i].subtype ===
                                "datetimerange" &&
                            this.form.formItemList[i].rangeName &&
                            !newV[this.form.formItemList[i].key]
                        ) {
                            //修改时组合控件值
                            let times = [];
                            for (let j in this.form.formItemList[i].rangeName) {
                                if (
                                    newV[this.form.formItemList[i].rangeName[j]]
                                ) {
                                    let rnD = newV[
                                        this.form.formItemList[i].rangeName[j]
                                    ].split(" ");
                                    let rd = rnD[0].split("-");
                                    let rt = ["0", "0", "0"];
                                    if (rnD.length > 1) rt = rnD[1].split(":");
                                    // let t=new Date(rd[0], rd[1], rd[2], rt[0], rt[1], rt[2]);
                                    times.push(
                                        this.util.getDateFormat(
                                            rd[0] +
                                                "-" +
                                                rd[1] +
                                                "-" +
                                                rd[2] +
                                                " " +
                                                rt[0] +
                                                ":" +
                                                rt[1] +
                                                ":" +
                                                (rt[2] || "00"),
                                            this.form.formItemList[i]
                                                .valueFormat ||
                                                "yyyy-MM-dd hh:mm:ss"
                                        )
                                    );
                                }
                            }
                            this.value[this.form.formItemList[i].key] = times;
                        }
                        if (this.form.formItemList[i].type === "province") {
                            let rangeName = [
                                "province",
                                "city",
                                "county",
                                "location",
                            ];
                            if (this.form.formItemList[i].rangeName)
                                rangeName = this.form.formItemList[
                                    i
                                ].rangeName.split(",");
                            if (!newV[this.form.formItemList[i].key]) {
                                if (
                                    this.value[rangeName[0]] ||
                                    this.value[rangeName[1]] ||
                                    this.value[rangeName[2]]
                                ) {
                                    this.value[
                                        this.form.formItemList[i].key
                                    ] = {
                                        province: this.value[rangeName[0]],
                                        city: this.value[rangeName[1]],
                                        county: this.value[rangeName[2]],
                                        location: this.value[rangeName[3]],
                                    };
                                } else {
                                    this.value[
                                        this.form.formItemList[i].key
                                    ] = null;
                                }
                                this.form.formItemList[i].defValue = this.value[
                                    this.form.formItemList[i].key
                                ];
                                this.form.formItemList = JSON.parse(
                                    JSON.stringify(this.form.formItemList)
                                );
                            } else {
                                for (let j in rangeName) {
                                    this.value[rangeName[j]] = newV[
                                        this.form.formItemList[i].key
                                    ]
                                        ? newV[this.form.formItemList[i].key][
                                              rangeName[j]
                                          ]
                                        : null;
                                }
                            }
                            this.$emit("input", this.value);
                        }
                        if (
                            (this.form.formItemList[i].type ===
                                "chooseUsername" ||
                                this.form.formItemList[i].type ===
                                    "chooseTeacher" ||
                                this.form.formItemList[i].type ===
                                    "chooseOrg" ||
                                this.form.formItemList[i].type ===
                                    "group" ||
                                this.form.formItemList[i].type ===
                                    "org"||
                                this.form.formItemList[i].type ===
                                    "department"||
                                this.form.formItemList[i].type ===
                                    "company"||
                                this.form.formItemList[i].type ===
                                    "user" ||
                                this.form.formItemList[i].type ===
                                    "interiorDialog") &&
                            this.value[this.form.formItemList[i].key]
                        ) {
                            if (
                                typeof this.value[
                                    this.form.formItemList[i].key
                                ] !== "string"
                            ) {
                                var datas = this.value[
                                    this.form.formItemList[i].key
                                ];
                                var kArray = this.form.formItemList[
                                    i
                                ].relevancy.split(",");
                                for (let j in kArray) {
                                    var kad = [];
                                    var kaj = kArray[j].split("-");
                                    if (kaj.length === 1) kaj.push(kaj[0]);
                                    for (let di in datas) {
                                        kad.push(datas[di][kaj[1]]);
                                    }
                                    this.value[kaj[0]] = kad.join(",");
                                }
                            }
                        }
                        if (
                            this.form.formItemList[i].type === "sbUpload" ||
                            this.form.formItemList[i].type === "upload"
                        ) {
                            if (
                                typeof newV[this.form.formItemList[i].key] ===
                                    "string" &&
                                this.form.formItemList[i].listType &&
                                    this.form.formItemList[i].listType.indexOf(
                                        "picture"
                                    ) > -1
                            ) {
                                this.form.formItemList[i].filelist =
                                    newV[this.form.formItemList[i].key] !== ""
                                        ? newV[
                                              this.form.formItemList[i].key
                                          ].split(",")
                                        : [];
                            } else {
                                if (
                                    this.form.formItemList[i].type === "upload"
                                ) {
                                    for (let j in newV[
                                        this.form.formItemList[i].key
                                    ]) {
                                        if (
                                            !newV[
                                                this.form.formItemList[i].key
                                            ][j].name
                                        )
                                            newV[this.form.formItemList[i].key][
                                                j
                                            ].name =
                                                newV[
                                                    this.form.formItemList[
                                                        i
                                                    ].key
                                                ][j].fileName;
                                        if (
                                            !newV[
                                                this.form.formItemList[i].key
                                            ][j].url
                                        )
                                            newV[this.form.formItemList[i].key][
                                                j
                                            ].url =
                                                this.util.getFile() +
                                                newV[
                                                    this.form.formItemList[i]
                                                        .key
                                                ][j].downLoadUrl;
                                    }
                                }
                                this.form.formItemList[i].filelist =
                                    newV[this.form.formItemList[i].key] || [];
                            }
                            this.form.formItemList = JSON.parse(
                                JSON.stringify(this.form.formItemList)
                            );
                        }
                    }
                }
            },
            deep: true,
            immediate: true,
        },
        form: {
            handler: function(newV, oldV) {
                if (this.form.inline) {
                    this.form.formItemList.forEach((item) => {
                        item.inline = true;
                    });
                }
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>
<style>
.el-form--inline .el-form-item__label {
    float: left;
    font-weight: bold;
}
.labelACol .el-form-item .el-form-item__label {
    float: none;
    width: 100%;
    font-weight: bold;
    text-align: left;
    color: #333;
    border-left: 3px solid #409eff;
    padding-left: 8px;
    border-bottom: 1px solid #ddd;
}
.el-form--inline .el-form-item__content {
    display: flex;
}
.el-radio-group,
.el-radio {
    line-height: inherit;
}
.el-form--inline .el-form-item {
    width: 100%;
}
.el-cascader {
    width: 100%;
}
.el-radio {
    float: left;
}
.el-radio-group {
    width: 100%;
    min-width: 170px;
}
.flowBtn {
    background: #f2f2f2;
    border-left: 5px solid#39aef5;
    border-radius: 0 2px 2px 0;
    line-height: 22px;
    overflow: hidden;
    padding: 10px 15px;
}
.el-switch {
    height: 32px;
    line-height: 32px;
}
/* .el-input.is-disabled .el-input__inner,.el-select .el-input.is-disabled .el-input__inner:hover,
.el-textarea.is-disabled .el-textarea__inner,.el-textarea.is-disabled .el-textarea__inner:hover{border-color:#fff;padding-left:0;}
.el-select .el-input.is-disabled .el-select__caret{color:#fff;} */

</style>
<style scoped>
::v-deep .el-form-item__label::before {
    vertical-align: middle;
    content: ' *';
    color: #DDF1FE;
    font-size: 100%;
}
::v-deep .is-required .el-form-item__label::after {
    vertical-align: middle;
    content: ' *';
    color: rgba(255, 59, 48, 1);
    font-size: 100%;
}
</style>