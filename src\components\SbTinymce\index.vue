<template>
	<div style="width: 100%;">
		<tinymce-editor :id="tinymceId" :value="tinymceValue" :init="init" :witdh="width" :height="height" :resize="resize"
			:disabled="disabled" @input="onInput" />
	</div>
</template>

<script>
	import tinymce from 'tinymce/tinymce'
	import TinymceEditor from '@tinymce/tinymce-vue'
	// 引入配置信息
	import defaultConfig from './config'

	export default {
		name: 'Editor',
		components: {
			TinymceEditor
		},
		props: {
			id: {
				type: String,
				default: function() {
					// 这个id一定要写，否则会出现莫名其妙的问题。
					return 'tinymce-' + Date.now() + Math.floor(Math.random() * 1000)
				}
			},
			// 内容
			content: {
				type: String,
				default: ''
			},
			// 是否禁用
			disabled: {
				type: Boolean,
				default: false
			},
			// 宽度
			width: {
				type: [String, Number],
				default: '100%'
			},
			// 高度
			height: {
				type: [String, Number],
				default: 300
			},
			// 是否允许拖动
			resize: {
				type: [String, Boolean],
				default: true
			},
			// 菜单栏
			menubar: {
				type: String,
				default: ''
			},
			// 工具栏
			toolbar: {
				type: String,
				default: ''
			}
		},
		data() {
			return {
				tinymceValue: '',
				tinymceId: this.id,
				init: Object.assign(defaultConfig, {
					// 组件值覆盖默认配置
					width: this.width,
					height: this.height,
					resize: this.resize,
					menubar: !this.menubar ? defaultConfig.menubar : this.menubar,
					toolbar: !this.toolbar ? defaultConfig.toolbar : this.toolbar
				})
			}
		},
		mounted() {
			setTimeout(() => {
				this.tinymceValue = this.htmlDecode(this.content)
			}, 300)
		},
		beforeDestroy() {
			tinymce.remove()
		},
		methods: {
			//处理转义字符
			htmlDecode(value) {
				if (typeof value == "string")
					value = value.replace(new RegExp("&nbsp;", "g"), "&nb-sp;");
				var frameDiv = document.createElement("div");
				frameDiv.innerHTML = value;
				value = frameDiv.innerText;
				value = value.replace(new RegExp("&nb-sp;", "g"), "&nbsp;");
				if (typeof value == "string") {
					var dec = [
						"&ldquo;-“",
						"&rdquo;-”",
						"&lsquo;-‘",
						"&rsquo;-’",
						'&quot;-"',
						"&#39;-'",
						"&acute;-´",
						"&lt;-<",
						"&gt;->",
						"&laquo;-«",
						"&raquo;-»",
						"&lsaquo;-‹",
						"&rsaquo;-›"
					];
					for (var i in dec) {
						var decA = dec[i].split("-");
						if (value.indexOf(decA[0]) > -1)
							value = value.replace(new RegExp(decA[0], "g"), decA[1]);
					}
				}
				return value;
			},
			onInput(content) {
				this.$emit("update:content", content);
				// this.$emit("on-content-change", content);
				this.$emit("input", content);
			}
		}
	}
</script>