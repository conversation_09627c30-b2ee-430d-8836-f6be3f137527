<template>
    <div class="upload_D"><!-- v-bind="$attrs" v-on="$listeners"-->
        <div v-if="!upload.disabled" class="upload_Btn">
            <input ref="sbUpload" type='file' :multiple="upload.multiple?'multiple':false" :accept="(upload.listType && upload.listType.indexOf('picture')>-1)?'image/*':(upload.accept || '*')" @change="chooseFile"/>
            <el-button class="uploadB" :size="upload.size || 'small'" type="primary">{{upload.btnText || '文件上传'}}</el-button>
        </div>
        <div v-else>
            <span style="color:#C0C4CC;" v-if="!upload.filelist[0]">{{'暂无'+upload.label}}</span>
        </div>
        <div class="clear"></div>
        <ul v-if="!upload.listType || (upload.listType && upload.listType!=='data')" :class="'el-upload-list'+((upload.listType && upload.listType.indexOf('picture')>-1)?' el-upload-list--picture-card':'')">
            <li v-for="(item,index) in fileList" :key="index" class="el-upload-list__item is-success"> 
                <img v-if="upload.listType && upload.listType.indexOf('picture')>-1" :src="handleImgUrl(item)" class="el-upload-list__item-thumbnail"/>
                <a v-if="!upload.listType || upload.listType==='text'" @click="handleFileToSee(item,index)" class="el-upload-list__item-name"><i class="el-icon-document"></i>{{item.fileName}}</a>
                <label class="el-upload-list__item-status-label"><i :class="'el-icon-upload-success '+((upload.listType && upload.listType.indexOf('picture')>-1)?'el-icon-check':'el-icon-circle-check')"></i></label>
                <i v-if="(!upload.listType || upload.listType==='text') && (!upload.disabled)" class="el-icon-close" @click="handleRemove(item,index)"></i>
                <span v-if="(upload.listType && upload.listType.indexOf('picture')>-1)" class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview" @click="handleImgToBig(item,index)"><i class="el-icon-zoom-in"></i></span>
                    <span v-if="!upload.disabled" class="el-upload-list__item-delete" @click="handleRemove(item,index)"><i class="el-icon-delete"></i></span>
                </span>
            </li>
        </ul>
        <el-dialog destroy-on-close v-dialogDrag :visible.sync="imgToBig" width="60%" append-to-body>
            <img style='width:100%;' :src="imgToSeeUrl"/>
        </el-dialog>
    </div>
</template>
<script>
import {uploadProcessFiles} from "@/api/public";
import EXIF from 'exif-js';
import { Loading } from 'element-ui';
export default {
    name:'SbUpload',
    props:{
        upload:{
            type:Object,
            required:true
        },
        onOk:{
            type:Function
        },
        from:{
            type:Boolean
        }        
    },
    data(){
        return {
            imgToBig:false,
            imgToSeeUrl:"",
            fileList:this.upload.filelist || [],
            reqs:[],
            valide:false
        }
    },
    created(){
        //Loading.service({text:"文件正在上传，请稍候！"});
    },
    methods:{
        handleImgUrl(item){
            if(typeof item==='object'){
                //return '/'+process.env.VUE_APP_APPCODE+item.downLoadUrl;
                return item.anonymousFilePath;
            }
            //return '/'+process.env.VUE_APP_APPCODE+item;
            return item;
        },
        handleImgToBig(item,index){            
            if(typeof item==='object'){                
                //this.imgToSeeUrl='/'+process.env.VUE_APP_APPCODE+item.downLoadUrl;
                this.imgToSeeUrl=item.anonymousFilePath;
            }else{
                //this.imgToSeeUrl='/'+process.env.VUE_APP_APPCODE+item;
                this.imgToSeeUrl=item;
            }
            this.imgToBig=true;
        },
        handleFileToSee(item,index){
            if(process.env.VUE_APP_FILEOPEN && process.env.VUE_APP_FILEOPEN==='false') 
                this.util.fileDownload(item.id);
            else
                this.util.fileOpen(item.id);
        },
        handleInof(){
            let info={has:false};
            let infoA=[];
            if (this.upload.listType && this.upload.listType==='picture'){
                infoA.push('只能上传图片');
            }
            if(this.upload.filesize){
                infoA.push('上传文件大小不能超过'+(this.upload.filesize || 10240)+'kb');
            }
            info.info=infoA.join(";")
            return info;
        },
        chooseFile(e){
            let etf=e.target.files,et=e.target;
            let cFL=etf.length+this.fileList.length;
            if(cFL>(this.upload.limit || 20)){// || (this.upload.limit && cFL>this.upload.limit)
                this.$message({
                    type: 'error',
                    message:'最多允许上传'+(this.upload.limit || 20)+'个文件！'
                });
                //清空上传文件框
                e.target.vlaue="";
                this.valide=false;
                return;
            }            
            if(this.upload.imgmaxSize || this.upload.imgminSize){
                for(let i=0;i<etf.length;i++){      
                    if(etf[i].size>(this.upload.imgmaxSize*1024)){  
                        let info='上传文件大小不能超过'+this.upload.imgmaxSize+'kb';
                        if(this.upload.imgminSize) info+=("，不能低于"+this.upload.imgminSize+"kb");                  
                        this.$message({
                            type: 'error',
                            message:info
                        });
                        //清空上传文件框
                        e.target.vlaue="";
                        this.valide=false;
                        return;
                    } 
                    if(etf[i].size<(this.upload.imgminSize*1024)){    
                        let info="上传文件大小";
                        if(this.upload.imgmaxSize) info="不能超过"+this.upload.imgmaxSize+"kb，";
                        info+=('不能低于'+this.upload.imgminSize+'kb');                                     
                        this.$message({
                            type: 'error',
                            message:info
                        });
                        //清空上传文件框
                        e.target.vlaue="";
                        this.valide=false;
                        return;
					} 
                }
            }  
            // if(this.upload.filesize && (this.upload.oversizedo===false || (!this.upload.oversizedo))){
            //     for(let i=0;i<etf.length;i++){
            //         if(etf[i].size>(this.upload.filesize*1024)){                        
            //             this.$message({
            //                 type: 'error',
            //                 message:'上传文件大小不能超过'+(this.upload.filesize || 10240)+'kb！'
            //             });
            //             //清空上传文件框
            //             e.target.vlaue="";
            //             this.valide=false;
            //             return;
            //         }
            //     }
            // }  
            this.valide=true;          
			let imgwhL=0,imgL=true;	
			if(this.upload.imgmaxWidth || this.upload.imgmaxHeight || this.upload.imgminWidth || this.upload.imgminHeight || this.upload.imgWidth || this.upload.imgHeight){
				this.valide=false;
				for (let fi = 0; fi < etf.length; fi++) {
					let filei = etf[fi];
                    if (/\/(?:jpeg|png)/i.test(filei.type)) {//如果是图片 就判断                    
                        let that=this,uploadT=this.upload;
                        EXIF.getData(filei,function(){
                            // EXIF.getAllTags(this);
                            let Orientation = EXIF.getTag(this, 'Orientation');                        
                            let fr = new FileReader();
						    fr.onload = function (e) {
						    	let imgTit=["上传图片"];
						    	let IMG = new Image();
						    	IMG.src = this.result;//读出来的文件流
						    	IMG.onload = function () {
                                    let w = this.naturalWidth, h = this.naturalHeight, resizeW = this.naturalWidth, resizeH = this.naturalHeight;                                    
								    imgwhL++;
                                    //将文件流画到canvas画布上
			    				    // let canvas = document.createElement('canvas'),
			    					// ctx = canvas.getContext('2d');                                    
                                    switch(Orientation){
                                        case 1://0°
                                            break;
                                        case 3://180°
                                            // ctx.rorate(180 * Math.PI / 180);
                                            break;
                                        case 6://顺时针90°
                                            resizeW=h;
                                            resizeH=w;
                                            // ctx.rorate(90 * Math.PI / 180);
                                            break;
                                        case 8://逆时针90°
                                            resizeW=h;
                                            resizeH=w;
                                            // ctx.rorate(-90 * Math.PI / 180);
                                            break;
                                    }
                                    // canvas.width = resizeW;
                                    // canvas.height = resizeH;
                                    // ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                    if(uploadT.imgmaxWidth && resizeW>uploadT.imgmaxWidth){
                                        that.valide=false;	
                                        imgL=false;
                                        imgTit.push("宽度不能超过"+uploadT.imgmaxWidth+"px");
                                    } 
                                    if(uploadT.imgmaxHeight && resizeH>uploadT.imgmaxHeight){
                                        that.valide=false;	
                                        imgL=false;
                                        imgTit.push("高度不能超过"+uploadT.imgmaxHeight+"px");
                                    }	
                                    if(uploadT.imgminWidth && resizeW<uploadT.imgminWidth){
                                        that.valide=false;	
                                        imgL=false;
                                        imgTit.push("宽度不能小于"+uploadT.imgminWidth+"px");
                                    } 
                                    if(uploadT.imgminHeight && resizeH<uploadT.imgminHeight){
                                        that.valide=false;	
                                        imgL=false;
                                        imgTit.push("高度不能小于"+uploadT.imgminHeight+"px");
                                    }	
                                    if(uploadT.imgWidth && resizeW==uploadT.imgWidth){
                                        that.valide=false;	
                                        imgL=false;
                                        imgTit.push("宽度为"+uploadT.imgWidth+"px");
                                    } 
                                    if(uploadT.imgHeight && resizeH==uploadT.imgHeight){
                                        that.valide=false;	
                                        imgL=false;
                                        imgTit.push("高度为"+uploadT.imgHeight+"px");
                                    }	
                                    if(imgL && imgwhL==etf.length){
                                        that.valide=true;
                                        that.chooseDoUpload(etf);
                                    }else{
                                        if(imgwhL==etf.length){
                                            that.$message({
                                                type: 'error',
                                                message:imgTit.join("")
                                            });
                                            et.vlaue="";
                                            that.valide=false;
                                        } 										
                                    }							
                                };//end imgload
						    };//end frload
						    fr.readAsDataURL(filei);
                        });
					}
				}
			}else{
				this.valide=true;
			}
            if(this.valide) this.chooseDoUpload(etf);     
        },
        chooseDoUpload(files){ 
            if(this.valide){
                let postFiles = Array.prototype.slice.call(files);
                if (!this.upload.multiple) { postFiles = postFiles.slice(0, 1); }
                if (postFiles.length === 0) { return; }
                postFiles.forEach(rawFile => {   
                    const { uid } = rawFile;
                    const options = {
                      file: rawFile,
                      onProgress: e => {
                        //this.onProgress(e, rawFile);
                      },
                      onSuccess: res => {
                        //this.onSuccess(res, rawFile);
                        this.handleSuccess(res, rawFile);
                        delete this.reqs[uid];
                      },
                      onError: err => {
                        //this.onError(err, rawFile);
                        this.handleError(err, rawFile);
                        delete this.reqs[uid];
                      }
                    };
                    const req = this.uploadfiles(rawFile,options);
                    this.reqs[uid] = req;
                    if (req && req.then) {
                      req.then(options.onSuccess, options.onError).catch(error => { });
                    }
                }); 
            }
        },
        handleError(err, file){
            if(err){
                this.$message({
                    message:err || '上传失败',
                    type:'error',
                    duration: 3000
                })
            }
        },
        handleValueType(){
            return {valueType:this.upload.listType && this.upload.listType.indexOf('picture')>-1 && (this.upload.valueType && this.upload.valueType==='string')}
        },
        handleSuccess(response, file){
            this.$refs.sbUpload.value="";
            let res=response;
            //if(res.errcode===0 || res.errcode===200){
                //if(res.message){
                //    this.$message({
                //        message:res.message || "上传成功！",
                //        type:'success',
                //        duration: 3000
                //    })
                //}
                let picType=this.handleValueType().valueType;
                if(this.upload.multiple || this.upload.limit>1){
                    for(let i in res.data.sysFiles){
                        if(picType){
                            this.fileList.push(res.data.sysFiles[i].anonymousFilePath);
                        }else{
                            this.fileList.push(res.data.sysFiles[i]);
                        }
                    }
                }else{
                    if(picType){
                        this.fileList=[res.data.sysFiles[0].anonymousFilePath];
                    }else{
                        this.fileList=[res.data.sysFiles[0]];
                    }
                }
                //if(this.upload.uploadData) 
                this.$emit("uploadData", picType?this.fileList.join(","):this.fileList);
                if(this.onOk && this.upload.afterUpload){
                    this.onOk(this.upload,"afterUpload",res.data);
                }
                //if(this.upload.afterUpload) this.$emit(this.upload.afterUpload,{list:files,fileData:file.response});
            //}else{
                //this.$message({
                //    message:res.message || '上传失败',
                //    type:'error',
                //    duration: 3000
                //});
                //if(res.errcode===401 || res.errcode===403){
                //    //this.$confirm('你已被登出,可以取消继续留在该页面,或者重新登录','确认登出',{
                //    //    confirmButtonText:'重新登录',
                //    //    cancelButtonText:'取消',
                //    //    type:'warning'
                //    //}).then(() => {
                //        store.dispatch('FedLogOut').then(() => {
                //            location.reload();
                //        });
                //   // })
                //}
            //}
        },
        handleRemove(file,i){
            //移除后要干什么
            if(this.upload.multiple || this.upload.limit>1){
                let index=this.fileList.findIndex(fi => fi===file);
                if(index>-1) this.fileList.splice(index,1);
                this.$emit("uploadData", this.fileList);
            }else{
                this.fileList=[];
                this.$emit("uploadData", []);
            }
            if(this.onOk && this.upload.removeFun){
                this.onOk(this.upload,"removeFun");
			}
        },
        beforeUpload(file){
            if(this.onOk && this.upload.beforeFun){
                let n=this.onOk(this.upload,"beforeFun");
                return n;
            }
            return true;
        },
        handleExceed(){
            if(this.fileList.length>(this.upload.limit ||20)){// || (this.upload.limit && this.fileList.length>this.upload.limit)
                this.$message({
                    message:'最多允许上传'+(this.upload.limit || 20)+'个文件！'
                });
                //清空上传文件框
                return false;
            }
            return true;
        },
        uploadfiles(file,content){         
            let self=this;
            if(this.beforeUpload(content.file)){
                let formData=new FormData;
                if(this.upload.data){
                    for(let i in this.upload.data){                        
                        formData.append(i,this.upload.data[i]);
                    }
                }
                if (this.upload.listType && (this.upload.listType==='picture' || this.upload.listType==='picture-card')) {//是否要压缩图片
                    if (/\/(?:jpeg|png)/i.test(content.file.type)) {//如果是图片 就压缩
			    		let fr = new FileReader();
			    		fr.onload = function (e) {
                            let IMG = new Image();
                            IMG.src = e.target.result;//读出来的文件流
			    			IMG.onload = function () {
			    				let w = this.naturalWidth, h = this.naturalHeight, resizeW = this.naturalWidth, resizeH = this.naturalHeight;
                                // maxSize 是压缩的设置，设置图片的最大宽度和最大高度，等比缩放，level是报错的质量，数值越小质量越低
                                let fs = ((self.upload.filesize*1024) / content.file.size);
			    				let maxSize = {
			    					width: self.upload.maxWidth || w,
			    					height: self.upload.maxHeight || h,
			    					level: fs || self.upload.maxLevel || 0.8
                                };
			    				if (w > maxSize.width || h > maxSize.height) {//设置压缩比例
			    					if (h / w > maxSize.height / maxSize.width) {
			    						resizeH = maxSize.height;
			    						resizeW = (maxSize.height / h) * w;
			    					} else {
			    						resizeW = maxSize.width;
			    						resizeH = (maxSize.width / w) * h;
			    					}
			    				}
    
			    				//将文件流画到canvas画布上
			    				let canvas = document.createElement('canvas');
                                let ctx = canvas.getContext('2d');   
                                canvas.width = resizeW;
                                canvas.height = resizeH;                            
                                // ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                let Orientation=content.file.exifdata.Orientation;      
                                switch(Orientation){
                                    case 1://0°
                                        canvas.width = resizeW;
                                        canvas.height = resizeH;
                                        ctx.drawImage(IMG, 0, 0, resizeW, resizeH);
                                        break;
                                    case 3://180°
                                        canvas.width = resizeW;
                                        canvas.height = resizeH;
                                        ctx.rotate(180 * Math.PI / 180);
			    				        ctx.drawImage(IMG, -resizeW, -resizeH, resizeW, resizeH);
                                        break;
                                    case 6://顺时针90°
                                        resizeW=h;
                                        resizeH=w;
                                        canvas.width = resizeW;
                                        canvas.height = resizeH;
                                        ctx.rotate(90 * Math.PI / 180);
			    				        ctx.drawImage(IMG, 0, -resizeW, resizeH, resizeW);
                                        break;
                                    case 8://逆时针90°
                                        resizeW=h;
                                        resizeH=w;
                                        canvas.width = resizeW;
                                        canvas.height = resizeH;
                                        ctx.rotate(-90 * Math.PI / 180);
			    				        ctx.drawImage(IMG, -resizeH, 0, resizeH, resizeW);
                                        break;
                                }
			    				//再将画布上元素导出到Base64
			    				let base64img = canvas.toDataURL('image/jpeg', maxSize.level);
			    				let base64 = window.atob(base64img.split(',')[1]);
			    				let buffer = new ArrayBuffer(base64.length);
			    				let ubuffer = new Uint8Array(buffer);
			    				for (let i = 0; i < base64.length; i++) {
			    					ubuffer[i] = base64.charCodeAt(i)
			    				}
			    				//再把Base64转成blob文件（图片）
			    				let blob;
			    				try {
			    					blob = new Blob([buffer], { type: 'image/jpg' });
			    				} catch (e) {
			    					window.BlobBuilder = window.BlobBuilder || window.WebKitBlobBuilder || window.MozBlobBuilder || window.MSBlobBuilder;
			    					if (e.name === 'TypeError' && window.BlobBuilder) {
			    						let blobBuilder = new BlobBuilder();
			    						blobBuilder.append(buffer);
			    						blob = blobBuilder.getBlob('image/jpg');
			    					}
                                 }
                                 formData.append((self.upload.name || 'file'), blob, content.file.name);    
                                 self.doUpload(formData,content);
			    			};//end imgload
                        };//end frload                        
                        fr.readAsDataURL(content.file);
			    	}
			    }else{                    
                    formData.append('file',content.file);
                    self.doUpload(formData,content);
                }                
            }
        },
        doUpload(formData,content){
            if(this.upload.fun){
                this.$emit((this.from?"handleHttpRequest":"uploadHttpRequest"),{fun:this.upload.fun,formData,content});
            }else{               
                uploadProcessFiles(formData,this.upload.data).then(res => {
                    content.onSuccess(res);
                }).catch(error => {   
                    console.log(error);
                    content.onError("文件上传失败！");
                });
            }
        }
    },
    watch:{
        'upload.filelist':{
            handler:function(newV,oldV){
                if(newV){
                    //console.log("值更新啦！",newV);
                    this.fileList=newV;
                }
            },
            deep:true,
            immediate: true
        }
    }
}
</script>
<style scoped>
.upload_D{min-height:32px;min-width:170px;}
.upload_Btn{position:relative;z-index:999;min-width:100px;height:32px;}
.upload_Btn input{position:absolute;left:0;z-index:1000;opacity:0;height:32px;cursor:pointer;}
.upload_Btn .uploadB{position:absolute;left:0;z-index:999;}
.el-upload-list--picture-card .el-upload-list__item{margin:8px 8px 8px 0;}
</style>
