<template>
  <div style="width: 100%;padding-bottom:5px;">
    <sb-el-form-item
        v-for="item in formItemList"
        :key="item.key"
        :lw="'100px'"
        :item="item"
        :value="formVal[item.key]"
        :keyVal="formVal[item.key]"
        :formVal="formVal"
        :gps="gps"
        :currentPageType="currentPageType||'formPage'"
        @input="handleInput($event, item)"
        @uploadUpdate="uploadUpdate($event, item)"
        @inputBtn="inputBtn"
        :on-ok="handleDoFun"
    ></sb-el-form-item>

    <!-- 回显表格 -->
    <div class="echoForm">
      <el-form v-if="tableEcho.queryForm.formItemList.length>0" :inline="true" :model="tableEcho.listQuery" label-width="100px" style="display:flex;margin-bottom: 15px;">
        <el-form-item v-for="item in tableEcho.queryForm.formItemList" :key="item.key" :label="item.label" style="width: 18%">
          <el-input v-model="tableEcho.listQuery[item.prop]" :placeholder="item.label" size="small"></el-input>
        </el-form-item>
        <el-form-item style="width: 8%;position: absolute;right: 1%;">
          <el-button type="primary" size="small" class="echo-btn" @click="getListQuery">查询</el-button>
          <el-button type="default" size="small" class="echo-btn" @click="resetListQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <sb-el-table :table="tableEcho" ref="echoTable" v-bind="$attrs" v-on="$listeners" @getList="getListQuery" @handleDelete="handleDelete"></sb-el-table>
    </div>
  </div>
</template>
<script>
import util from '@/assets/js/public'
import SbElFormItem from '../SbElForm/components/SbElFormItem'
export default {
  name: 'SbEchoTable',
  components: { SbElFormItem },
  props: {
    item: {
      type: Object,
      required: true,
    },
    formVal: {},
    gps: {},
    currentPageType: {
      type: String,
    }
  },
  data() {
    return {
      formItemList: [],
      tableEcho: {
        modulName: "tableEcho-回显表格", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            
          ],
        },
        tr: [],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      tableEchoData: [],
      isPageEcho: false, //
    }
  },
  methods: {
    handleInput(val, item) {
      this.$emit('chooseCardData', val, item)
    },
    uploadUpdate(val, item) {
      this.tableEchoData = [];
      // console.log(val, item);
      this.formItemList.forEach(ele => {
        if(ele.type == 'echoBtn') {
          // 映射关系
          if (ele.dialogData.keyShowArr&&ele.dialogData.keyShowArr.length > 0) {
            val.list.forEach(item1 => {
              let row = {}
              ele.dialogData.keyShowArr.forEach(item => {
                if(item1[item.name]) {
                  row[item.value] = item1[item.name];
                }
              })
              this.tableEchoData.push(row);
            })
          } else {
            this.tableEchoData = val.list;
          }
        }
      })
      this.getListQuery();
      this.$emit('chooseCardData', this.tableEchoData, item)
    },
    inputBtn(data) {
      if (this.from) {
        this.$emit('handleInputBtn', data)
      } else {
        this.$emit(data.fun, data)
      }
    },
    handleDoFun(obj, fun, data) {
      if (this.onOk) {
        if (obj[fun]) {
          let n = this.onOk(obj, fun, data)
          if (fun === 'beforeFun') return n
        }
      } else {
        if (obj[fun]) this.$emit(obj[fun], { obj, data })
      }
      if (fun === 'beforeFun') return true
    },
    getChangeData(type, arr) {
      // type showData是否显示  changeData是否编辑
      let arrs = arr
      if (this.gps.location == process.env.VUE_APP_APPCODE + '.') {
        return true
      } else {
        let location = this.gps.location
          ? this.gps.location
          : process.env.VUE_APP_APPCODE + '.start'
        const apiInfo = arrs.filter((item) => item.activityDefId == location)
        if (apiInfo.length > 0) {
          return !apiInfo[0][type]
        } else {
          return true
        }
      }
    },
    resetListQuery() {
      this.tableEcho.listQuery = {
        page: 1,
        size: 10
      }
      this.getListQuery();
    },
    // 查询
    getListQuery() {
      let listQ = this.tableEcho.listQuery;
      let dataQ = [];
      // isPageEcho  分页
      if(Object.keys(listQ).length > 2) {
        for(let i in listQ) {
          if(i != 'page' && i != 'size') {
            this.tableEchoData.forEach(item => {
              if(item[i] == listQ[i]) {
                dataQ.push(item);
              }
            })
          }
        }
      } else {
        dataQ = this.tableEchoData;
      }
      if(this.isPageEcho) {
        let index = (this.tableEcho.listQuery.page-1)*this.tableEcho.listQuery.size;
        this.tableEcho.data = dataQ.slice(index,index+this.tableEcho.listQuery.size);
        this.tableEcho.total = dataQ.length;
      } else {
        this.tableEcho.data = dataQ;
        this.tableEcho.total = null;
      }
    },
    // 删除
    handleDelete(row, index) {
      let i = ((this.tableEcho.listQuery.page-1)*this.tableEcho.listQuery.size)+index;
      this.tableEchoData.splice(i,1);
      this.getListQuery();
    }
  },
  created() {
    var formList = this.item.list
    formList.forEach((item) => {
      let formInfo = {
        class: 'c6',
        label: item.item.label,
        key: item.name,
        type: item.type,
        disabled: item.control.readonly || false,
        placeholder: item.control.placeholder,
        inline: this.item.inline,
        show:true
      }
      // formInfo.show = item.control[this.gps.processDefKey] ? this.getChangeData('showData', item.control[this.gps.processDefKey]) : true
      // if (this.gps.location || (this.gps.action && this.gps.action == 'read')) {
      //   if ((!this.gps.modify && (this.gps.type == 'task' && this.gps.location !== process.env.VUE_APP_APPCODE + '.start'))) {
      //     formInfo.disabled = item.control[this.gps.processDefKey] ? !this.getChangeData('changeData', item.control[this.gps.processDefKey]) : item.control.readonly
      //   } else if (this.gps.type == 'draft' || (this.gps.type == 'task' && this.gps.location == process.env.VUE_APP_APPCODE + '.start')) {
      //     formInfo.disabled = item.control.readonly
      //   } else {
      //     formInfo.disabled = true
      //   }
      // }
      formInfo = Object.assign(formInfo, this.util.changeListInfo(formInfo,item))
      this.formItemList.push(formInfo)
    })

    this.formItemList.forEach(ele => {
      if(ele.type == 'echoBtn') {
        this.isPageEcho = ele.dialogData.isPageEcho;
        // 列表
        if (ele.dialogData.optionsEcho.length > 0) {
          const options = ele.dialogData.optionsEcho.map(element => {
            return {
              id: element.value,
              label: element.name,
              prop: element.value,
            };
          });
          this.tableEcho.tr = options
        } else {
          this.tableEcho.tr = []
        }
        // 查询条件
        if (ele.dialogData.paramsArrEcho&&ele.dialogData.paramsArrEcho.length > 0) {
          const formItemList = ele.dialogData.paramsArrEcho.map(element => {
            return {
              type: "input",
              label: element.name,
              prop: element.value,
              key: element.value
            };
          });
          this.tableEcho.queryForm.formItemList = formItemList
        } else {
          this.tableEcho.queryForm.formItemList = []
        }
        // 操作列
        if (ele.dialogData.operaEcho&&ele.dialogData.operaEcho.length > 0) {
          const operate = ele.dialogData.operaEcho.map(element => {
            return {
              id: element.id,
              name: element.name,
              fun: element.id=='delete'?'handleDelete':''
            };
          });
          this.tableEcho.operation.data = operate
        } else {
          this.tableEcho.operation.data = []
        }
      }
    })

  },
}
</script>
<style scoped>
.echoForm {
  padding-top: 20px;
}
.echoForm ::v-deep .el-form-item .el-form-item__label{
  font-weight: 500;
  background: transparent;
}
.echoForm ::v-deep .el-input__inner {
    border: 1px solid #DCDFE6;
}
.echo-btn {
  height: 28px;
  margin: 2px 5px;
}
.echoForm ::v-deep .inlineB {
  border: none;
}
</style>
