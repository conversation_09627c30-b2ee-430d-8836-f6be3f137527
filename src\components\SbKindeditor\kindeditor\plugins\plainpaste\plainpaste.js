/*******************************************************************************
 * KindEditor - WYSIWYG HTML Editor for Internet
 * Copyright (C) 2006-2011 kindsoft.net
 *
 * <AUTHOR> <<EMAIL>>
 * @site http://www.kindsoft.net/
 * @licence http://www.kindsoft.net/license.php
 *******************************************************************************/

KindEditor.plugin("plainpaste", function(K) {
  var self = this,
    name = "plainpaste";
  self.clickToolbar(name, function() {
    var lang = self.lang(name + "."),
      html =
        '<div style="padding:10px 20px;">' +
        '<div style="margin-bottom:10px;">' +
        lang.comment +
        "</div>" +
        '<textarea class="ke-textarea" style="width:408px;height:260px;"></textarea>' +
        "</div>",
      dialog = self.createDialog({
        name: name,
        width: 450,
        title: self.lang(name),
        body: html,
        yesBtn: {
          name: self.lang("yes"),
          click: function(e) {
            var html = textarea.val();
            html = K.escape(html);
            html = html.replace(/ {2}/g, " &nbsp;");
            if (self.newlineTag == "p") {
              html = html
                .replace(/^/, "<p>")
                .replace(/$/, "</p>")
                .replace(/\n/g, "</p><p>");
            } else {
              html = html.replace(/\n/g, "<br />$&");
            }
            self
              .insertHtml(html)
              .hideDialog()
              .focus();
          }
        }
      }),
      textarea = K("textarea", dialog.div);
    textarea[0].focus();
  });
});
