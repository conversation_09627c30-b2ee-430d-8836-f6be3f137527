<template>
	<div class="app-container" style="display:flex;justify-content: space-between;">
		<div class="container-left">
			<el-card shadow="never" style="overflow: auto; width: 100%;height: 100%;">
				<template #header>
				<div class="card-header">
					<!-- <el-input v-model="dbName" placeholder="请输入数据源名称" size="small" style="width:70%" clearable /> -->
                    <!-- <span style="color:red">修改组织信息后需手动点击当前父级组织更新数据</span> -->
				</div>
				</template>
				<div class="head-container">
					<el-tree class="tree1" :props="defaultProps" ref="chooseOrgTree" :default-expanded-keys="treeExpandData" :load="loadNodeOrg" lazy :node-key="'id'" :highlight-current="true" :check-on-click-node="true">
						<!-- :expand-on-click-node="false" -->
						<template #default="{ node, data }">
							<div @click="handleNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
						</template>
					</el-tree>
				</div>
			</el-card>
		</div>
		<div class="container-right">
			<sb-el-table :table="table" @getList="getList" @handleAddData="handleAddData" @handleDelete="handleDelete" 
						@handleUpDataGetRow="handleUpDataGetRow" @handleAddBef="handleAddBef" @handleReadData="handleReadData">
                <template v-slot:enabled="{ obj }">
                    <div>{{ obj.row.enabled==true?'是':'否' }}</div>
                </template>
			</sb-el-table>
		</div>
        <el-dialog title="查看" v-if="zwDialogFlag" :visible.sync="ZWAllocationDialog" width="900px" center :destroy-on-close="true" @close="ZWAllocationClose" :close-on-click-modal="false">
			<div style="display:flex;flex-direction: column;height:450px">
				<!-- <sb-el-table :table="ZWAllocatTable" @getList="getRoleList">
					<template v-slot:isApplicationRole="{ obj }">
						<div>{{ obj.row.isApplicationRole==true?'是':'否' }}</div>
					</template>
				</sb-el-table> -->
				<el-form :inline="true" :model="ZWAllocatTable.listQuery" class="demo-form-inline" style="display:flex">
					<el-form-item label="中文姓名">
						<el-input v-model="ZWAllocatTable.listQuery.truename" placeholder="请输入角色编码" size="small" clearable></el-input>
					</el-form-item>
					<el-form-item>
						<el-button type="primary" @click="getZwQuery" size="small" style="margin-top:5px">查询</el-button>
					</el-form-item>
				</el-form>
				 <el-table
					ref="multipleTable"
					v-loading="ZWAllocatTable.loading"
					:data="ZWAllocatTable.data"
					tooltip-effect="dark"
					style="width: 100%"
					border
					height="400"
					>
                    <!-- <el-table-column
                        type="index"
                        width="50">
                    </el-table-column> -->
					<el-table-column
						prop="truename"
						label="姓名">
					</el-table-column>
					<el-table-column
						prop="username"
						label="OA账号">
					</el-table-column>
					<el-table-column
						prop="positionName"
						label="职务">
						<!-- <template slot-scope="scope">{{ scope.row.isApplicationRole==true?'是':'否' }}</template> -->
					</el-table-column>
				</el-table>
				<el-pagination
					@size-change="handleSizeChange"
					@current-change="handleCurrentChange"
					:current-page="ZWAllocatTable.page"
					:page-sizes="[10, 20, 30, 40]"
					:page-size="ZWAllocatTable.size"
					layout="total, sizes, prev, pager, next, jumper"
					:total="ZWAllocatTable.total"
					background
					style="text-align: right;">
				</el-pagination>
			</div>
		</el-dialog>
	</div>
</template>
<script>
// import { findAll, deleteById, changeStatus, getTableField, getSyncField, batchUpdateField, create, update, findAllDb, saveGroupDataAdd, saveGroupDataUpdate, saveGroupData, delectGroupData, queryGroupList, addGroupJY, deleteGroupById } from '@/api/dataCollect/dataSet';
// import { ListByDataSetId } from '@/api/dataCollect/datasetForm';
import { getZwList,findDictValue,addZw,deleteZwCustom,findPositionAndUser } from '@/api/system/zwgl.js';
import ConfigDialog from "../component/configDialog";
import util from "@/assets/js/public";

export default {
	name: 'dataSet',
	props: ["tableType", "tableTitle"],
	components: {
		ConfigDialog
	},
	data() {
		return {
			resetPasswordDialog: false,
			fieldTableDialog: false, //字段管理弹框
			fieldTableData: [], //字段列表表数据
			quotaChecked: false,
			quotaIndeterminate: false,
			datasetId: '', //数据集id
			table: {
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'yhgl-用户信息', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: true, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					inline: true,
					labelWidth: '80px',
					labelPosition: 'right',
					formItemList: [
						{ label: '职务ID', key: 'id', type: 'input', clearable: true },
						{ label: '职务名称', key: 'positionName', type: 'input', clearable: true },
						{ label: '职务类型', key: 'positionCompany', type: 'select',dictType: "positionType" , from:true, clearable: true },
					],
				},
				tr: [
					{ id: 'id', label: '职务ID', prop: 'id', width:'100' },
					{ id: 'smapPosition', label: '职务级别', prop: 'smapPosition',  },
					{ id: 'positionName', label: '职务名称', prop: 'positionName',  },
					{ id: 'positionCompany', label: '职务类型', prop: 'positionCompany',  },
					{ id: 'positionCompanyCn', label: '公司类型', prop: 'positionCompanyCn',   },
				],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				positionType: [], //
				positionLevel: [], //
				nation: [],
				userType: [],
				staffType: [],
				form: {
					width: '600px',
					labelWidth: '120px',
					inline: false,
					labelPosition: 'right',
					formItemList: [
						{ class: 'c6', label: '职务名称', key: 'positionName', type: 'input', rule: { required: true }, clearable: true},
						{ class: 'c6', label: '所属公司类型', key: 'positionCompany', type: 'select', dictType: "positionType" , from:true, clearable: true},
						{ class: 'c6', label: '职务级别', key: 'positionType', type: 'select', dictType: "positionLevel" , from:true, clearable: true},
					],
				},
				listFormModul: {},
				hasOperation: true, //是否有操作列表
				operation: {
					width: '250',
					data: [
						{ id: 'add', name: '新增',beforeFun: "handleAddBef", fun: 'handleAddData' },
                        // { id: 'read', name: '查看', fun: 'handleUpDataGetRow' },
						{ id: 'update', name: '编辑', fun: 'handleUpData', beforeFun: "handleUpDataGetRow" },
						{ id: 'delete', name: '删除', fun: 'handleDelete'},
						{ id: 'czmm', name: '查看该职务下所有人', fun: 'handleReadData'},
					],
				},
				hasPagination: true,
				listQuery: { size: 10, page: 1, },
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: true,
				otherQueryBtn: {
					data: [
                        {
                            id: 'export',
                            name: '导出 ',
                            fun: 'exportSelect',
                        },
                    ],
				},
				hasGroupTabs: true,
				tabsList: [],
				tabsPosition: '全部',
			},
			dbName:"",
			dbArr: [],
			idS: [], //当前选中分组ids
			selectIndex:null,  //搜索结果,被选中的li index
            data: [],
			treeExpandData: [],
            defaultProps: {
                children: 'children',
                label: 'label'
            },
            ZWAllocationDialog: false,
            zwDialogFlag: true,
			ZWAllocatTable: {
				border: true, // 是否带纵向边框
				loading: false, // 加载中动画
				modulName: 'role-角色配置', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				showIndex: true, // 序号
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					truename: ''
				},
				initSelection: [],
				multipleSelection: [], //多选选中数据存放变量
				form: {
					width: '800px',
					labelWidth: '100px',
					inline: true,
					labelPosition: 'right',
					formItemList: [
						
					],
				},
				listFormModul: {},
				hasOperation: false, //是否有操作列表
				operation: {
					width: '250',
					data: [],
				},
				hasPagination: true,
				listQuery: { size: 10, page: 1 , truename: ''},
				hasBatchOperate: false, //有无批量操作
				batchOperate: {},
				hasOtherQueryBtn: false,
				otherQueryBtn: {
					data: [],
				},
				total: 0
			},
		};
	},
	activated() {
		
	},
	created(){
		this.getList();
	},
	methods: {
        //查询职务下所有人
        getZwQuery(){
            findPositionAndUser(this.ZWAllocatTable.listQuery).then(({ data }) => {
                this.ZWAllocatTable.data = data.content;
                this.ZWAllocatTable.total = data.totalElements;
            })
        },
        ZWAllocationClose() {
            this.zwDialogFlag = false;
            this.ZWAllocatTable.listQuery = { size: 10, page: 1 , truename: ''};
            this.ZWAllocatTable.data = []
        },
        handleSizeChange(val) {
			this.ZWAllocatTable.listQuery.size = val
			this.getZwQuery()
		},
		handleCurrentChange(val) {
			this.ZWAllocatTable.listQuery.page = val
			this.getZwQuery()
		},
        //左侧树点击
		handleNodeClick(node,data) {
			var params = Object.assign(this.table.listQuery,{positionCompany: node.data.value,enabled: true}) 
			this.getList(params)
		},
		loadNodeOrg(node, resolve) {
			if (node.level === 0) {
                let params = {"dictType":"positionType"}
				findDictValue(params).then(({ data }) => {
                    resolve(data)
				})
			} else {
				resolve([])
			}
		},
        // 点击组织树节点
		// 查询列表
		getList(params) {
			this.table.loading = true;
            this.table.listQuery.enabled=true
			getZwList(params || this.table.listQuery).then((res) => {
				this.table.loading = false;
				this.table.data = res.data&&res.data.content?res.data.content:[];
				this.table.total = res.data.totalElements;
			}).catch((err) => {
				this.table.loading = false;
			});
		},
		handleAddBef(){
			this.table.operation.nowBtn = true;
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = false;
			})
			return true
		},
		// 新增
		handleAddData() {
			this.table.positionType.forEach((item)=>{
                if(item.value == this.table.listFormModul.positionCompany) {
                    this.table.listFormModul.positionCompanyCn = item.name
                }
            })
            // this.table.positionLevel.forEach((item1)=>{
            //     if(item1.value == this.table.listFormModul.smapPosition) {
            //         this.table.listFormModul.smapPosition = item1.name
            //     }
            // })
			addZw(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false
				if (res.status == 200){
					this.getList();
				} 
			})
		},

		// 根据id查询行数据
		handleUpDataGetRow(row) {
			this.table.form.formItemList.forEach((item)=>{
				item.disabled = row.read;
			})
			this.table.listFormModul = row
			this.table.listFormModul.genderDictValue = this.table.listFormModul.genderDictValue+'';
			this.table.listFormModul.employeeTypeDictValue = this.table.listFormModul.employeeTypeDictValue+'';
		},
		// // 编辑
		// handleUpData() {
		// 	updateUserCustom(this.table.listFormModul).then(res => {
		// 		this.table.dialogVisible = false
		// 		if (res.status == 200) this.getList();
		// 	})
		// },
		// 删除
		handleDelete(row) {
			deleteZwCustom(row.id).then((res) => {
				this.getList();
			});
		},
		handleReadData(obj) {
            this.zwDialogFlag = true;
			this.ZWAllocationDialog = true;
            this.ZWAllocatTable.listQuery.positionId = obj.row.id;
            this.getZwQuery()
		},
	},
};
</script>
<style scoped>
.table-name {
    font-weight: bold;
}
.container-left {
    width: 300px;
    height: 100%;
    overflow: auto;
    background-color: var(--el-bg-color-overlay);
    border: 1px solid var(--el-border-color-light);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);
}
.container-right {
    width: calc(100% - 300px);
    height: 100%;
	position: relative;
}
::-webkit-scrollbar {
    display: none;
}
::v-deep .el-card__header{
	padding: 8px 15px;
}
.treeData{
	width:100%;
	display:flex;
	justify-content: space-between;
	font-size: 15px;
	height: 30px;
	line-height: 30px;
	color: #444;
	padding-left: 8px;
}
.treeData .text{
	width: 215px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	/* padding: 5px; */
	cursor: pointer;
}
.treeData .text:hover{
	color: #0F85CF;
}
.treeData .execute{
	width: 70px;
	color: #0F85CF;
	cursor: pointer;
	text-align: right;
}
.treeData .execute:hover{
	text-decoration: underline;
}
::v-deep .el-card__body{
	padding: 0 15px;
	padding-bottom: 15px;
}
.head-container{
	height: calc(100vh - 180px);
	overflow-y: auto;
    padding-top: 20px;
}
::v-deep .el-table__body-wrapper{
	max-height: calc(100vh - 320px);
	overflow-y: auto;
}
.acceptanceBox{
    position: relative;
	/* height: 250px; */
}
.footer{
    width: 100%;
    /* position: absolute; */
    left: 0;
    bottom: 0;
    text-align: center;
    margin-top: 20px;
	margin-bottom: 20px;
}
.selectedColor {
	background-color: #eeeeee;
}
.rateRuleContent {
	margin-right: 22px;
}
</style>
