import request from "@/assets/js/request";
import store from "@/store";
import util from "@/assets/js/public";


// 管理员管理
export function queryAdmin(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/admin/queryAdmin?source=PC&page=${params.page}&size=${params.size}`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}

export function addAdmins(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/admin/addAdmin?source=PC`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}
export function updateAdmin(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/admin/updateAdmin?source=PC`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}
export function delAdmin(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/admin/delAdmin?source=PC&id=${params.id}`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}
export function usDeptAdmin_exportExcel(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/admin/exportQuery?source=PC`,
      contentType: "application/json; charset=utf-8",
      data:params,
      responseType: "blob"
  });
}

// 首页
// 我的待办
export function myTaskTodo(params){
  let data = params?params:{};
  data.databaseType = "oracle";
  data.participant = "OA";
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsWorkitemModel/myTaskTodo?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
      contentType: "application/json;charset=UTF-8",
      data: data
  });
}

// 我的草稿
export function myDraft(params){
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/commitment/myDraft?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
      contentType: "application/json;charset=UTF-8",
      data: params
  });
}

// 我的已办
export function queryMyJoin(params){
  let data = params?params:{};
  data.databaseType = "oracle";
  data.participant = "OA";
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsWorkitemModel/queryMyJoin?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
      contentType: "application/json;charset=UTF-8",
      data: data
  });
}


// 发起派发
export function distribute(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/commitment/distribute?source=PC`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}
// 发起下发
export function issued(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsApplicationForm/startProcess?source=PC&currentUserCode=${store.state.user.user.username}`,
      contentType: "application/json; charset=utf-8",
      data: params,
      loading: true
  });
}


// 保存草稿
export function saveDraft(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/commitment/saveDraft?source=PC`,
      contentType: "application/json; charset=utf-8",
      data: params
  });
}
// 详情接口
export function queryDetails(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsApplicationForm/getFormDetail?id=${params.id}`,
      contentType: "application/json; charset=utf-8",
  });
}

// 图表接口 
// 年度签订情况
export function annualStatistics(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsApplicationForm/annualStatistics?source=PC&year=${params.year}`,
      contentType: "application/json; charset=utf-8",
      // data: params
  });
}
// 月签订情况
export function monthlyStatistics(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsApplicationForm/monthlyStatistics?source=PC&year=${params.year}&month=${params.month}`,
      contentType: "application/json; charset=utf-8",
      // data: params
  });
}

// 模板下载
export function downloadTemplete(id) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsApplicationForm/downloadTemplateUser?source=PC&pmInsId=${id}`,
      contentType: "application/json; charset=utf-8",
      responseType: "blob"
  });
}
// 二维码接口
export function generateQRCode(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsApplicationForm/generateQRCode?id=${params.id}&isAgainSign=${params.isAgainSign}`,
      contentType: "application/json; charset=utf-8",
  });
}
// 查询
export function getWorkQueryPage(params){
  let data = params?params:{};
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsPersonInfo/getWorkQueryPage?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
      contentType: "application/json;charset=UTF-8",
      data: data
  });
}
// 导出
export function exportParameter(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsPersonInfo/downLoadZip`,
      contentType: "application/json; charset=utf-8",
      data: params,
      responseType: "blob"
  });
}
// 派发出人
export function getDjLeader(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/UsApplicationForm/getDjLeader?location=${params.location}&type=${params.type}`,
      contentType: "application/json; charset=utf-8",
  });
}
// 判断报表是否展示
export function isAdminShow(params) {
  return request({
      url: `/${process.env.VUE_APP_APPCODE}/action/admin/isAdmin`,
      contentType: "application/json; charset=utf-8",
  });
}

