<template>
  <div class="app-container">
    <sb-el-table :table="table" @getList="getList" @handleTodo="handleTodo" @updateTableData="updateTableData" :on-ok="handleDoFun">
      <template v-slot:RECEIPTTILE="{obj}">
        <span class="toDetail" @click="handleTodo(obj)">{{obj.row.RECEIPTTILE}}</span>
      </template>
    </sb-el-table>

    <!-- 工单详情 -->
    <el-dialog :title="dialogTitle" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <work-order :key="cKey" :gps="gps" :dialogClose="dialogClose"></work-order>
    </el-dialog>
  </div>
</template>
<script>
import WorkOrder from "@/components/WorkOrder";
import { findProcessRead } from "@/api/process";
export default {
  name: "processtoRead",
  components: { WorkOrder },
  data() {
    return {
      viewD: false,
      dialogTitle: "",

      gps: {
        type: "toRead",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "processtoRead-待阅列表", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            { label: "工单标题", key: "title", type: "input" ,class: 'c4'},
            // {label: "流程类型",key: "pmInstType",type: "select",dictType: "processType"}
          ],
        },
        tr: [
          { id: "RECEIPTCODE", label: "流程编号", prop: "RECEIPTCODE", width: 180 },
          { id: "RECEIPTTILE", label: "工单标题", prop: "RECEIPTTILE",show:"template", template: "RECEIPTTILE" },
          { id: "CREATEORGNAME", label: "创建部门", prop: "CREATEORGNAME", width: 150 },
          { id: "CREATEUSERNAME", label: "创建人", prop: "CREATEUSERNAME", width: 90 },
          { id: "STARTTIME", label: "创建时间", prop: "STARTTIME", width: 160 },
          { id: "PREVIOUSASSISTANTNAME", label: "已办理人", prop: "PREVIOUSASSISTANTNAME", width: 90 },
          { id: "CONFIRMTIME", label: "办理时间", prop: "CONFIRMTIME", width: 160 },
          // {id: "aaa",label: "当前办理环节",prop: "aaa",width: 160}
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [
            { id: "handleTodo", name: "办理", fun: "handleTodo" },
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      findProcessRead(listQuery || this.table.listQuery).then((res) => {
        this.table.loading = false;
        this.table.data = res.data.content;
        this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },

    // 办理
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "toRead",
        // location: obj.row.ACTIVITYINSTID,
        location: obj.row.NEXTACTIVITYDEFID,
        pmInsType: obj.row.PMINSTTYPE,
        pmInsId: obj.row.RECEIPTCODE,
        taskId: obj.row.TASKID,
        processInstId: obj.row.PROCESSINSTID,
        processDefinitionId: obj.row.PROCESSDEFINTIONID,
        notificationId: obj.row.NOTIFICATIONID,
        nextlocation: obj.row.NEXTACTIVITYDEFID,
      };

      // 工单标题
      var th = this.util.appNameTH(obj.row.PMINSTTYPE);
      this.dialogTitle = th.type + (obj.row.RECEIPTTILE || "") + "-审批";

      this.cKey++;
      this.viewD = true;
    },

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header {
  text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;
}
::v-deep .el-dialog__title {
  color: black !important;
  font-size: 15.5px;
}
::v-deep .el-dialog__headerbtn .el-dialog__close {
  color: black;
}
::v-deep .el-form-item__content {
  width: 100% !important;
}
</style>