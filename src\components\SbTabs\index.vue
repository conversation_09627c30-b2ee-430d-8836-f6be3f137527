<template>
  <div style="width: 100%">
    <el-tabs>
      <el-tab-pane v-for="(info, tIndex) in itemArr.columns" :label="info.label" :key="tIndex">
        <sb-el-form-item v-for="item in info.list" :key="item.key" :lw="'160px'" :item="item" :value="formVal[item.key]" :keyVal="formVal[item.key]" :formVal="formVal" :gps="gps" @input="handleInput($event, item)" @uploadUpdate="uploadUpdate($event, item)" @inputBtn="inputBtn" :on-ok="handleDoFun"></sb-el-form-item>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import util from '@/assets/js/public'
import SbElFormItem from '../SbElForm/components/SbElFormItem'
export default {
  name: 'SbCard',
  components: { SbElFormItem },
  props: {
    item: {
      type: Object,
      required: true,
    },
    formVal: {},
    gps: {
      type: Object,
      default: function () {
        return {
          location: '',
          action: '',
          type: ''
        };
      }
    },
  },
  data() {
    return {
      itemArr: [],
    }
  },
  methods: {
    handleInput(val, item) {
      this.$emit('chooseCardData', val, item)
    },
    uploadUpdate(val, item) {
      this.$emit('chooseCardData', val.list, item)
    },
    inputBtn(data) {
      if (this.from) {
        this.$emit('handleInputBtn', data)
      } else {
        this.$emit(data.fun, data)
      }
    },
    handleDoFun(obj, fun, data) {
      if (this.onOk) {
        if (obj[fun]) {
          let n = this.onOk(obj, fun, data)
          if (fun === 'beforeFun') return n
        }
      } else {
        if (obj[fun]) this.$emit(obj[fun], { obj, data })
      }
      if (fun === 'beforeFun') return true
    },

    changeItem(item) {
      let formInfo = {
        class: 'c6',
        label: item.item.label,
        key: item.name,
        type: item.type,
        disabled: item.control.readonly,
        placeholder: item.control.placeholder,
        inline: this.item.inline,
        show: true,
      }
      formInfo.show = item.control[this.gps.processDefKey] ? this.getChangeData('showData', item.control[this.gps.processDefKey]) : true
      if (this.gps.location || (this.gps.action && this.gps.action == 'read')) {
        if ((!this.gps.modify && (this.gps.type == 'task' && this.gps.location !== process.env.VUE_APP_APPCODE + '.start'))) {
          formInfo.disabled = item.control[this.gps.processDefKey] ? !this.getChangeData('changeData', item.control[this.gps.processDefKey]) : item.control.readonly
        } else if (this.gps.type == 'draft' || (this.gps.type == 'task' && this.gps.location == process.env.VUE_APP_APPCODE + '.start')) {
          formInfo.disabled = item.control.readonly
        } else {
          formInfo.disabled = true
        }
      }
      formInfo = Object.assign(formInfo, this.util.changeListInfo(formInfo,item)) 
      return formInfo
    },


    getChangeData(type, arr) {
      // type showData是否显示  changeData是否编辑
      let arrs = arr
      if (this.gps.location == process.env.VUE_APP_APPCODE + '.') {
        return true
      } else {
        let location = this.gps.location ? this.gps.location : process.env.VUE_APP_APPCODE + '.start'
        const apiInfo = arrs.filter((item) => item.activityDefId == location)
        if (apiInfo.length > 0) {
          return !apiInfo[0][type]
        } else {
          return true
        }
      }
    },
  },
  created() {
    let itemArr = JSON.parse(JSON.stringify(this.item))
    itemArr.columns.forEach((element) => {
      for (let i in element.list) {
        let lists = this.changeItem(element.list[i])
        element.list[i] = lists
      }
    })
    this.itemArr = JSON.parse(JSON.stringify(itemArr))
  },
}
</script>
<style scoped></style>
