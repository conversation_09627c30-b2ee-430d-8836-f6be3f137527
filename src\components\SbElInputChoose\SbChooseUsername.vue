<template>
  <div class="w100 inputBtn">
    <el-input
      ref="elInput"
      :type="item.inputType || 'text'"
      v-bind="$attrs"
      v-on="$listeners"
      :size="item.size || 'small'"
      :placeholder="item.placeholder || item.label || '请输入'"
      :disabled="item.disabled || false"
      :readonly="item.readonly || false"
      :autosize="item.autosize || false"
    >
      <el-button
        slot="append"
        :size="item.size || 'small'"
        type="primary"
        :disabled="item.disabled || false"
        @click="openDialog"
        >{{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
      </el-button>
    </el-input>
    <el-dialog
      title="选择人员"
      v-dialogDrag
      :visible.sync="dialogVisible"
      width="60%"
      append-to-body
    >
      <el-container>
        <el-aside width="200px">
          <el-tree
            :props="defaultProps"
            ref="tree"
            node-key="id"
            @node-expand="nodeExpand"
            @node-click="treeClick"
            :data="treeData"
            accordion
            :expand-on-click-node="false"
          ></el-tree>
        </el-aside>
        <el-main>
          <sb-el-table
            :table="table"
            ref="chooseUsername"
            v-bind="$attrs"
            v-on="$listeners"
            @getList="getList"
            @updateTableData="updateTableData"
          ></sb-el-table>
        </el-main>
        <el-aside width="200px" class="asideR">
          <h5 class="fbold">已选人员</h5>
          <div class="chooseD">
            <a
              v-for="citem in table.multipleSelection"
              :key="citem.USERNAME"
              :USERNAME="citem.USERNAME"
              :TRUENAME="citem.TRUENAME"
              :PREFERREDMOBILE="citem.PREFERREDMOBILE"
              >{{ citem.TRUENAME
              }}<span class="fr" @click="delChoose(citem)"
                ><svg-icon iconClass="cuo1"></svg-icon></span
            ></a>
          </div>
        </el-aside>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="handleConfirm" size="small"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { findOrgTreeFromCorp, findUserOrgDim } from "@/api/public";
export default {
  name: "SbChooseUsername",
  props: {
    item: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "name"
      },
      treeData: [],
      table: {
        border: true, //是否带纵向边框
        loading: false, // 加载中动画
        modulName: "chooseUsername-OA账户", //列表中文名称
        stripe: true, //是否为斑马条样式
        hasSelect: true, //是否有复选框
        mulitple:
          (!this.item.mulitple && this.item.mulitple !== false) ||
          this.item.mulitple === true
            ? true
            : false, //单选多选
        data: [], //数据
        //addAndUpdateType:'dialog',
        total: null,
        hasQueryForm: true, //是否有查询条件
        // show有三种值：false隐藏当前列，true常规表格列，template自定义表格列  默认:true
        //<template slot-scope='props' slot='example'>
        //<a class='list-a' target='_blank' :href='"/#/bombscreen?mobile=" + props.obj.row.mobile'>{{ props.obj.row.username }}</a>
        //</template>
        queryForm: {
          inline: true,
          labelWidth: "0px",
          formItemList: [
            {
              placeholder: "OA账号/姓名/手机号",
              key: "searchFields",
              type: "input"
            }
          ]
        },
        tr: [
          //{ id: 'id',label: '编号',prop: 'id',width:'200', show: true},
          //{ id: 'accountid',label: '账户id',prop: 'accountid',width:'100'},
          //{ id: 'searchFields',label: 'OA账号/姓名/手机号',prop: 'searchFields',show:false},
          { id: "USERNAME", label: "OA账号", prop: "USERNAME" },
          { id: "TRUENAME", label: "姓名", prop: "TRUENAME", width: "120" },
          {
            id: "POSITIONNAME",
            label: "职务",
            prop: "POSITIONNAME",
            width: "140"
          },
          {
            id: "PREFERREDMOBILE",
            label: "手机号",
            prop: "PREFERREDMOBILE",
            width: "120"
          },
          { id: "EMAIL", label: "电子邮箱", prop: "EMAIL" }
        ],
        //dataDictionary:{},
        //userType:[],moneyType:[],//数据字典变量,列表数据
        multipleSelection: [], //多选选中数据存放变量
        //dialogVisible:false,//默认对话框关闭
        //dialogTitle:'现金充值',
        //form:{
        //    inline:true,
        //    formItemList:[
        //    //{label:'归属场所',key:'placeId',type:'select',rule:{required:true},fun:'getPlaceList',props:{value:'id',label:'name'}},//如果新增时有对应fun就可以不加
        //    {label:'账户',key:'username',type:'input',rule:{required:true}},
        //    {label:'姓名',key:'truename',type:'input',rule:{required:true}},
        //    {label:'余额类型',key:'moneyType',type:'select',rule:{required:true},dictType:'moneyType'},
        //    {label:'本次充值金额',key:'changeMoney',type:'input',rule:{required:true,type:'money'}}]
        //},
        hasOperation: false, //是否有操作列表
        //operation:{
        //    data:[{id:'addAcount',name:'账户充值',fun:'handleCashTopUpDialog'}]
        //},
        //listFormModul:{},
        hasPagination: true,
        listQuery: {
          size: 10,
          page: 1,
          corpid: this.$store.state.user.user.currentCorp
        },
        hasOtherQueryBtn: false,
        //otherQueryBtn:{
        //    data:[{id:'downloadF',name:'下载批量充值模板',fun:'downloadModelFile'}]
        //},
        hasBatchOperate: false //有无批量操作
        //batchOperate:{
        //    operateType:"deleteByIds",
        //    list:[{value:"deleteByIds",label:"批量删除",fun:"handleDeleteByIds"}]
        //}
      }
    };
  },
  methods: {
    openDialog(e) {
      //console.log(this.$refs.elInput);
      let inputData = this.$refs.elInput._props;
      let formData = this.$refs.elInput.elForm._props.model;
      //console.log(JSON.stringify(formData));
      //console.log('formData');
      let relevancy = this.item.relevancy.split(",");
      let di = formData[relevancy[0].split("-")[0]]
        ? formData[relevancy[0].split("-")[0]].split(",").length
        : 0;
      if (di === 0) {
        this.table.multipleSelection = [];
        if (this.$refs.chooseUsername)
          this.$refs.chooseUsername.handleClearSelection();
      }
      for (let i = 0; i < di; i++) {
        let datai = {};
        for (let j in relevancy) {
          var reF = relevancy[j].split("-");
          if (reF.length === 1) reF.push(reF[0]);
          datai[reF[1]] = formData[reF[0]]
            ? formData[reF[0]].split(",")[i]
            : "";
        }
        var ml = 0;
        for (let k in this.table.multipleSelection) {
          for (let j in relevancy) {
            var reF = relevancy[j].split("-");
            if (reF.length === 1) reF.push(reF[0]);
            if (datai[reF[1]] === this.table.multipleSelection[k][reF[1]]) ml++;
          }
        }
        if (ml !== relevancy.length) this.table.multipleSelection.push(datai);
      }
      //console.log(JSON.stringify(this.table.multipleSelection));
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("chooseData", this.table.multipleSelection);
    },
    getTreeData() {
      findOrgTreeFromCorp(this.$store.state.user.user.currentCorp).then(res => {
        this.treeData = this.util.toTreeData(
          res.data,
          "id",
          "parentId",
          "id,name,treeType,parentId"
        );
        //console.log(JSON.stringify(this.treeData));
        //this.$nextTick(function () {
        //    console.log("update") // => '更新完成'
        //})
      });
    },
    treeClick(data, node, tree) {
      this.table.listQuery.orgCode = node.data.id;
      this.table.listQuery.username = "";
      this.getList();
    },
    nodeExpand(data, node, tree) {},
    getList(listQuery) {
      this.table.loading = true;
      findUserOrgDim(listQuery || this.table.listQuery)
        .then(res => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
          if (this.table.multipleSelection.length > 0) {
            //let a=JSON.parse(JSON.stringify(this.table.multipleSelection));
            //let b=JSON.parse(JSON.stringify(this.table.data));
            //for(let i in this.table.multipleSelection){
            //    console.log(this.table.data.includes(this.table.multipleSelection[i]));
            //}
            for (let i in this.table.data) {
              for (let j in this.table.multipleSelection) {
                let fl = 0;
                for (let a in this.table.multipleSelection[j]) {
                  if (
                    this.table.data[i][a] &&
                    this.table.multipleSelection[j][a] === this.table.data[i][a]
                  ) {
                    fl++;
                  }
                }
                if (fl === this.item.relevancy.split(",").length) {
                  let self = this;
                  setTimeout(function() {
                    self.$refs.chooseUsername.handleToggleRowSelection(
                      self.table.data[i],
                      true
                    );
                  }, 200);
                }
              }
            }
          }
          this.table.loading = false;
        })
        .catch(err => {
          this.table.loading = false;
        });
    },
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },
    delChoose(row) {
      if (
        (!this.item.mulitple && this.item.mulitple !== false) ||
        this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.table.multipleSelection));
        for (let i in arry) {
          let fl = 0;
          for (let j in row) {
            if (arry[i][j] === row[j]) fl++;
          }
          if (fl === row.length) arry.splice(arry[i], 1);
        }
        this.table.multipleSelection = arry;
        for (let i in this.table.data) {
          let fl = 0;
          for (let a in row) {
            if (this.table.data[i][a] && row[a] === this.table.data[i][a]) {
              fl++;
            }
          }
          if (fl === this.item.relevancy.split(",").length) {
            this.$refs.chooseUsername.handleToggleRowSelection(
              this.table.data[i],
              false
            );
          }
        }
      } else {
        //单选
        this.table.multipleSelection = [];
        this.$refs.chooseUsername.handleClearSelection();
      }
    }
  },
  created() {
    this.getTreeData();
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}
.el-main {
  padding: 0px 20px;
  margin-left: 20px;
}
.asideR {
  border-left: 1px solid #e0e0e0;
  padding-left: 15px;
}
.chooseD a {
  display: block;
  padding: 5px 0;
}
</style>
