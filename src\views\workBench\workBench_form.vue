<template>
  <div class="app">
    <el-card shadow="never">
      <div class="my_work">
        <div class="workTitle">
          <!-- <i class="el-icon-menu" style="font-size: 24px;color: rgba(192, 0, 0, 1);margin: 3px 5px 0 20px;"></i>
          <div style="font-weight: bold;">我的待办已办</div> -->
          <el-button :type="currType=='task'?'primary':''" size="small" style="margin-left: 30px" @click="handleTypeChange('task')">我的待办</el-button>
          <el-button :type="currType=='join'?'primary':''" size="small" @click="handleTypeChange('join')">我的已办</el-button>
          <!-- <el-button :type="currType=='draft'?'primary':''" size="small" @click="handleTypeChange('draft')">我的草稿</el-button> -->
          <!-- <div class="workQuery">
            <span style="width:110px">任务标题</span>
            <el-input v-model="table.listQuery.title" placeholder="任务标题" size="small" style="margin: 0 20px 0 10px"></el-input>
            <el-button type="primary" size="small" @click="handleTypeChange(currType)" >查询</el-button>
          </div> -->
        </div>
        <div class="workContent">
          <sb-el-table :table="table" :key="tableKey" @handleTodo="handleTodo" @handleTodoJoin="handleTodoJoin" :on-ok="handleDoFun" @getList="getList">
            <template v-slot:title="{obj}">
              <span class="toDetail" @click="handleTodo(obj)">{{obj.row.title}}</span>
            </template>

            <template v-slot:type="{obj}">
              <span v-if="obj.row.type == '0'">派发工单</span>
              <span v-else-if="obj.row.type == '1'">下发工单</span>
              <span v-else-if="obj.row.type == '2'">签订工单</span>
            </template>
          </sb-el-table>
        </div>
      </div>
    </el-card>
    <div style="margin-top: 20px;display:flex;justify-content:space-between" v-show="echartsShow">
      <el-card shadow="never" style="width: 50%">
          <div class="workTitle">
            <i class="el-icon-menu" style="font-size: 24px;color: rgba(192, 0, 0, 1);margin: 3px 5px 0 20px;"></i>
            <div style="font-weight: bold;">本年签订情况</div>
          </div>
          <div id="echartsBarYear" style="width:100%;height: 300px"></div>
      </el-card>  
      <el-card shadow="never" style="width: 49%">
        <div class="workTitle">
          <i class="el-icon-menu" style="font-size: 24px;color: rgba(192, 0, 0, 1);margin: 3px 5px 0 20px;"></i>
          <div style="font-weight: bold;">本月签订情况</div>
        </div>
        <div id="echartsBarMonth" style="width:100%;height: 300px"></div>
      </el-card>
    </div>

    <!-- 工单详情 -->
    <el-dialog :title="dialogTitle" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <work-order :key="cKey" :gps="gps" :dialogClose="dialogClose"></work-order>
    </el-dialog>
  </div>
</template>
<script>
import { queryMyJoin,myTaskTodo,myDraft,annualStatistics,monthlyStatistics,isAdminShow } from "@/api/home";
import WorkOrder from "@/components/WorkOrder";
import * as echarts from "echarts";
export default {
  name: 'app',
  components: {
    WorkOrder
  },
  data() {
    return {
      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),
      currType: 'task',
      viewD: false,
      dialogTitle: "",
      gps: {
        type: "task",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      tableKey: 0,
      table: {
        modulName: "processTask-待办列表", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
          ],
        },
        tr: [
          { id: "title", label: "任务标题", prop: "title" },
          { id: "departmentName", label: "创建部门", prop: "departmentName", width: 350 },
          { id: "applyTrueName", label: "创建人", prop: "applyTrueName", width: 150 },
          { id: "pushDate", label: "创建时间", prop: "pushDate", width: 190 },
          { id: "deadlineDate", label: "截止时间", prop: "deadlineDate", width: 190 },
          { id: "locationName", label: "当前办理环节", prop: "locationName", width: 190 }
          // { id: "type", label: "工单类型", prop: "type", width: 160 ,show: "template",}
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "80",
          fixed: "right",
          data: [
            { id: "handleTodo", name: "办理", fun: "handleTodo" },
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      echartsShow:false
    }
  },
  created() {
    this.handleTypeChange('task')
    this.getEchartsShow()

  },
  mounted() {
    
  
  },
  methods: {
    getEchartsShow(){
      isAdminShow().then(res=>{
        console.log(res)
        this.echartsShow = res.data == true?true:false;
        if(this.echartsShow){
          this.getBarEchartsYear()
          this.getBarEchartsMonth()
        }
      })
    },
    handleTypeChange(type) {
      this.table.listQuery.page = 1;
      this.currType = type;
      this.getList()
    },
    getBarEchartsMonth() {
      let xAxisData = []
      let yqs = []
      let wqs = []
      const myChart = echarts.init(document.getElementById('echartsBarMonth'))
      myChart.clear()
      const options = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            left: 'center',
            top: 'bottom',
        },
        color: ['#e95345','#f1b6ae',],
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '5%',
            containLabel: true
        },
        xAxis: [
            {
                type: 'category',
                data: xAxisData,
                axisLabel:{interval: 0, rotate: 45  },
                axisTick: {
                    alignWithLabel: true
                }
            }
        ],
        yAxis: [
            {
                type: 'value',
                name: ''
            }
        ],
        series: [
            {
                name: '已签署',
                type: 'bar',
                barWidth: '10',
                data: yqs
            },
            {
                name: '未签署',
                type: 'bar',
                barWidth: '10',
                data: wqs
            },
        ]
      };
      monthlyStatistics({year:new Date().getFullYear(),month:(new Date().getMonth()+1)<10?'0'+(new Date().getMonth()+1):(new Date().getMonth()+1)}).then((res) => {
        for(let i=0;i<res.data.length;i++){
          xAxisData.push(res.data[i].company)
          yqs.push(res.data[i].complete)
          wqs.push(res.data[i].nocomplete)
        }
        myChart.setOption(options)
        myChart.resize()
      })
    },
    getBarEchartsYear() {
      let xAxisData = []
      let groupData = []

      annualStatistics({year:new Date().getFullYear()}).then((res) => {
        if (!res.data || res.data.length === 0) return
        // 获取第一条数据中的所有分组名称
        const groupNames = res.data[0].grouping.map(g => g.groupingName)
        // 初始化每个分组的数据数组
        groupData = groupNames.map(name => ({
          name,
          data: []
        }))
        res.data.forEach(company => {
          xAxisData.push(company.company)
          company.grouping.forEach(group => {
            const groupIndex = groupNames.indexOf(group.groupingName)
            if (groupIndex !== -1) {
              groupData[groupIndex].data.push(group.num)
            }
          })
        })

        const myChart = echarts.init(document.getElementById('echartsBarYear'))
        myChart.clear()

        const options = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
 
          legend: {
            left: 'center',
            top: 'bottom',
          },
          color: ['#e95345','#ed7d31','#fbe5d6','#ffc000'],
          grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            top: '5%',
            containLabel: true
          },
          xAxis: [{
            type: 'category',
            data: xAxisData,
            axisLabel: {
                interval: 0, // 强制显示所有标签（根据需要调整）
                rotate: 45   // 旋转标签，防止重叠
            },
            axisTick: {
              alignWithLabel: true
            }
          }],
          yAxis: [{
            type: 'value',
            name: ''
          }],
          // 动态生成 series
          series: groupData.map(group => ({
            name: group.name,
            type: 'bar',
            barWidth: '10',
            data: group.data
          }))
        }
        
        myChart.setOption(options)
        myChart.resize()
      })
    },
    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data)
      return n
    },
    // 办理
    handleTodo(obj) {
      // 参数
      this.gps = {
        location: obj.row.location,
        pmInsType:'A',
        pmInsId: obj.row.pmInsId,
        id: obj.row.id,
        types: obj.row.endTime?'join':'task'
      };

      // 工单标题
      var th = this.util.appNameTH(obj.row.pmInsType);
      this.dialogTitle = (obj.row.title || "") + "";

      this.cKey++;
      this.viewD = true;
    },
    // 已办
    handleTodoJoin(obj){
      this.gps = {
        location: obj.row.location,
        pmInsType: 'A',
        pmInsId: obj.row.pmInsId,
        id: obj.row.id,
      };

      // 工单标题
      var th = this.util.appNameTH(obj.row.PMINSTTYPE);
      this.dialogTitle = (obj.row.title || "") + "-派发";

      this.cKey++;
      this.viewD = true;
    },
    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    getList() {
      if(this.currType=='task') {
        this.table.loading = true;
        myTaskTodo(this.table.listQuery).then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        }).catch((err) => {
          this.table.loading = false;
        });

        this.table.operation.data = [
          { id: "handleTodo", name: "办理", fun: "handleTodo" },
        ]
        this.tableKey++;
      } else if(this.currType=='join') {
        this.table.loading = true;
        queryMyJoin(this.table.listQuery).then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        }).catch((err) => {
          this.table.loading = false;
        });

        this.table.operation.data = [
          { id: "handleTodo", name: "查看", fun: "handleTodo" },
        ]
        this.tableKey++;
      } else {
        this.table.loading = true;
        myDraft(this.table.listQuery).then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        }).catch((err) => {
          this.table.loading = false;
        });

        this.table.operation.data = [
          {id: "handleTodo",name: "编辑", fun:"handleTodo"},
        ]
        this.tableKey++;
      }

    }
  }
}
</script>
<style scoped>
.app {
    /* height: calc(100vh - 100px); */
    padding: 10px;
}
.workTitle {
  display: flex;
  line-height: 30px;
}
.workQuery {
  display: flex;
  /* float: right; */
  position: absolute;
  right: 30px;
}
.workContent {
  margin-top: 20px;
}
::v-deep .el-card__body {
  padding: 10px 0;
}

::v-deep .el-card{
  padding: 10px 0;
}



</style>                  