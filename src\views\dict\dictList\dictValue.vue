<template>
	<div>
		<sb-el-table :table="table"
			@getList="getList"
			@handleCreate="handleCreate"
			@handleUpdate="handleUpdate"
			@handleDelete="handleDelete"
			@handleDeleteByIds="handleDeleteByIds"
			@handleUpdateGetRow="handleUpdateGetRow"
			@updateTableData="updateTableData"
			@handleChangeEnable="handleChangeEnable">
			<template v-slot:enabled="{obj}">
				<el-switch v-model="obj.row.enabled" @change="handleChangeEnable(obj.$index,obj.row)"></el-switch>
			</template>

			<template v-slot:isDefault="{obj}">
				<div>
					<el-switch v-model="obj.row.isDefault" :disabled="true"></el-switch>
				</div>
			</template>
		</sb-el-table>
	</div>
</template>
<script> 
import { findAllDictValue,findDictValueById,createDictValue,updateDictValue,deleteDictValueById,deleteDictValueByIds,updateDictValueEnable } from "@/api/dict/dictValue.js"

export default {
	name: "dictValue",
	props: {
		dictType: {
			type: String,
			required: true
    	},
		isPublic:{
			type:Boolean,
			required:true
		}
	},

  data() {
    return {
		table: {
			border: true,//是否带纵向边框
			loading: true, // 加载中动画
			modulName: 'dictValue-字典值', // 列表中文名称
			stripe: true, // 是否为斑马条样式
			hasSelect: true, // 是否有复选框
			data: [], // 数据
			addAndUpdateType: 'dialog',
			total: null,
			showIndex: true, // 序号
			hasQueryForm: false, // 是否有查询条件
			queryForm: {},
			tr: [
				{id: 'name', label: '字典值名称', prop: 'name', align: 'center'},
				{id: 'value', label: '字典值', prop: 'value', align: 'center'},
				// {id: 'description', label: '字典值描述', prop: 'description', align: 'center'},
				{id: 'displayOrder', label: '字典值排序', prop: 'displayOrder', align: 'center'},
				{id: 'valueType', label: '数据类型', prop: 'valueType', align: 'center'},
				{id: 'enabled', label: '是否启用', prop: 'enabled', show: "template", className: 'enabled', align: 'center'},
				{id: 'isDefault', label: '是否默认', prop: 'isDefault', show: 'template', className: 'isDefault', align: 'center',width:140}
			],
			multipleSelection: [], //多选选中数据存放变量
			dialogVisible: false, //默认对话框关闭
			form:{
				width: '450px',
				inline:true,
				formItemList:[
					{label: '字典值名称', key: 'name', type: 'input', rule: {required: true}},
					{label: '字典值', key: 'value', type: 'input', rule: {required: true}},
					{label: '排序', key: 'displayOrder', type: 'input', rule: {required: true, type: 'zinteger'}},
					{label: '是否默认', key: 'isDefault', type: 'switch'},
					{label: '是否启用', key: 'enabled', type: 'switch', defaultValue: true},
					{label: '数据类型', key: 'valueType', type: 'input'},
					{label: '字典值描述', key: 'description', inputType: "textarea", autosize: true, type: 'input'}
				],
			},
			listFormModul: {},
			hasOperation: true,//是否有操作列表
			operation: {
				width: '200',
				align: 'center',
				data: [
					{id: 'add', name: '新增', fun: 'handleCreate'},
					{id: 'update', name: '编辑', fun: 'handleUpdate'},
					{id: 'delete', name: '删除', fun: "handleDelete"}
				]
			},
			hasPagination: true,
			listQuery: {size: 10, page: 1},
			hasBatchOperate: true,//有无批量操作
			batchOperate:{
				operateType:"deleteByIds",
				list:[
					{value:"deleteByIds",label:"批量删除",fun:"handleDeleteByIds"}
				]
			}
		}
		}
  	},
	created(){
		this.table.listQuery.dictType = this.dictType;
		this.table.listFormModul.dictType = this.dictType;
		this.getList();
	},
 	methods: {
		// 查询列表
		getList(listQuery){
			this.table.loading=true;
			findAllDictValue(listQuery || this.table.listQuery).then(res => {
				this.table.loading = false;
				this.table.data = res.data.content;
				this.table.total = res.data.totalElements;
					this.table.loading=false;
			}).catch(err => {
				this.table.loading=false;
			});
		},

		// 新增
		handleCreate() {
			this.table.listFormModul.dictType = this.dictType;
			this.table.listFormModul.isPublic = this.isPublic;
			this.table.listFormModul.enabled = true;
			createDictValue(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false;
				this.getList();
			});
		},

		// 编辑
		handleUpdate() {
			updateDictValue(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false;
				this.getList();
			});
		},

		// 删除
		handleDelete(row) {
			deleteDictValueById(row.id).then(res => {
				this.getList();
			});
		},

		// 批量删除
		handleDeleteByIds(ids) {
			deleteDictValueByIds(ids).then(res => {
				this.getList();
			});
		},
		
		// 根据id查询行数据
		handleUpdateGetRow(row) {
			findDictValueById(row.id).then(res => {
				this.table.listFormModul = res.data;
				this.print(this.table.listFormModul);
			});
		},

		// 刷新数据
		updateTableData(obj) {
			for (let i in obj) {
				this.$set(this.table, i, obj[i]);
			}
		},

		// 修改是否启用
		handleChangeEnable(index, row) {
			updateDictValueEnable(row.id, row.enabled).then(res => {
			}).catch(error => {
				this.table.data[index].enabled = this.table.data[index].enabled;
			});
		}
  	},
  	watch: {
		"dictType": function(newVal){
			this.table.listQuery.dictType = newVal;
			this.table.listFormModul.dictType = newVal;
			this.getList();
		}
  	}
  
}
</script>
