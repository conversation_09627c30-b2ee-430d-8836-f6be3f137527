<template>
	<div class="app-container">
		<sb-el-table :table="table"
		 @getList="getList"
		 @handleCreate="handleCreate"
		 @handleUpdate="handleUpdate"
		 @handleDelete="handleDelete"
		 @handleDeleteByIds="handleDeleteByIds"
		 @handleUpdateGetRow="handleUpdateGetRow"
		 @handleView="handleView"
		 @updateTableData="updateTableData" :on-ok="handleDoFun">
			<template v-slot:enabled="{obj}">
				<el-switch v-model="obj.row.enabled" @change="handleChangeEnable(obj.$index,obj.row)"></el-switch>
			</template>
		</sb-el-table>
		<!-- 查看字典值 -->
		<el-dialog title="查看字典值" v-dialogDrag :visible.sync="dictViewFlag" :close-on-click-modal="false" width="60%">
			<dict-value :dictType="dictType" :isPublic="isPublic"></dict-value>
		</el-dialog>
	</div>
</template>
<script> 
import { findAllDict,findDictById,createDict,updateDict,deleteDictById,deleteDictByIds,updateDictEnable } from "@/api/dict/dict.js"
import dictValue from "./dictValue";

export default {
	name: "dictionary",
	components: { dictValue },
	data() {
		return {
			dictType: '',
			isPublic:true,
			dictViewFlag: false, // 是否显示字典值对话框
			table: {
				border: true, // 是否带纵向边框
				loading: true, // 加载中动画
				modulName: 'dictionary-数据字典', // 列表中文名称
				stripe: true, // 是否为斑马条样式
				hasSelect: true, // 是否有复选框
				data: [], // 数据
				addAndUpdateType: 'dialog',
				total: null,
				showIndex: true, // 序号
				hasQueryForm: true, // 是否有查询条件
				queryForm: {
					inline: true,
					labelWidth: "90px",
					formItemList: [
						{label:"字典名称", key:'name', type:'input'},
						{label:"字典编码", key:'dictType', type:'input'},
						// {placeholder:"是否启用", key:'enabled', type:'select', props:{value: 'id', label:'name'}, options: [{id: true, name: "已启用"}, {id: false, name: "未启用"}], nullText:'全部'}
					]
				},
				tr: [
					{id: 'name', label: '字典名称', prop: 'name', align: 'center'},
					{id: 'dictType', label: '字典编码', prop: 'dictType', align: 'center'},
					{id: 'enabled', label: '是否启用', prop: 'enabled', show: "template", className: 'enabled', width: '150', align: 'center'},
					{id: 'displayOrder', label: '排序', prop: 'displayOrder', width: '150', align: 'center'},
					{id: 'createdTime', label: '创建时间', prop: 'createdTime', formatter:{type:'date',fmtStr:'yyyy-MM-dd hh:mm:ss'}, align: 'center'},
					{id: 'modifiedTime', label: '修改时间', prop: 'modifiedTime', formatter:{type:'date',fmtStr:'yyyy-MM-dd hh:mm:ss'}, align: 'center'},
					// {id: 'description', label: '备注', prop: 'description', align: 'center'}
				],
				multipleSelection: [], //多选选中数据存放变量
				dialogVisible: false, //默认对话框关闭
				form:{
					width: '450px',
					inline:true,
					formItemList:[
						{class: 'c10', label: '字典名称', key: 'name', type: 'input', rule: {required: true}},
						{class: 'c10', label: '字典编码', key: 'dictType', type: 'input', rule: {required: true}},
						{class: 'c10', label: '是否公共', key: 'isPublic', type: 'switch', defaultValue: true},
						{class: 'c10', label: '是否启用', key: 'enabled', type: 'switch', defaultValue: true},
						{class: 'c10', label: '排序', key: 'displayOrder', type: 'input', rule: {required: true, type: 'zinteger'}},
						{class: 'c10', label: '备注', key: 'description', type: 'input', inputType: "textarea", autosize: true,}
					],
				},
				listFormModul: {},
				hasOperation: true,//是否有操作列表
				operation: {
					width: '300',
					align: 'center',
					data: [
						{id: 'add', name: '创建', fun: 'handleCreate'},
						{id: 'dictView', name: '维护字典值', fun: 'handleView'},
						{id: 'update', name: '编辑', fun: 'handleUpdate'},
						{id: 'delete', name: '删除', fun: "handleDelete"}
					]
				},
				hasPagination: true,
				listQuery: {size: 10, page: 1},
				hasBatchOperate: true,//有无批量操作
				batchOperate:{
					operateType:"deleteByIds",
					list:[{value:"deleteByIds",label:"批量删除",fun:"handleDeleteByIds"}]
				}
			}
		}
  	},
	created(){
		this.getList();
	},
	methods: {
		// 查询列表
		getList(listQuery){
			this.table.loading=true;
			this.table.listQuery.enabled=1;
			this.table.listQuery.removedTime=null;
			findAllDict(listQuery || this.table.listQuery).then(res => {
				this.table.loading = false;
				this.table.data = res.data.content;
				this.table.total = res.data.totalElements;
					this.table.loading=false;
				}).catch(err => {
					this.table.loading=false;
			});
		},

		// 新增
		handleCreate() {
			createDict(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false;
				this.getList();
			});
		},

		// 编辑
		handleUpdate() {
			updateDict(this.table.listFormModul).then(res => {
				this.table.dialogVisible = false;
				this.getList();
			});
		},

		// 删除
		handleDelete(row) {
			deleteDictById(row.id).then(res => {
				this.getList();
			});
		},

		// 批量删除
		handleDeleteByIds(ids) {
			deleteDictByIds(ids).then(res => {
				this.getList();
			});
		},
		
		// 根据id查询行数据
		handleUpdateGetRow(row) {
			findDictById(row.id).then(res => {
				this.table.listFormModul = res.data;
			});
		},

		// 刷新数据
		updateTableData(obj) {
			for (let i in obj) {
				this.$set(this.table, i, obj[i]);
			}
		},

		handleDoFun(obj, fun, data) {
			//若一个beforeFun可直接在这个函数里面写
			let n = this[obj[fun]].call(this, obj, data);
			return n;
		},

		// 修改是否启用
		handleChangeEnable(index, row) {
			updateDictEnable(row.id, row.enabled).then(res => {
			}).catch(error => {
				this.table.data[index].enabled = this.table.data[index].enabled;
			});
		},

		// 打开维护字典值对话框
		handleView(obj) {
			this.dictType = obj.row.dictType;
			this.isPublic=obj.row.isPublic;
			this.dictViewFlag = true;
		}

	}
}
</script>
