<template>
  <div :class="gps.location?'w99':'p10'">
      <process-btn
          ref="processBtn"
          :gps="gps"
          :formData="appFormValue"
          :decisionTypeArr="decisionTypeArr"
          :formBtnsArr='formBtnsArr' 
          :dialogClose="dialogClose"
          :on-ok="handleDoFun"
      ></process-btn>

      <div class="message tableWarp tableForm">
          <sb-el-form ref="appForm" :key="formIndex" :gps="gps" :form="appForm" v-model="appFormValue" :disabled="appForm.formDisabled" @allChangeFun="allChangeFun" @allblurFun="allblurFun" @afterUpload="afterUpload" @handleRemove="handleRemove" :on-ok="handleDoFun" :controlProcess="controlProcess"></sb-el-form>
          <div></div>
      </div>

      <div
        :style="{height:'40px',textAlign:'left',overflow:'hidden',whiteSpace:'nowrap',textOverflow:'ellipsis',paddingLeft:'5px',lineHeight:'40px',borderLeft:'5px solid #39aef5',fontSize: '16px',color: '#333',fontWeight: '700',margin: '10px 20px'}"
      >
        <span>廉洁从业承诺书</span>
        <el-button v-if="gps.type!=='join'" type="primary" size="small" style="float: right;margin-top: 10px;" @click="handleAdd">新增</el-button>
      </div>
      <div>
        <sb-el-table :table="table" :key="tableKey" :on-ok="handleDoFun" @handleUpdate="handleUpdate" @handleDelete="handleDelete" @handlePerson="handlePerson">
        </sb-el-table>
      </div>

      <el-dialog title="新增或修改" :visible.sync="centerDialogVisible" width="45%" center>
        <div class="tableForm">
          <sb-el-form :form="dialogForm" v-model="table.listFormModul"></sb-el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="centerDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleCreate">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 选择签署人 -->
      <ConfigDialog ref="userDialog" :item="userAllocatData" @chooseData="chooseData" @getYiYouList="queryUserByPermissionId"/>
  </div>
</template>
<script>
import store from "@/store";
import util from "@/assets/js/public";
import { Message, MessageBox } from "element-ui";
import ProcessBtn from "@/components/Process/ProcessBtn";
import { getDictList,getApiList } from "@/api/public";
import { deleteDraft } from "@/api/process";
import { getFormDetail, saveDraft, deleteProcess, stopProcess,initForm } from "@/api/apply/application";
import ConfigDialog from "../system/component/configDialog";


let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  creator: store.getters.user.truename
}; 

export default {
  name: 'application',
  components: { ProcessBtn, ConfigDialog },
  props: {
    href: {
      type: Object,
      default(){
        return {};
      }
    },
    // 关闭
    dialogClose: {
      type: Function
    }
  },

  data() {
    return {
      currentMenuRows: {},
      userAllocatData: {
				inputType: 'text', title: '选择签署人员', appendShow: true, rows: 12, type: 'RY', btnText: '搜索', mulitple: true, dialogVisible: false,
				defaultProps: { children: "children", label: "name", isLeaf: 'leaf', },
			},
      dialogForm: {
        formDisabled: false,
        labelWidth: "160px",
        size:"default",
        inline: true,
        size:'small',
        labelPosition:"right", 
        formItemList: [
          { class: "c12", label: "分组名称", key: "RECEIPTTILE", type: "input" },
          { class: "c12", label: "廉洁从业承诺书", key: "CREATEORGNAME", type: "sbUpload" },
        ],
      },
      dialogFormCurr: {},
      Flag: true,
      centerDialogVisible: false,
      formIndex:0,
      gps: this.href,
      processType:  "A",
      processDefKey:  "Process_1730691457218",
      processBtnArr:  null,
      decisionTypeArr:  null,
      formBtnsArr:  null,

      processD: false,
      pnKey: 0,
      clickFlag: true,//防止多次点击

      nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),

      tableKey: 0,
      table: {
        modulName: "processTask-承诺书", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
          ],
        },
        tr: [
          { id: "RECEIPTTILE", label: "分组名称", prop: "RECEIPTTILE",width: 250 },
          { id: "CREATEORGNAME", label: "廉洁从业承诺书", prop: "CREATEORGNAME", width: 350  },
          { id: "qianshurenyuan", label: "签署人员", prop: "qianshurenyuan",  },
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: { width: "600px", labelWidth: "100px", inline: true, formItemList: [], },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "200", fixed: "right",
          data: [
            {id: 'update1', name: '编辑', fun: 'handleUpdate',},
						{id: 'delete', name: '删除', fun: "handleDelete"},
            {id: 'seletUser', name: '选择签署人员', fun: "handlePerson"},
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },

      // 业务表单
      initValue: {},
      appFormValue: Object.assign({},defaultAppFormValue),
      appForm:{
          formDisabled: false, labelWidth: "160px", size:"default", inline: true, formItemList: [], size:'small', labelPosition:"right", 
      },
      
      FormList: [
        {
          class: "c12",
          label: "",
          key: "txt1730702445050",
          type: "txt",
          showLabel: false,
          disabled: false,
          rule:
          {
            required: false
          },
          item:
          {
            label: "头部标题",
            showLabel: false
          },
          control:
          {
            modelValue: "廉洁从业承诺书派发",
            fontSize: 18,
            align: "center",
            color: "#0070c3",
            labelAlign: "right"
          }
        },
        {
          class: "c4",
          label: "签订年份",
          key: "createdTime",
          type: "date",
          showLabel: false,
          disabled: false,
          listConfig:
          {
            isTodo: 0,
            isDone: 0,
            spareName: null
          },
          rule:
          {
            required: true
          },
          modelValue: null,
          dateType: "currTime",
          subtype: "year",
          valueFormat: "yyyy"
        },
        {
          class: "c4",
          label: "签订截止日期",
          key: "datePicker1730702492052",
          type: "date",
          showLabel: false,
          disabled: false,
          rule:
          {
            required: true
          },
          modelValue: "",
          dateType: "fixedTime",
          subtype: "date",
          valueFormat: "yyyy-MM-dd"
        },
        {
          class: "c4",
          label: "派发人",
          key: "creator",
          type: "input",
          showLabel: false,
          disabled: true,
          listConfig:
          {
            isTodo: 0,
            isDone: 0,
            spareName: null
          },
          rule:
          {
            required: true
          }
        },
        // {
        //   class: "c12",
        //   type: "tip",
        //   value: "廉洁从业承诺书",
        //   key: "tip1730702741449",
        //   align: "left"
        // },
      
      ],
      controlProcess: {
        txt1730702445050:
        {
          modelValue: "廉洁从业承诺书派发",
          fontSize: 18,
          align: "center",
          color: "#0070c3",
          labelAlign: "right"
        },
        createdTime:
        {
          modelValue: null,
          type: "year",
          format: "YYYY",
          timePickerRule: "",
          startTime: "",
          endTime: "",
          labelAlign: "right",
          ishide: "",
          listConfig:
          {
            isTodo: 0,
            isDone: 0,
            spareName: null
          }
        },
        datePicker1730702492052:
        {
          modelValue: "",
          type: "date",
          format: "YYYY-MM-DD",
          timePickerRule: "fromCurrentTimeDate",
          startTime: "",
          endTime: "",
          labelAlign: "right"
        },
        creator:
        {
          modelValue: "",
          computeMode: "",
          dateRule: "",
          startWay: "",
          startDate: "",
          endWay: "",
          endDate: "",
          dateUnit: "",
          labelAlign: "right",
          ishide: "",
          listConfig:
          {
            isTodo: 0,
            isDone: 0,
            spareName: null
          },
          readonly: true
        },
        tip1730702741449:
        {
          modelValue: "廉洁从业承诺书",
          fontSize: 18,
          align: "left",
          labelAlign: "right"
        },
        button1730702806970:
        {
          modelValue: "新增",
          clickFun: null,
          labelAlign: "right",
          btnStyle: "primary",
          btnAlignStyle: "right"
        }
      },
      // 子表传的字段
      sublistFields:[],
    }
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps,query);
    this.initValue = {
      applyUser: this.$store.getters.user.truename,
      applyUserName: this.$store.getters.user.username,
      belongCompanyName: this.$store.getters.user.belongCompanyTypeDictValue == '03'  ? this.$store.getters.user.belongCompanyNameParent : this.$store.getters.user.belongCompanyName,
      belongDepartmentName: this.$store.getters.user.belongDepartmentName,
      applyPhone: this.$store.getters.user.preferredMobile,
      applyTime: this.nowTime
    };
    this.appFormValue = Object.assign(defaultAppFormValue,this.initValue);
        
        if(this.createdResponse){  //表单设计器自定义初始化
            this.createdResponse()
        }
        const list = this.getFlowFormList()
        this.appForm.formItemList = list
        this.appForm.formItemList.forEach(item => {
          if(item.type=="date" && item.modelValue) {
            this.appFormValue[item.key] = this.util.getNow(item.valueFormat); //item.modelValue
          }
        })

    //廉洁从业承诺书表格
    

    this.initFun();//初始化
  },
  mounted() {},
  methods: {
    //更新人员信息
		chooseData(array,type) {
			let idx = this.currentMenuRows.index;
			this.table.data[idx].qianshurenyuan = array
		},
    //查询已有人员
		queryUserByPermissionId() {
			this.currentMenuRows.row.qianshurenyuan.forEach((user) =>{
        user.name = user.truename
        user.id = user.username
      })
      this.$refs.userDialog.multipleSelection = res.data
		},
    //选择签署人员
    handlePerson(obj) {
			this.currentMenuRows = obj;
			this.userAllocatData.dialogVisible = true;
			this.queryUserByPermissionId()
		},
    // 编辑
		handleUpdate(obj) {
      this.Flag = false;
      this.dialogFormCurr = obj;
			this.centerDialogVisible = true;
      this.table.listFormModul = obj.row;
		},

		// 删除
		handleDelete(row,idx) {
			this.table.data.splice(idx,1);
      this.tableKey++;
		},
    handleAdd() {
      this.centerDialogVisible = true;
      this.Flag = true;
      this.table.listFormModul = {}
    },
    handleCreate() {
      if(this.Flag) {
        this.table.data.push(this.table.listFormModul);
      } else {
        let idx = this.dialogFormCurr.index;
        this.table.data[idx] = this.table.listFormModul;
      }
      this.tableKey++;
      this.centerDialogVisible = false;
    },
    // 初始化
    initFun(){
            if (this.gps.pmInsType) {
              var typeArrs = this.processType.split(",");
              var keyArrs = this.processDefKey.split(",");
              for (var i in typeArrs) {
                if (typeArrs[i] == this.gps.pmInsType) {
                  this.processDefKey = keyArrs[i];
                }
              }
            } else {
              this.gps.pmInsType = this.processType;
            }
            this.gps.processDefKey = this.processDefKey;
      // 加载表单
      if(this.gps.location || (this.gps.action && this.gps.action=="read")){
        this.loadForm();
      } else {
                // 判断是否存在自动生成工单编号操作
                this.util.changeAllArr()
                let allArr = this.util.allArr(this.FormList)
                if(allArr.length>0){
                    allArr.forEach(element => {
                        if ((element.key == 'orderNo') && element.control.generationMode == '自动生成') {
                            this.automatic = true
                            this.autoParams = {
                                "noTemplate": "constantRule#delimiterRule1#dateRule#delimiterRule2#serialNumberRule",
                                "dateRule": element.control.dateRule,
                                "serialNumberRule": "period|" + element.control.period + "#numLength|" + element.control.numLength + "|Y#startNum|" + element.control.startNum,
                                "constantRule": element.control.constantRule,
                                "delimiterRule1": element.control.delimiterRule1,
                                "delimiterRule2": element.control.delimiterRule2,
                                "processDefKey":this.processDefKey,
                                "appCode": "ljcns",
                            }
                        }
                    });
                }
                
        initForm(this.autoParams).then(res => {
          if (res && res.data) {
            if (res.data.orderNo) {
              this.appFormValue.orderNo = res.data.orderNo;
            }
            if (res.data.serialNumber) {
              this.appFormValue.serialNumber = res.data.serialNumber;
            }
          }
        })
            }
    },
    // 获取formList
    getFlowFormList() {
      return this.FormList.map(item => {
        const currentProcess = this.controlProcess[item.key] && this.controlProcess[item.key][this.processDefKey] // 判断当前的是不是流程控制，返回的是一个布尔值
        item.show = currentProcess ? this.getChangeData("showData", currentProcess) : true
        if (!item.show) {
          item.rule = {required: false}
        }
        item.flowRequired = currentProcess ? this.getChangeData("flowRequired", currentProcess) : false
        if (item.flowRequired && item.show) {
          item.rule = {required: true}
        }
        if (this.gps.location || (this.gps.action && this.gps.action == 'read')) {
          if ((!this.gps.modify && (this.gps.type == 'task' && this.gps.location !== "ljcns.start"))) {
            item.disabled = currentProcess ? !this.getChangeData('changeData', currentProcess) : this.controlProcess[item.key].readonly
          } else if (this.gps.type == 'draft' || (this.gps.type == 'task' && this.gps.location == "ljcns.start")) {
            item.disabled = this.controlProcess[item.key].readonly
          } else {
            item.disabled = true
          }
        }
        if (item.type == 'echoTable') {
          if (item.key == this.controlProcess[item.key].correlation) {
            item.alKey = item.key
          }
        }
        if (item.type == 'uploadFile') {
          if (!this.gps.location || this.gps.type == "draft") {
            item.disabled = false;
          }
          if (this.gps.type == 'join' || this.gps.type == 'doRead' || this.gps.type == 'toRead') {
            item.disabled = true
          }
          if (!this.gps.modify && this.gps.type == "task" && this.gps.location !== "ljcns.start") {
            item.disabled = currentProcess ? !this.getChangeData('changeData', currentProcess)  : item.control.readonly
          }
        }
        if(this.controlProcess[item.key] && this.controlProcess[item.key].allTypes && this.controlProcess[item.key].allTypes =='sub'){
          this.sublistFields.push({
            'allFileds':this.controlProcess[item.key].allFileds,
            'fileds':item.key,
          })
        }
        item.control = this.controlProcess[item.key]
        return item
      })
    },
    getFile() {
        const filesArr = this.FormList.filter((item) => item.type == 'sbUpload')
        let filesObj = {}
        if(filesArr.length > 0){
          filesArr.forEach(element => {
              filesObj[element.fileId] = element.key
          });
        }
        return filesObj
    },
    // 获取工单详情
    loadForm(){
      var data = {
        pmInsId: this.gps.pmInsId,
        processDefKey: this.gps.processDefKey,
                file:this.getFile()
      };
      getFormDetail(data).then((res) => {
        this.appFormValue = res.data;
                this.formIndex++
        // 设置只读
                // if ((this.gps.type != "draft" && this.gps.type != "task") || this.gps.location != "ljcns.start") {
                //     for (var i in this.appForm.formItemList) {
                //         this.appForm.formItemList[i].disabled = true;
                //     }
                // } else {
                //     this.appForm.formDisabled = false;
                // }
                if(this.afterResponse){
                    this.afterResponse(res.data)
                }
      });
    },

    // 重置表单
    handleFormReset(){
      this.appFormValue = Object.assign(defaultAppFormValue,this.initValue);
    },

    // 判断是否有增加列表业务字段
    listConfigFun(){
      var list = []
      this.FormList.forEach(element => {
        if(element.listConfig && (element.listConfig.isTodo == 1 || element.listConfig.isDone == 1) && element.listConfig.spareName){
          list.push({
            "act_field":element.listConfig.spareName,
            "us_field":element.key
          })
        }
      });
      return list
    },
    // 流转下一步
    handleNextBtn(){
      this.$refs['appForm'].$children[0].validate((valid) => {
        if(!valid){
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500
          });
          return false;
        }else{
                    if(this.sublistFields.length > 0){
                        this.changeSub()
                    }
                    let listArr = this.listConfigFun()
                    if(listArr.length>0){
                        this.appFormValue.actConfigParam = listArr
                    }
          this.$refs["processBtn"].doProcessNext();
        }
      });
    },
    // 处理子表数据
    changeSub(){
        let mergedItems = this.sublistFields.reduce((acc, current) => {
            let existing = acc.find(item => item.allFileds === current.allFileds);
            if (existing) {
            existing.fileds = existing.fileds+','+current.fileds;
            } else {
            acc.push({ ...current });
            }
            return acc;
        }, []);
        mergedItems.forEach(element => {
            let filedArrs = (element.fileds).split(",")
            let filedObj = {}
            filedArrs.forEach(element => {
            filedObj[element] = this.appFormValue[element]
            });
            this.appFormValue[element.allFileds] = [filedObj]
        });
    },
    // 保存草稿
    handleSaveDraft(){
      if(this.clickFlag){
        this.clickFlag = false;
        saveDraft({"processDefKey": this.processDefKey,"pmInsType":this.processType,"title": this.appFormValue.title,"formData": this.appFormValue}).then((res) => {
            this.clickFlag = true;
            if(!this.gps.location){
              this.$router.push({name: "processDraft"});
            }else{
              this.dialogClose();
            }
        }).catch((err) => {
            this.clickFlag = true;
        });
        
      }
    },

    // 废除草稿
    handleAbolish(){
      this.$confirm('确认废除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          if(this.clickFlag){
              this.clickFlag = false;
              deleteDraft({pmInsId: this.appFormValue.pmInsId,processDefKey: this.processDefKey}).then((res) => {
                  this.clickFlag = false;
                  this.dialogClose();
              }).catch((err) => {
                  this.clickFlag = true;
              });
          }
        }).catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });          
        });
    },
    // 废除归档
    handleDeleteProcess() {
        MessageBox.confirm('确定废除归档么', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }).then((action) => {
            this.clickFlag = true
            deleteProcess({
                pmInsId: this.appFormValue.pmInsId,
                processInstId: this.gps.processInstId,
            })
            .then((res) => {
                this.clickFlag = false
                this.dialogClose();
            })
            .catch((err) => {
                this.clickFlag = true
            })
        })
    },
    // 终止
    handleStopProcess() {
        MessageBox.confirm('确定终止该流程么', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        }).then((action) => {
            this.clickFlag = true
            stopProcess({
                pmInsId: this.appFormValue.pmInsId,
                processInstId: this.gps.processInstId,
            })
            .then((res) => {
                this.clickFlag = false
                this.dialogClose();
            })
            .catch((err) => {
                this.clickFlag = true
            })
        })
    },

    getChangeData(type, arr) {
        // type showData是否显示  changeData是否编辑
          let arrs = arr
          if (this.gps.location == "ljcns.") {
            return true
          } else {
            let location = this.gps.location ? this.gps.location :  "ljcns" + ".start";
            const apiInfo = arrs.filter((item) => item.activityDefId == location)
            let userName = this.$store.getters.user.username;
            if(location.indexOf('.start')>-1) {
              if(this.gps.currUser==userName) {
                return true
              }
            }
            if (apiInfo.length > 0) {
                return apiInfo[0][type]
            } else {
                return  true
            }
          }
          
    },
    // 表单的值发生变化时触发
    allChangeFun(obj, value) {
        var index = this.FormList.findIndex(item => item.key === obj.key);
        if (index > -1) {
            this.controlProcess[obj.key].changeFun(obj, value)
        }
    },
    // 失去焦点时触发
    allblurFun(obj, value) {
        var index = this.FormList.findIndex(item => item.key === obj.key);
        if (index > -1) {
            this.controlProcess[obj.key].blurFun(obj, value)
        }
    },

    afterUpload(obj,data){
        let idsArr = []
        if(obj.multiple){
            idsArr = this.appFormValue[obj.fileId] ? (this.appFormValue[obj.fileId]).split(",") : []
        }
        data.sysFiles.forEach(element => {
            idsArr.push(element.id)
        });
        this.appFormValue[obj.fileId] = idsArr.join(",")
    },
    handleRemove(obj){
        let sysFiles = this.appFormValue[obj.key]
        let idsArr = []
        if(sysFiles.length>0){
              sysFiles.forEach(element => {
                idsArr.push(element.id)
            });
        }
        this.appFormValue[obj.fileId] = idsArr.join(",")
    },


    handleDoFun(obj, fun, data) {
        //若一个beforeFun可直接在这个函数里面写
        let n;
        if (obj) {
          n = this[obj[fun]].call(this, obj, data);
        } else {
          n = this[fun].call(this, data);
        }
        return n;
    }
    // cutomcode start

    // cutomcode end
  },
  beforeCreate(){
      if (this.beforeCreateResponse) {
        this.beforeCreateResponse()
      }
  },
  beforeUpdate(){
      if (this.beforeUpdateResponse) {
        this.beforeUpdateResponse()
      }
  },
  updated(){
      if (this.updatedResponse) {
        this.updatedResponse()
      }
  },
  beforeMount(){
      if (this.beforeMountResponse) {
        this.beforeMountResponse()
      }
  },
  mounted(){
      if (this.mountedResponse) {
        this.mountedResponse()
      }
  },
  beforeDestroy(){
      if (this.beforeDestroyResponse) {
        this.beforeDestroyResponse()
      }
  },
  destroyed(){
      if (this.destroyedResponse) {
        this.destroyedResponse()
      }
  },
}
</script>
<style scoped>
.app {
    height: calc(100vh - 100px);
    padding: 10px;
}
.w99 {
  width: 99%;
  margin: 0 auto;
}
.p10 {
  padding: 0px;
}
.toptext {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
}
.message {
  margin: 20px;
}
</style>
<style>
  
</style>                                                                                                                                                                                                                                                