<template>
  <div class="w100 inputBtn">
    <el-input
      ref="elInput"
      :type="item.inputType || 'text'"
      v-bind="$attrs"
      v-on="$listeners"
      :size="item.size || 'small'"
      :placeholder="item.placeholder || item.label || '请输入'"
      :disabled="item.disabled || false"
      :readonly="item.readonly || false"
      :autosize="item.autosize || false"
    >
      <el-button
        v-if="item.appendShow"
        slot="append"
        :size="item.size || 'small'"
        type="primary"
        :disabled="item.disabled || false"
        @click="openDialog"
        >{{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
      </el-button>
    </el-input>
    <el-dialog
      title="选择公司" 
      v-dialogDrag
      :visible.sync="dialogVisible"
      width="50%"
      append-to-body
      :center="false"
    >
      <el-container>
        <el-main>
          <el-tree
            :props="item.defaultProps || defaultProps"
            ref="chooseOrgTree"
            :node-key="item.nodeKey || 'orgCode'"
            @node-click="treeClick"
            :data="treeData"
            @check="treeCheck"
            :default-checked-keys="defaultCheckedKs"
            accordion
            :expand-on-click-node="false"
            default-expand-all
            show-checkbox
            check-strictly
          ></el-tree>
        </el-main>
        <el-aside width="320px" class="asideR">
          <h5 class="fbold">已选公司</h5>
          <div class="choose-department-checked">
            <div class="choose-department-item" v-for="(citem, index) in multipleSelection" :key="citem[item.nodeKey || 'orgCode']">
              <div class="choose-department-item-text ellipsis-line-1">
                {{ citem[item.defaultProps.label || "orgName"] }}
              </div>
              <span class="choose-department-item-close" @click="delChoose(citem)">x</span>
            </div>
          </div>
        </el-aside>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="handleConfirm" size="small"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { findPOrgAndCityOrg, findSonByParentOrgId } from "@/api/public";
// import { findPOrgAndCityOrg, findSonByParentOrgId } from "@/api/senior";
export default {
  name: "SbChooseOrg",
  props: {
    item: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    },
  },
  data() {
    return {
      dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "orgName"
      },
      treeData: [],
      multipleSelection: [],
      defaultCheckedKs: [],
      reqs: []
    };
  },
  methods: {
    openDialog(e) {
      //console.log(this.$refs.elInput);
      let inputData = this.$refs.elInput._props;
      let formData = this.$refs.elInput.elForm._props.model;
      // console.log(formData);
      let relevancy = this.item.relevancy.split(",");
      let di = formData[relevancy[0].split("-")[0]]
        ? formData[relevancy[0].split("-")[0]].split(",").length
        : 0;
      if (di === 0) {
        this.multipleSelection = [];
        this.defaultCheckedKs = [];
        if (this.$refs.chooseOrgTree)
          this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
      for (let i = 0; i < di; i++) {
        let datai = {};
        for (let j in relevancy) {
          var reF = relevancy[j].split("-");
          if (reF.length === 1) reF.push(reF[0]);
          datai[reF[1]] = formData[reF[0]]
            ? formData[reF[0]].split(",")[i]
            : "";
        }
        var ml = 0;
        for (let k in this.multipleSelection) {
          for (let j in relevancy) {
            var reF = relevancy[j].split("-");
            if (reF.length === 1) reF.push(reF[0]);
            if (datai[reF[1]] === this.multipleSelection[k][reF[1]]) ml++;
          }
        }
        if (ml !== relevancy.length) {
          this.multipleSelection.push(datai);
          this.defaultCheckedKs.push(datai[this.item.nodeKey || "orgCode"]);
        }
      }
      //console.log(JSON.stringify(this.multipleSelection));
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("chooseData", this.multipleSelection);
      if (this.onOk && this.item.handleCompany) {
        this.onOk(this.item,"handleCompany",this.multipleSelection);
      }
    },
    getTreeData() {
      if (this.item.treeDataHttpRequest) {
        const { uid } = new Date().getTime();
        const options = {
          onProgress: e => {},
          onSuccess: res => {
            this.treeData = res;
            delete this.reqs[uid];
          },
          onError: err => {
            //this.handleError(err);
            delete this.reqs[uid];
          }
        };
        const req = this.handleHttpRequest(options);
        this.reqs[uid] = req;
        if (req && req.then) {
          req.then(options.onSuccess, options.onError);
        }
      } else {
        findPOrgAndCityOrg().then(res => {
          let datas = [res.data[0]]
          this.treeData = this.util.toTreeData(
            datas,
            "orgCode",
            "parentOrgCode",
            "orgCode,orgName,parentOrgCode,displayName"
          );
          // console.log(JSON.stringify(this.treeData));
          //this.$nextTick(function () {
          //    console.log("update") // => '更新完成'
          //})
        });
      }
    },
    handleHttpRequest(content, data) {
      this.$emit("uploadHttpRequest", {
        fun: this.item.treeDataHttpRequest,
        content,
        data
      });
    },
    treeClick(data, node, tree) {
      if (this.item.treeClickHttpRequest) {
        const { uid } = new Date().getTime();
        const options = {
          onProgress: e => {},
          onSuccess: res => {
            if (res.length > 0)
              this.$refs.chooseOrgTree.updateKeyChildren(
                data[this.item.nodeKey || "orgCode"],
                res
              );
            else
              this.$message({
                message: "该组织无下级组织！",
                type: "warning",
                duration: 3000
              });
            delete this.reqs[uid];
          },
          onError: err => {
            //this.handleError(err);
            delete this.reqs[uid];
          }
        };
        const req = this.handleHttpRequest(options, data);
        this.reqs[uid] = req;
        if (req && req.then) {
          req.then(options.onSuccess, options.onError);
        }
      } else {
        if (
          (this.item.stepLoad !== undefined && this.item.stepLoad !== false) ||
          this.item.stepLoad === true
        ) {
            if (node.level > 0 && node.level < 2) {
                findSonByParentOrgId(data[this.item.nodeKey || "orgCode"]).then(
                    res => {
                    if (res.data.length > 0)
                        this.$refs.chooseOrgTree.updateKeyChildren(
                        data[this.item.nodeKey || "orgCode"],
                        res.data
                        );
                    else
                        this.$message({
                        message: "该组织无下级组织！",
                        type: "warning",
                        duration: 3000
                        });
                    }
                );
            }
        }
      }
    },
    treeCheck(data, checkedD) {
      if (
        (!this.item.mulitple && this.item.mulitple !== false) ||
        this.item.mulitple === true
      ) {
        //多选
        var index = this.multipleSelection.findIndex(
          datai =>
            datai[this.item.nodeKey || "orgCode"] ===
            data[this.item.nodeKey || "orgCode"]
        );
        if (
          checkedD.checkedKeys.indexOf(data[this.item.nodeKey || "orgCode"]) >
          -1
        ) {
          this.multipleSelection.push(data);
        } else {
          this.multipleSelection.splice(index, 1);
        }
      } else {
        if (checkedD.checkedKeys.length === 0) {
          this.multipleSelection = [];
          this.$refs.chooseOrgTree.setCheckedKeys([]);
        } else {
          this.multipleSelection = [data];
          this.$refs.chooseOrgTree.setCheckedKeys([
            data[this.item.nodeKey || "orgCode"]
          ]);
        }
      }
    },
    delChoose(row) {
      if (
        (!this.item.mulitple && this.item.mulitple !== false) ||
        this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.multipleSelection));
        for (let i in arry) {
          if (
            arry[i][this.item.nodeKey || "orgCode"] ===
            row[this.item.nodeKey || "orgCode"]
          )
            arry.splice(arry[i], 1);
        }
        this.multipleSelection = arry;
      } else {
        //单选
        this.multipleSelection = [];
        this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
    }
  },
  created() {
    this.getTreeData();
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}
.el-dialog__body {
  padding: 0px 20px 30px;
}
.el-main {
  padding: 0px;
  border-left: 0px solid #e0e0e0;
}
.asideR {
  border-left: 1px solid #e0e0e0;
  padding-left: 15px;
}
.chooseD a {
  display: block;
  padding: 5px 0;
}

.choose-department-checked {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  height: 500px;
  overflow-y: auto;
  border: 1px solid #e6ebf5;
}

.choose-department-item {
  height: 30px;
  -js-display: flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.choose-department-item-text {
  width: 80%;
  user-select: none;
}

.choose-department-item-close {
  cursor: pointer;
  font-size: 16px;
}

</style>
