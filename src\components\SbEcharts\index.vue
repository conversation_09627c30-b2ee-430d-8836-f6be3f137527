<template>
    <div
        :id="item.key"
        :style="`padding: 20px;width:${
            this.control.styleWidth ? this.control.styleWidth : '100%'
        };height:${
            this.control.styleHeight ? this.control.styleHeight : '400px'
        };background-color: 'red'`"
    />
</template>

<script>
import * as echarts from "echarts";
import util from "@/assets/js/public";
import request from "@/assets/js/request";


export default {
    name: "sb-echarts",
    props: {
        item: {
            type: Object,
            required: true,
        },
        control: {
            type: Object,
            required: true,
        },
    },
    mounted() {
      this.init()
    },

    methods: {
        // 后续有接口了使用init
        refresh(echartsList){
          // 我已经获取到了数据 echartsList
          this.handleEcharts(echartsList);
        },
        init() {
            let echartsList = []
            if (Object.keys(this.control.api).length > 0) {
                this.getEchartsInfo().then((res)=>{
                  if(res.status === 200) {
                    echartsList = res.data;   // 把请求数据赋给echarts处理的数据，后续根据接口书写处理方法
                  }
                })
            } else {
                echartsList = this.control.data
            }
            this.handleEcharts(echartsList);
        },
        handelAPI() {
            const { type, reqUrl } = this.control.api;
            let params = {};
            this.control.api.params.forEach((item) => {
                params[item.key] = item.value;
            });
            let data = {};
            if (this.control.api.radio === "form-data") {
                this.control.api.body.listValue.forEach((item) => {
                    data[item.key] = item.value;
                });
            } else {
                const list = JSON.parse(
                    JSON.stringify(this.control.api.body.jsonValue)
                );
                list.forEach((item) => {
                    data[item.key] = item.value;
                });
            }
            return {
                type,
                reqUrl,
                params,
                data
            }
        },
        getEchartsInfo() {
            const {type, reqUrl, params, data} = this.handelAPI()
            return request({
                method: type,
                url: util.toUrl(`/${process.env.VUE_APP_APPCODE}${reqUrl}`),
                params: params,
                data: data,
                contentType: "application/json;charset=UTF-8",
            });
        },
        handleEcharts(echartsList) {
          const {mapX, showX, showY, mapYList} = this.control
          if (!echartsList.length) return false
          let legend = []
          let xAxis = {}
          let seriesData = []
          let tooltip = {}
          let xList = []
          echartsList.forEach((item, index) => {
            const itemKeys = Object.keys(item)
            xList.push(itemKeys.includes(mapX)?item[mapX]:'')
          })
          xAxis = {
            data: xList,
            show: this.control.type !== 'pie'?showX:false
          }

          // 解析生成Y轴坐标系
          let yAxis;
          if(this.control.type !== 'lineAndBar') {
            yAxis = {
              type: 'value',
              show:  this.control.type !== 'pie'?showY:false
            }
          } else {
            yAxis = []
            const {yAxisList} =  this.control
            yAxisList.forEach((item, index1)=>{
              let obj = {
                alignTicks: true,
                offset: item.offset*1,
                ...item,
              }
              yAxis.push(obj)
            })

          }


          /**
           * mapYList: 我有几个数据需要展示
           * currentY = {
           *  type: 'line', // 或者 ‘bar’
           *  seriesY: '对应字段',
           *  name: '名称',
           *  yAxis: '0'或者‘1’ 多Y轴情况 // 暂不考虑
           * }
           */
          tooltip = {
            show: true,
          }
          if (this.control.type !== 'pie') {
            tooltip.axisPointer = {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
            tooltip.trigger = 'axis'
            mapYList.forEach((currentY, index)=>{
              const yAxisIndex = this.control.type === 'lineAndBar'?yAxis.findIndex(item => currentY.yAxis===item.uid):0
              let seriesObj = {
                name: currentY.name,
                type: currentY.type,
                yAxisIndex,
                tooltip: {
                  valueFormatter: function (value) {
                    return value;
                  }
                },
                data: []
              }
              echartsList.forEach((item, index) => {
                seriesObj.data.push(item[currentY.seriesY])
              })
              legend.push(currentY.name)
              seriesData.push(seriesObj)
            })
          } else {
            const {mapY, isRing} = this.control
            seriesData[0] = {
              type: 'pie',
              radius: isRing?['40%','60%']:'60%',
              data: []
            }
            echartsList.forEach((item, index) => {
              const itemKeys = Object.keys(item)
              legend.push(itemKeys.includes(mapX)?item[mapX]:'')
              seriesData[0].data.push({
                name: item[mapX],
                value: item[mapY]
              })
            })
          }
          const myChart = echarts.init(document.getElementById(this.item.key))
          myChart.clear()
          const options = {
            title: {
              show: this.control.showTitle,
              text: this.control.title,
              left: 'center', // 标题位置，可以是'left', 'center', 'right'
              textStyle: {
                fontSize: 20, // 标题字体大小
                fontWeight: 'bold', // 标题字体粗细
                color: '#333', // 标题字体颜色
              },
            },
            tooltip: tooltip,
            legend: {
              show: this.control.showLegend,
              top: 'bottom',
              data: legend,
            },
            grid: {
              left: '3%',
              right: '3%',
              bottom: '10%',
              top: '80px',
              containLabel: true,
            },
            xAxis: {...xAxis},
            yAxis,
            series: seriesData,
          }
          console.log("查看option", options)
          myChart.setOption(options)
          myChart.resize()
        },
    },
};
</script>
