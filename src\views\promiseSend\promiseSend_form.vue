<template>
  <div :class="gps.location ? 'w99' : 'p10'">
    <!-- <process-btn ref="processBtn" :gps="gps" :processBtn="processBtn" :formData="appFormValue" :dialogClose="dialogClose" :on-ok="handleDoFun"></process-btn> -->
    <div class="pageInfo">
      <span
        class="btn nextBtn"
        v-show="gps.types == 'task' && gps.location == 'act_deptAdmin'"
        @click="handleSend()"
        ref="nextBtn"
      >
        <svg-icon icon-class="random"></svg-icon>
        <font>派发</font>
      </span>
      <span
        class="btn nextBtn"
        v-show="gps.types == 'task' && gps.location == 'act_perEnd'"
        @click="handleSend()"
        ref="nextBtn"
      >
        <svg-icon icon-class="random"></svg-icon>
        <font>归档</font>
      </span>
       <!--<span
        class="btn nextBtn"
        v-show="gps.types == 'task' && gps.location == 'act_perEnd'"
        @click="handleResetWrite()"
        ref="nextBtn"
      >
        <svg-icon icon-class="zhongzhi"></svg-icon>
        <font>重写</font>
      </span>-->
      <span
        class="btn nextBtn"
        v-show="
          (gps.types == 'task' && gps.location == 'out_flow') ||
          (gps.types == 'task' && gps.location == 'act_djLeader') ||
          (gps.types == 'task' && gps.location == 'act_djAdmin') ||
          (gps.types == 'task' && gps.location == 'act_dj_z_Leader')
        "
        @click="handleSend()"
        ref="nextBtn"
      >
        <svg-icon icon-class="random"></svg-icon>
        <font>同意</font>
      </span>
      <span
        class="btn nextBtn"
        v-show="
          (gps.types == 'task' && gps.location == 'out_return') ||
          (gps.types == 'task' && gps.location == 'act_djLeader') ||
          (gps.types == 'task' && gps.location == 'act_djAdmin') ||
          (gps.types == 'task' && gps.location == 'act_dj_z_Leader')
        "
        @click="handleNoSend()"
        ref="nextBtn"
      >
        <svg-icon icon-class="close"></svg-icon>
        <font>不同意</font>
      </span>
      <span
        class="btn nextBtn"
        v-show="gps.types == 'task' && gps.location == 'act_djLeader'"
        @click="handleTransfer()"
        ref="nextBtn"
      >
        <svg-icon icon-class="random"></svg-icon>
        <font>转办</font>
      </span>
      <span
        :class="{ 'btn nextBtn': true, 'next-isdisabled': !checkedSign }"
        v-if="gps.types == 'task' && gps.location == 'act_perSign'"
        @click="handleNext()"
        ref="nextBtn"
      >
        <svg-icon icon-class="random"></svg-icon>
        <font>下一步</font>
      </span>
      <!-- <span class="btn saveDraft" v-show="gps.state == '0' || !gps.state " @click="handleSaveDraft()">
				<svg-icon icon-class="baocun"></svg-icon>
				<font>保存草稿</font>
			</span> -->
      <!-- <span class="btn abolish" v-show="gps.state == '0'" @click="handleAbolish()">
				<svg-icon icon-class="shanchu"></svg-icon>
				<font>废除草稿</font>
			</span> -->
      <span class="btn optClose" @click="handleOptClose()">
        <svg-icon icon-class="close"></svg-icon>
        <font>关闭</font>
      </span>
    </div>

    <!-- 业务表单 -->
    <div class="message tableForm">
      <!-- {{gps.types == 'join'?'预览':(gps.location == 'act_deptAdmin' || gps.location == 'act_djAdmin')?'派发':gps.location == 'act_perEnd'?'预览':'学习'}} -->
      <div class="orderTitle" style>廉洁从业承诺书签订</div>
      <sb-el-form
        ref="appForm"
        :form="appForm"
        v-model="appFormValue"
        :disabled="appForm.formDisabled"
        :on-ok="handleDoFun"
      >
      </sb-el-form>
    </div>

    <div class="titbox">
      <span>廉洁从业承诺书</span>
      <div style="display: flex">
        <el-upload
          v-show="
            gps.types == 'task' &&
            gps.location == 'act_deptAdmin' &&
            !isFenCompanyShow
          "
          class="upload-demo ml10"
          :action="uploadData.action"
          :on-success="
            uploadData.isPopup
              ? uploadData.isDialog
                ? handleDialog
                : handlePopup
              : handleSuccess
          "
          :before-upload="handleProgress"
          multiple
          :limit="uploadData.limit"
          :show-file-list="false"
          ref="upload"
        >
          <el-button
            :key="uploadData.id"
            :size="uploadData.size || 'small'"
            type="primary"
            >{{ uploadData.name }}</el-button
          >
        </el-upload>
        <el-button
          type="primary"
          style="margin-left: 10px"
          size="mini"
          v-show="
            gps.types == 'task' &&
            gps.location == 'act_deptAdmin' &&
            !isFenCompanyShow
          "
          @click="handleDownTem"
          >模板下载</el-button
        >
      </div>
    </div>
    <!-- gps.type == '2' && gps.state == '2' -->
    <div
      v-if="
        this.gps.location == 'act_perEnd' || this.gps.location == 'act_perSign'
      "
      style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      "
    >
      <!-- 预览文档 -->
      <iframe-com
        v-if="gps.types == 'task' && picSysFileList.length == 0"
        :url="previewUrl"
        :fileType="fileType"
      ></iframe-com>
      <div
        class="checkSure"
        v-if="gps.types == 'task' && gps.location == 'act_perSign'"
      >
        <el-checkbox
          size="medium"
          fill="#c00000"
          v-model="checkedSign"
        ></el-checkbox>
        <span class="sign-text">我已学习完毕</span>
      </div>
      <el-button
        :disabled="!checkedSign"
        v-if="gps.types == 'task' && gps.location == 'act_perSign'"
        type="primary"
        style="margin-left: 10px; width: 20%; margin-top: 10px;margin-bottom: 20px;"
        size="middle"
        @click="handleNext"
        >下一步，将前往手机端手写签订</el-button
      >
      <div class="word-img" v-if="(gps.location == 'act_perEnd' && picSysFileList.length >0) ||(gps.location == 'act_perSign' && gps.types == 'join' && picSysFileList.length >0)">
        <img v-for="(item,index) in picSysFileList" :key="index" :src="item.mobileFilePath" class="word-img-style" alt="" @click="handlePreviewImage(index)" />
      </div>

      <!-- 图片预览弹窗 -->
      <el-dialog
        :visible.sync="imagePreviewVisible"
        append-to-body
        :modal-append-to-body="false"
        :show-close="true"
        custom-class="image-preview-dialog"
        width="80%"
        :title="' '"
      >
        <div class="image-preview-container">
          <div class="preview-nav prev" @click="prevImage" v-if="picSysFileList.length > 1">
            <i class="el-icon-arrow-left"></i>
          </div>
          <img :src="previewImageUrl" class="preview-image" alt="预览图片" />
          <div class="preview-nav next" @click="nextImage" v-if="picSysFileList.length > 1">
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
        <div class="image-counter" v-if="picSysFileList.length > 1">
          {{ currentImageIndex + 1 }} / {{ picSysFileList.length }}
        </div>
      </el-dialog>
    </div>

    <div v-if="gps.location != 'act_perEnd' && gps.location != 'act_perSign'">
      <sb-el-table
        :table="table"
        :key="cKey"
        :on-ok="handleDoFun"
        @handleEdit="handleEdit"
        @handlePerson="handlePerson"
        @handleDel="handleDel"
      >
        <template v-slot:files="{ obj }">
          <!--  -->
          <div v-for="it in obj.row.files" :key="it.id" @click="handleDown(it)">
            <span class="toDetail">{{ it.fileName }}</span>
          </div>
        </template>

        <template v-slot:trueNames="{ obj }">
          <div
            class="user-list"
            v-if="
              gps.types == 'task' &&
              gps.location == 'act_deptAdmin' &&
              !isFenCompanyShow
            "
          >
            <div
              class="user-item"
              v-for="(it, index) in obj.row.userList"
              :key="index"
            >
              <div>{{ it.trueName }} |</div>
              <div class="cancel" @click="handleUser(it, index, obj)">X</div>
            </div>
          </div>
          <div
            class="user-list"
            v-if="
              gps.types == 'join' ||
              (gps.types == 'task' && gps.location != 'act_deptAdmin') ||
              (gps.types == 'task' && isFenCompanyShow)
            "
          >
            <div
              class="user-item"
              v-for="(it, index) in obj.row.userList"
              :key="index"
            >
              <div>{{ it.trueName }}</div>
            </div>
          </div>
        </template>
      </sb-el-table>
    </div>

    <!-- 新增弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="viewD"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
      width="50%"
    >
      <addInfo
        :key="cKey"
        :types="types"
        :rowData="rowdata"
        @event="getEvent"
        @closeshowDialog="closeshowDialog"
      >
      </addInfo>
    </el-dialog>

    <!-- 扫码弹窗 -->
    <!-- <el-dialog title="" :visible.sync="qrShow" v-dialogDrag :close-on-click-modal="false" append-to-body
			> -->
    <div class="mask-qr" v-if="qrShow">
      <div class="close-qricon1" @click="qrClose()">
        <svg-icon icon-class="close" class="close-qricon"></svg-icon>
      </div>

      <div class="qrcode">
        <img class="qrcode-img" :src="qrImg" alt="" />
        <div class="qrcode-text">
          请通过手机扫描上方二维码在手机上手写签订<br />（微信扫一扫或手机浏览器扫一扫）
          <br>温馨提示：手写签订后请关闭此页面
        </div>
          
      </div>
    </div>
    <el-dialog
      title="选择审批人"
      :visible.sync="desionShow"
      v-dialogDrag
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="content">
        <div class="content-box">
          <el-radio-group v-model="desionRadioValue" v-if="desionRadioList.length > 0">
            <el-radio
              v-for="(item, index) in desionRadioList"
              :key="index"
              :label="item.USERNAME"
              >{{ item.TRUENAME }}</el-radio
            >
          </el-radio-group>
          <div v-if="desionRadioList.length == 0">无审批人</div>
        </div>
        <div style="display: flex; justify-content: center; padding: 20px 0">
          <el-button @click="desionShow = false"  size="small"
            >取消</el-button
          >
          <el-button type="primary" @click="handleDesion()" size="small" style="margin-left: 20px"
            >确认</el-button
          >
        </div>
      </div>
    </el-dialog>
    <!-- 选择签署人 -->
    <ConfigDialog
      ref="userDialog"
      :key="pnKey"
      :listData="listData"
      :item="userAllocatData"
      @chooseData="chooseData"
      :pmInsId="gps.pmInsId"
    />
  </div>
</template>
<script>
import addInfo from "@/components/myCom/addInfo";
import iframeCom from "@/components/myCom/iframeCom";
import { mapGetters } from "vuex";
import {
  distribute,
  saveDraft,
  queryDetails,
  issued,
  downloadTemplete,
  generateQRCode,
  getDjLeader,
} from "@/api/home";
import ConfigDialog from "../system/component/configDialog";

let defaultAppFormValue = {
  pmInsId: "",
  id: "",
  blank: "blank",
};

export default {
  name: "application",
  components: { addInfo, ConfigDialog, iframeCom },
  props: {
    href: {
      type: Object,
      default() {
        return {};
      },
    },
    // 关闭
    dialogClose: {
      type: Function,
    },
  },
  computed: {
    ...mapGetters(["tabnav"]),
  },
  data() {
    return {
      desionShow: false,
      desionRadioList: [],
      desionRadioValue: "",
      qrImg: "",
      uploadData: {
        id: "b",
        action: `/${process.env.VUE_APP_APPCODE}/action/UsApplicationForm/importExcel?source=PC`,
        name: "导入",
        isPopup: false,
        isDialog: false,
      },
      listData: [],
      previewUrl: "",
      fileType: "",
      userAllocatData: {
        inputType: "text",
        title: "选择签署人员",
        appendShow: true,
        rows: 12,
        type: "RY",
        btnText: "搜索",
        mulitple: true,
        dialogVisible: false,
        defaultProps: { children: "children", label: "name", isLeaf: "leaf" },
      },
      rowdata: {},
      viewD: false,
      cKey: 0,
      title: "",
      types: "",
      gps: this.href,
      processDefKey: "",
      processBtn: {
        optClose: false,
      },
      processD: false,
      pnKey: 0,
      clickFlag: true, //防止多次点击
      // nowTime: this.util.getNow("yyyy-MM-dd hh:mm:ss"),
      nowTime: this.util.getNow("yyyy-MM-dd"),
      // 业务表单
      initValue: {},
      appFormValue: Object.assign({}, defaultAppFormValue),
      appForm: {
        formDisabled: false,
        labelWidth: "150px",
        inline: true,
        formItemList: [
          {
            class: "c4",
            label: "签订年份",
            key: "signYear",
            type: "input",
            showLabel: false,
            disabled: true,
            rule: { required: true },
            modelValue: null,
            dateType: "currTime",
            subtype: "year",
            valueFormat: "yyyy",
            show: false,
          },
          {
            class: "c4",
            label: "派发日期",
            key: "pushDate",
            type: "input",
            showLabel: false,
            disabled: true,
            rule: { required: true },
            modelValue: null,
            dateType: "currTime",
            subtype: "year",
            valueFormat: "yyyy",
          },
          {
            class: "c4",
            label: "签订截止日期",
            key: "deadlineDate",
            type: "input",
            showLabel: false,
            disabled: true,
            rule: { required: true },
            modelValue: "",
            dateType: "fixedTime",
            subtype: "date",
            valueFormat: "yyyy-MM-dd",
          },
          {
            class: "c4",
            label: "派发单位",
            key: "departmentName",
            type: "input",
            showLabel: false,
            disabled: true,
            rule: { required: true },
          },
        ],
      },
      // 业务列表
      table: {
        modulName: "processTask-承诺书", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [],
        },
        tr: [
          { id: "groupingName", label: "人员类型", prop: "groupingName" },
          {
            id: "files",
            label: "廉洁从业承诺书类型",
            prop: "files",
            show: "template",
          },
          {
            id: "trueNames",
            label: "签署人员",
            prop: "trueNames",
            show: "template",
          },
          {
            id: "userNames",
            label: "签署人员OA",
            prop: "userNames",
            show: false,
          },
          {
            id: "userList",
            label: "签署人员LIST",
            prop: "userList",
            show: false,
          },
        ],
        // hasSetup:true,
        // setup:[],
        processType: [],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "200",
          fixed: "right",
          data: [
            // { id: "handleEdit", name: "【编辑】", fun: "handleEdit" },
            // { id: "handleDel", name: "【删除】", fun: "handleDel" },
            {
              id: "seletUser",
              name: "【选择签署人员】",
              fun: "handlePerson",
              show: "canChoose|1",
            },
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
      qrShow: false,
      joinFlag: false,
      checkedSign: false,
      isFenCompanyShow: false,
			isTransfer: false,
			picSysFileList:[],
      imagePreviewVisible: false,
      previewImageUrl: "",
      currentImageIndex: 0

    };
  },
  created() {
    var query = this.util.getQueryString();
    this.gps = Object.assign(this.gps, query);
    this.initValue = {
      applyUserName: this.$store.getters.user.truename,
      applyTrueName: this.$store.getters.user.username,
      endTime: this.nowTime,
      year: new Date().getFullYear().toString(),
    };
    this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);
    this.initFun(); //初始化
  },
  methods: {
    handleProgress() {
      this.tableLoading = true;
    },
    handleSuccess(response, file) {
      console.log(response);
      let jsstr = response.slice(
        response.indexOf("result=") + 7,
        response.lastIndexOf("<")
      );
      let responseData = JSON.parse(jsstr);
      if (responseData.status == 200) {
        this.$message({
          type: "success",
          message: "导入成功",
        });
        console.log(responseData);
        if (responseData.data) {
          for (var item in responseData.data) {
            console.log(item, responseData.data[item]);
            if (item == "党员") {
              responseData.data[item]?.forEach((el) => {
                this.table.data[2].userList.push(el);
              });
            } else if (item == "非党员") {
              responseData.data[item]?.forEach((el) => {
                this.table.data[3].userList.push(el);
              });
            }
          }
        }
      } else {
        this.table.loading = false;
        this.$message.error(responseData.message);
      }
    },
    handleUser(item, index, obj) {
      if (obj.$index == 0) {
        this.table.data.forEach((el, n) => {
          if (n == obj.$index) {
            this.$delete(el.userList, index);
            this.$set(el, "userList", el.userList);
          } else if (n == 1) {
            if (el.userList && el.userList.length > 0) {
              el.userList.push(item);
            } else {
              el.userList = [];
              el.userList.push(item);
            }
            this.$set(el, "userList", el.userList);
          }
        });
      } else {
        this.table.data[obj.$index].userList.splice(index, 1);
      }
    },

    //更新人员信息
    chooseData(array, type) {
      let idx = this.currentMenuRows.index;
      console.log(array);
      array.forEach((item) => {
        item.trueName = item.name;
        item.userName = item.id;
      });
      this.table.data[idx].userList = array;
      console.log(this.table.data[idx].userList);
    },
    //选择签署人员
    handlePerson(obj) {
      this.currentMenuRows = obj;
      this.pnKey++;
      // 确保设置 listData
      console.log(obj.row.userList);
      this.listData = obj.row.userList?.map((el) => ({
        name: el.trueName,
        id: el.userName,
      }));
      this.userAllocatData.dialogVisible = true;
    },
    // 关闭按钮
    handleOptClose() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if (
        this.gps.myFrom &&
        this.$router.currentRoute.path == "/workOrder"
      ) {
        if(this.gps.flushPortalUrl){
            // 集团单点流转
            var flushPortalUrl = decodeURIComponent(this.gps.flushPortalUrl);
            var params = {
                "appcode": this.gps.appcode,
                "uniqueId": this.gps.uniqueId,
                "itemId": this.gps.itemId,
            }
            var pageUrlNew = this.util.toUrl(flushPortalUrl,params);
            window.location.replace(pageUrlNew)
          }else{
             window.opener = null;
            window.open("", "_self");
            window.close();

          }
      } else {
        let item = this.tabnav.find((item) => item.path === this.$route.path);
        this.$store.dispatch("CloseTabnav", item).then((res) => {
          if (item.path === this.$route.path) {
            const lastTag = res.slice(-1)[0];
            // 前一个 tab-view 页面存在，就跳；不存在就到首页
            if (lastTag) {
              this.$router.push({ path: lastTag.path });
            } else {
              this.$router.push({ path: "/workBench/workBench_form" });
            }
          }
        });
      }
    },
    // 关闭按钮
    handleOptCloseNoFlash() {
      if (this.dialogClose) {
        //待办打开
        this.dialogClose();
      } else if (
        this.gps.myFrom &&
        this.$router.currentRoute.path == "/workOrder"
      ) {
        //单点
        window.opener = null;
        window.open("", "_self");
        window.close();
      } else {
        let item = this.tabnav.find((item) => item.path === this.$route.path);
        this.$store.dispatch("CloseTabnav", item).then((res) => {
          if (item.path === this.$route.path) {
            const lastTag = res.slice(-1)[0];
            // 前一个 tab-view 页面存在，就跳；不存在就到首页
            if (lastTag) {
              this.$router.push({ path: lastTag.path });
            } else {
              this.$router.push({ path: "/workBench/workBench_form" });
            }
          }
        });
      }
    },

    //删除数据
    handleDel(data) {
      this.rowIndex = data.index;
      this.$confirm("此操作将永久删除该数据, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.table.data.splice(this.rowIndex, 1);
          this.$message({ type: "success", message: "删除成功!" });
        })
        .catch(() => {});
    },

    // 下载附件
    handleDown(item) {
      window.open(
        process.env.VUE_APP_PROBASEURL + "/sys/file/download?id=" + item.id
      );
    },
    closeshowDialog() {
      this.viewD = false;
    },
    getEvent(data) {
      if (this.types == "add") {
        this.table.data.push(data);
      }
      if (this.types == "edit") {
        this.table.data.splice(this.rowIndex, 1, data);
      }
    },

    // 新增
    handleAdd() {
      this.cKey++;
      this.viewD = true;
      this.title = "新增";
      this.types = "add";
    },

    // 编辑
    handleEdit(data) {
      this.rowdata = data.row;
      this.cKey++;
      this.viewD = true;
      this.title = "编辑";
      this.types = "edit";
    },
    // 模板下载
    handleDownTem() {
      downloadTemplete(this.gps.pmInsId)
        .then((res) => {
          this.util.blobDownload(res.data, res.filename);
        })
        .catch((error) => {});
    },

    // 初始化
    initFun() {
      // type;//0派发工单  1下发工单  2签订工单
      console.log(this.gps, "this.gps");
      if (this.gps.id && this.gps.location) {
        this.loadForm();
      }
    },

    // 获取工单详情
    loadForm() {
      var data = {
        id: this.gps.id,
      };
      queryDetails(data).then((res) => {
        this.table.data = [
          {
            groupingName: "党员领导干部",
            userList: [],
            canChoose: 0,
            files: [
              {
                fileName: "《廉洁从业承诺书（党员领导干部）》",
                id: "F844886585475923968",
              },
            ],
          },
          {
            groupingName: "非党员领导干部",
            userList: [],
            canChoose: 0,
            files: [
              {
                fileName: "《廉洁从业承诺书（非党员领导干部）》",
                id: "F844886615972708352",
              },
            ],
          },
          {
            groupingName: "关键岗位党员员工",
            userList: [],
            canChoose: 1,
            files: [
              {
                fileName: "《廉洁从业承诺书（关键岗位党员员工）》",
                id: "F844886634553475072",
              },
            ],
          },
          {
            groupingName: "关键岗位非党员员工",
            userList: [],
            canChoose: 1,
            files: [
              {
                fileName: "《廉洁从业承诺书（关键岗位非党员员工）》",
                id: "F844886649741049856",
              },
            ],
          },
        ];
        this.appFormValue = res.data;
        this.appFormValue.blank = "blank";
        // this.appForm.formDisabled = true;
        // this.appFormValue.commitmentInfos.forEach(item => {
        // 	if(item.file) {
        // 		item.files = [item.file];
        // 	}
        // });
        this.table.data[0].userList = res.data.onePersonInfoList
          ? res.data.onePersonInfoList
          : [];
        this.table.data[1].userList = res.data.twoPersonInfoList
          ? res.data.twoPersonInfoList
          : [];
        this.table.data[2].userList = res.data.threePersonInfoList
          ? res.data.threePersonInfoList
          : [];
        this.table.data[3].userList = res.data.fourPersonInfoList
          ? res.data.fourPersonInfoList
          : [];
         if (this.gps.pmInsId.indexOf("A") != -1 && this.gps.location != "act_deptAdmin") {
          // 遍历table.data,从后向前删除userList为空的行
          for(let i = this.table.data.length - 1; i >= 0; i--) {
            if(!this.table.data[i].userList || this.table.data[i].userList.length === 0) {
              this.table.data.splice(i, 1);
            }
          }
         }
        if (this.gps.location == "act_deptAdmin" && this.gps.types == "task") {
          // 派发
        }
        if (
          this.gps.types == "join" ||
          (this.gps.types == "task" && this.gps.location != "act_deptAdmin")
        ) {
          console.log("6666");
          this.table.hasOperation = false;
        }
        if (
          this.gps.types == "task" &&
          this.gps.pmInsId.indexOf("B") != -1 &&
          res.data.todoType == "year" &&
          this.$store.getters.user.belongCompanyTypeDictValue != "01"
        ) {
          // 分公司
          this.table.hasOperation = false;
          this.isFenCompanyShow = true;
        }

        if (
          this.gps.location == "act_perEnd" ||
          this.gps.location == "act_perSign"
        ) {
          // 签订工单-待办
          // this.$refs.nextBtn.click()
          this.appForm.formItemList[0].show = true;
          this.appForm.formItemList[1].show = false;
					if( this.appFormValue.sysFile){
						this.previewUrl =
							this.util.getApiUrl() +
							"/sys/file/open?id=" +
							this.appFormValue.sysFile.id;
						this.fileType = this.appFormValue.sysFile.fileType;
					}

        }
				if( this.gps.location == "act_perEnd" || this.gps.location == "act_perSign"){
					this.picSysFileList = this.appFormValue.picSysFileList?this.appFormValue.picSysFileList:[];
					console.log(this.picSysFileList);

				}
      });
    },

    // 重置表单
    handleFormReset() {
      this.appFormValue = Object.assign(defaultAppFormValue, this.initValue);
    },

    beforeSubmit() {},
    // 同意
    handlleOk() {},
    // 不同意
    handleNot() {},
    // 归档
    handleGui() {},
    // 重写
    handleResetWrite() {
			generateQRCode({ id: this.gps.id,isAgainSign:1 }).then((res) => {
          console.log(res);
          this.qrImg = res.data.mobileFilePath;
          this.qrShow = true;
        });
		},
    handleNext() {
      // this.$confirm('我已认真学习，可以签订', '温馨提示', {
      // 				confirmButtonText: '确定',cancelButtonText: '返回学习',type: 'warning'
      // }).then(() => {
      // 	generateQRCode({id:this.gps.id}).then(res=>{
      // 		console.log(res)
      // 		this.qrImg = res.data.mobileFilePath;
      // 		this.qrShow = true;
      // 	})

      // }).catch(() => {});
      if (this.checkedSign) {
        generateQRCode({ id: this.gps.id,isAgainSign:'' }).then((res) => {
          console.log(res);
          this.qrImg = res.data.mobileFilePath;
          this.qrShow = true;
        });
      }
    },
    handleNoSend() {
      this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          // 不同意
          let infoData = {
            location: this.gps.location,
            type: "out_return",
            id: this.gps.id,
            pmInsId: this.gps.pmInsId,
            formData: {
              ...this.appFormValue,
            },
          };
          console.log(infoData);
          issued(infoData)
            .then((res) => {
              this.clickFlag = true;
              this.handleOptClose();
            })
            .catch((err) => {
              this.clickFlag = true;
            });
        }
      });
    },
    // 转办
    handleTransfer() {
      getDjLeader({
        location: this.gps.location,
        type: "out_transfer",
      }).then((user) => {
        console.log(user);
        this.desionRadioList = user.data;
        if (user.data.length > 0) {
          this.desionRadioValue = "";
					this.isTransfer = true;
          this.desionShow = true;
        }else{
           this.desionRadioValue = "";
					this.isTransfer = true;
          this.desionShow = true;
        }
      });
    },
    // 选择决策项
    handleDesion() {
      console.log(this.desionRadioValue);
      if (this.desionRadioList.length>0 && this.desionRadioValue == "") {
        this.$message({
          message: "请选择审批人员",
          type: "warning",
          duration: 1500,
        });
        return false;
      } else {
        let infoData = {
          location: this.gps.location,
          type: this.isTransfer?"out_transfer":"out_flow",
          id: this.gps.id,
          pmInsId: this.gps.pmInsId,
          nextUserName: this.desionRadioValue,
          formData: {
            ...this.appFormValue,
          },
        };
        console.log(infoData);
        issued(infoData)
          .then((res) => {
            this.clickFlag = true;
            this.desionShow = false;
						this.isTransfer = false;
            this.handleOptClose();
          })
          .catch((err) => {
            this.clickFlag = true;
          });
      }
    },

    // 派发
    handleSend() {
      this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          if (this.gps.location == "act_deptAdmin") {
            // 派发
            // 检查是否所有项都有 userNames
            const hasValidItem = this.table.data.some(
              (item) => item.userList.length > 0
            );
            if (!hasValidItem) {
              this.$message({
                message: "请先添加签署人员",
                type: "warning",
                duration: 1500,
              });
              return;
            }
            console.log(this.appFormValue, "this.appFormValue");
            this.appFormValue.onePersonInfoList =
              this.table.data[0].userList?.map((el) => ({
                trueName: el.trueName,
                userName: el.userName,
              }));
            this.appFormValue.twoPersonInfoList =
              this.table.data[1].userList?.map((el) => ({
                trueName: el.trueName,
                userName: el.userName,
              }));
            this.appFormValue.threePersonInfoList =
              this.table.data[2].userList?.map((el) => ({
                trueName: el.trueName,
                userName: el.userName,
              }));
            this.appFormValue.fourPersonInfoList =
              this.table.data[3].userList?.map((el) => ({
                trueName: el.trueName,
                userName: el.userName,
              }));
            // 检查重复账号
            const allUserNames = [];
            const duplicateUsers = new Set();

            this.table.data.forEach((item) => {
              const users = item.userList;
              console.log(users, "users");
              users?.forEach((user) => {
                if (allUserNames.includes(user.userName)) {
                  duplicateUsers.add(user.trueName);
                } else {
                  allUserNames.push(user.userName);
                }
              });
            });
            if (duplicateUsers.size > 0) {
              this.$confirm(
                `${Array.from(duplicateUsers).join(
                  ","
                )}不能同时签订多个承诺书，请重新选择。`,
                "温馨提示",
                {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                }
              )
                .then(() => {
                  return false;
                })
                .catch(() => {
                  return false;
                });
            } else {
              if (this.gps.pmInsId.indexOf("A") != -1) {
                // 存在
                getDjLeader({
                  location: this.gps.location,
                  type: "out_flow",
                }).then((user) => {
                  console.log(user);
                  this.desionRadioList = user.data;
                  if (user.data.length > 0) {
                    this.desionRadioValue = "";
                    this.desionShow = true;
                    this.isTransfer = false;
                  }else{
                    this.desionShow = true;
                    this.isTransfer = false;

                  }

                  // this.$confirm(
                  //   `请党建部领导审批：${user.data.TRUENAME}`,
                  //   "温馨提示",
                  //   {
                  //     confirmButtonText: "确认提交",
                  //     cancelButtonText: "取消",
                  //   }
                  // )
                  //   .then(() => {
                  //     let infoData = {
                  //       location: this.gps.location,
                  //       type: "out_flow",
                  //       id: this.gps.id,
                  //       pmInsId: this.gps.pmInsId,
                  //       formData: {
                  //         ...this.appFormValue,
                  //       },
                  //     };
                  //     console.log(infoData);
                  //     issued(infoData)
                  //       .then((res) => {
                  //         this.clickFlag = true;
                  //         this.handleOptClose();
                  //       })
                  //       .catch((err) => {
                  //         this.clickFlag = true;
                  //       });
                  //   })
                  //   .catch(() => {});
                });
              } else {
                let infoData = {
                  location: this.gps.location,
                  type: "out_flow",
                  id: this.gps.id,
                  pmInsId: this.gps.pmInsId,
                  formData: {
                    ...this.appFormValue,
                  },
                };
                console.log(infoData);
                issued(infoData)
                  .then((res) => {
                    this.clickFlag = true;
                    this.handleOptClose();
                  })
                  .catch((err) => {
                    this.clickFlag = true;
                  });
              }
            }

            // 处理文件
            // this.table.data.forEach(item => {
            // 	if(item.files && item.files.length > 0) {
            // 	item.file = item.files[0];
            // 	}
            // });

            // this.appFormValue.title = this.appFormValue.year + '年廉洁从业承诺书'
            // this.appFormValue.commitmentInfos = this.table.data
          } else if (this.gps.location == "act_perEnd") {
            // 归档
            let infoData = {
              location: this.gps.location,
              type: "out_flow",
              id: this.gps.id,
              pmInsId: this.gps.pmInsId,
              formData: {
                ...this.appFormValue,
              },
            };
            console.log(infoData);
            issued(infoData)
              .then((res) => {
                this.clickFlag = true;
                this.handleOptClose();
              })
              .catch((err) => {
                this.clickFlag = true;
              });
          }else if(this.gps.location == "act_djLeader"){
            if (this.gps.pmInsId.indexOf("A") != -1) {
               // 存在
                getDjLeader({
                  location: this.gps.location,
                  type: "out_flow",
                }).then((user) => {
                  console.log(user);
                  this.desionRadioList = user.data;
                  if (user.data&&user.data.length > 0) {
                    this.desionRadioValue = "";
                    this.desionShow = true;
                    this.isTransfer = false;
                  }else{
                    this.desionShow = true;
                    this.isTransfer = false;

                  }
                });
                } else {
                    let infoData = {
                      location: this.gps.location,
                      type: "out_flow",
                      id: this.gps.id,
                      pmInsId: this.gps.pmInsId,
                      formData: {
                        ...this.appFormValue,
                      },
                    };
                    console.log(infoData);
                    issued(infoData)
                      .then((res) => {
                        this.clickFlag = true;
                        this.handleOptClose();
                      })
                      .catch((err) => {
                        this.clickFlag = true;
                      });
                }

          } else if (
            this.gps.location == "act_dj_z_Leader" ||
            this.gps.location == "act_djAdmin"
          ) {
            let infoData = {
              location: this.gps.location,
              type: "out_flow",
              id: this.gps.id,
              pmInsId: this.gps.pmInsId,
              formData: {
                ...this.appFormValue,
              },
            };
            console.log(infoData);
            issued(infoData)
              .then((res) => {
                this.clickFlag = true;
                this.handleOptClose();
              })
              .catch((err) => {
                this.clickFlag = true;
              });
          } else if (this.gps.type == "0" || !this.gps.type) {
            if (this.table.data.length > 0) {
              this.table.data.forEach((item) => {
                if (item.files && item.files.length > 0) {
                  item.file = item.files[0];
                }
              });
              this.appFormValue.title =
                this.appFormValue.year + "年廉洁从业承诺书";
              this.appFormValue.commitmentInfos = this.table.data;
              distribute(this.appFormValue)
                .then((res) => {
                  this.clickFlag = true;
                  this.handleOptClose();
                })
                .catch((err) => {
                  this.clickFlag = true;
                });
            } else {
              this.$message({
                message: "请先添加承诺书",
                type: "warning",
                duration: 1500,
              });
              return false;
            }
          } else if (this.gps.type == "2") {
            this.$confirm("请您登录豫移办公-审批待办进行签署", "温馨提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(() => {})
              .catch(() => {});
          }
        }
      });
    },

    // 保存草稿
    handleSaveDraft() {
      this.$refs["appForm"].$children[0].validate((valid) => {
        if (!valid) {
          this.$message({
            message: "表单数据校验不通过",
            type: "warning",
            duration: 1500,
          });
          return false;
        } else {
          if (this.clickFlag) {
            this.clickFlag = false;

            if (this.table.data.length > 0) {
              // 处理 table.data 中的 file 字段
              this.table.data.forEach((item) => {
                if (item.files && item.files.length > 0) {
                  item.file = item.files[0];
                }
              });
              this.appFormValue.title =
                this.appFormValue.year + "年廉洁从业承诺书";
              this.appFormValue.commitmentInfos = this.table.data;
            }
            saveDraft(this.appFormValue)
              .then((res) => {
                this.clickFlag = true;
                this.handleOptCloseNoFlash();
              })
              .catch((err) => {
                this.clickFlag = true;
              });
          }
        }
      });
    },
    qrClose() {
      this.qrShow = false;
      this.handleOptCloseNoFlash();
    },

    // 预览图片
    handlePreviewImage(index) {
      this.currentImageIndex = index;
      this.previewImageUrl = this.picSysFileList[index].mobileFilePath;
      this.imagePreviewVisible = true;
    },

    // 上一张图片
    prevImage() {
      if (this.currentImageIndex > 0) {
        this.currentImageIndex--;
      } else {
        this.currentImageIndex = this.picSysFileList.length - 1;
      }
      this.previewImageUrl = this.picSysFileList[this.currentImageIndex].mobileFilePath;
    },

    // 下一张图片
    nextImage() {
      if (this.currentImageIndex < this.picSysFileList.length - 1) {
        this.currentImageIndex++;
      } else {
        this.currentImageIndex = 0;
      }
      this.previewImageUrl = this.picSysFileList[this.currentImageIndex].mobileFilePath;
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n;
      if (obj) {
        n = this[obj[fun]].call(this, obj, data);
      } else {
        n = this[fun].call(this, data);
      }
      return n;
    },
  },
};
</script>
<style scoped>
.w99 {
  width: 99%;
  margin: 0 auto;
}

.p10 {
  padding: 15px;
}

.titbox {
  display: flex;
  justify-content: space-between;
  align-items: center;

  font-size: 16px;
  font-weight: 700;
  border-left: 5px solid rgba(192, 0, 0, 1);
  padding-left: 10px;
  margin: 20px 0;
}

::v-deep .table-container {
  padding: 0px !important;
}
</style>
<style scoped>
.user-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.user-item {
  display: flex;
  align-content: center;
  background-color: rgba(255, 255, 255, 1);
  border: 1px solid rgba(170, 170, 170, 1);
  padding: 0 5px;
  margin-right: 3px;
  border-radius: 3px;
  font-size: 12px;
  line-height: 20px;
  margin-bottom: 3px;
}
.cancel {
  cursor: pointer;
  margin-left: 5px;
  color: rgba(192, 0, 0, 1);
}
.mask-qr {
  position: fixed; /* 固定定位 */
  z-index: 1; /* 置于顶层 */
  left: 0;
  top: 0;
  width: 100%; /* 宽度100% */
  height: 100%; /* 高度100% */
  overflow: auto; /* 如果需要滚动条 */
  background-color: rgb(0, 0, 0); /* 背景颜色 */
  background-color: rgba(0, 0, 0, 0.4); /* 背景颜色透明度 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.qrcode {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.qrcode-img {
  width: 300px;
  height: 300px;
}
.qrcode-text {
  background-color: #fff;
  padding: 20px 30px;
  color: #c00000;
  line-height: 32px;
  margin-top: 30px;
  font-size: 24px;
  border-radius: 10px;
  text-align: center;
}

.close-qricon1 {
  position: absolute;
  top: 30px;
  right: 50px;
  background: #fff;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
::v-deep .close-qricon {
  text-align: center;
  margin-right: 0 !important;
}
::v-deep .close-qricon.icon {
  margin-left: 0 !important;
  margin-right: 0 !important;
}
.checkSure {
  width: 20%;
  margin: 10px auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.sign-text {
  margin-left: 10px;
  color: #000;
  font-size: 16px;
}
::v-deep .el-checkbox__inner {
  width: 16px;
  height: 16px;
  border-color: #000;
}
::v-deep .el-checkbox__inner:hover {
  border-color: #c00000;
}
::v-deep .el-button--primary.is-disabled {
  background-color: #c0c4cc;
  border-color: #c0c4cc;
}
::v-deep .next-isdisabled {
  background-color: #c0c4cc !important;
  border-color: #c0c4cc !important;
  pointer-events: none;
}
.word-img{
	width: 80%;
	display: flex;
	flex-wrap: wrap;
}
.word-img-style{
	display: block;
	width: 33%;
	cursor: pointer;
	transition: all 0.3s;
}

.word-img-style:hover {
	opacity: 0.8;
	transform: scale(1.02);
}

.image-preview-container {
	display: flex;
	justify-content: center;
	align-items: center;
}

.preview-image {
	max-width: 100%;
	max-height: 80vh;
	object-fit: contain;
}

::v-deep .image-preview-dialog {
	background-color: rgba(0, 0, 0, 0.7);
}

::v-deep .image-preview-dialog .el-dialog__header {
	padding: 0;
	height: 0;
	overflow: hidden;
}

::v-deep .image-preview-dialog .el-dialog__headerbtn {
	top: 15px;
	right: 15px;
	font-size: 20px;
	z-index: 10;
}

::v-deep .image-preview-dialog .el-dialog__headerbtn .el-dialog__close {
	color: #fff;
}

::v-deep .image-preview-dialog .el-dialog__body {
	padding: 20px;
	position: relative;
}

.preview-nav {
	position: absolute;
	top: 50%;
	transform: translateY(-50%);
	width: 50px;
	height: 50px;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	transition: all 0.3s;
	z-index: 10;
}

.preview-nav:hover {
	background-color: rgba(0, 0, 0, 0.8);
}

.preview-nav.prev {
	left: 20px;
}

.preview-nav.next {
	right: 20px;
}

.preview-nav i {
	color: #fff;
	font-size: 24px;
}

.image-counter {
	position: absolute;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	padding: 5px 15px;
	border-radius: 15px;
	font-size: 14px;
}
</style>