const tab = {
    state: {
        tabnav: [],
    },
    mutations: {
        ADD_TABNAV: (state, tab) => {
            let itemA = state.tabnav.find(
                (ri, index, res) => ri.path === tab.path
            );
            if (!itemA) {
                state.tabnav.push(tab);
            }
        },
        CLOSE_TABNAV: (state, tab) => {
            let index = state.tabnav.findIndex(
                (item) => item.path === tab.path
            );
            if (index > -1) {
                state.tabnav.splice(index, 1);
            }
        },
        CLOSE_TABOTHER: (state, tab) => {
            let items = state.tabnav.filter((it) => it.path === tab.path);
            state.tabnav = items;
        },
        CLOSE_TABALL: (state) => {
            state.tabnav = [];
        },
    },
    actions: {
        AddTabnav: ({ commit, state }, tab) => {
            return new Promise((resolve, reject) => {
                commit("ADD_TABNAV", tab);
                resolve([...state.tabnav]);
            });
        },
        CloseTabnav: ({ commit, state }, tab) => {
            return new Promise((resolve, reject) => {
                commit("CLOSE_TABNAV", tab);
                resolve([...state.tabnav]);
            });
        },
        CloseTabAll: ({ commit, state }, tab) => {
            return new Promise((resolve, reject) => {
                commit("CLOSE_TABALL");
                resolve([...state.tabnav]);
            });
        },
        CloseTabOther: ({ commit, state }, tab) => {
            return new Promise((resolve, reject) => {
                commit("CLOSE_TABOTHER", tab);
                resolve([...state.tabnav]);
            });
        },
        TabActiveRefresh: ({ commit }) => {},
    },
};
export default tab;
