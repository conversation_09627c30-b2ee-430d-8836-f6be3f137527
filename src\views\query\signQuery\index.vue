<template>
  <div class="app-container">
    <sb-el-table
      :table="table"
      @getList="getList"
      @handleTodo="handleTodo"
      @updateTableData="updateTableData"
      @handleExport="handleExport"
      :on-ok="handleDoFun"
    >
      <template v-slot:BUSINESS_TITLE="{obj}">
        <span class="toDetail" @click="handleTodo(obj)">{{obj.row.BUSINESS_TITLE}}</span>
      </template>
      <template v-slot:CREATED_TIME="{obj}">
        <span>{{util.getTimeDate(obj.row.CREATED_TIME,"yyyy-MM-dd HH:mm:ss")}}</span>
      </template>
      <template v-slot:sysFile="{ obj }">
					<div>
						<span class="toDetail" @click="handleDown(obj)">{{
							obj.row.sysFile.fileName
						}}</span>
					</div>
				</template>
    </sb-el-table>

    <!-- 工单详情 -->
     <el-dialog :title="dialogTitle" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <work-order :key="cKey" :gps="gps" :dialogClose="dialogClose"></work-order>
    </el-dialog>
  </div>
</template>
<script>
import WorkOrder from "@/components/WorkOrder";
import { getWorkQueryPage,exportParameter } from "@/api/home";
import util from "@/assets/js/public";
export default {
  name: "applicationQuery",
  components: { WorkOrder },
  data() {
    return {
      viewD: false,
      dialogTitle:'',
      gps: {
        type: "join",
        location: "",
        pmInsType: "",
      },

      cKey: 0,
      table: {
        modulName: "signQuery-工单查询", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            {class: "c4",label: "单位名称",key: "belongCompanyName",type: "input"},
            {class: "c4",label: "部门名称",key: "belongDepartmentName",type: "input"},
            {class: "c4",label: "姓名",key: "trueName",type: "input"},
            // {class: "c4",label: "申请单位",key: "a",type: "select"},
            // {class: "c4",label: "申请部门",key: "b",type: "select"},
            // {class: "c4",label: "流程状态",key: "currentState",type: "select"},
            {class: "c4",label:'开始时间',key: "startDate",type: "date",subtype: "datetime",datetimerange: "yyyy-MM-dd",},
            {class: "c4",label:'结束时间',key: "endDate",type: "date",subtype: "datetime",datetimerange: "yyyy-MM-dd",}

          ],
        },
        tr: [
          {id: "trueName",label: "签订人员",prop: "trueName",width: 200},
          {id: "pushDate",label: "派发时间",prop: "pushDate",width: 200},
          {id: "signDate",label: "签订时间",prop: "signDate",width: 200},
          {id: "sysFile",label: "廉洁从业承诺书",prop: "sysFile",show: "template",},
         
        ],
        // hasSetup:true,
				// setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "400px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: false, //是否有操作列表
        operation: {
          width: "80",
          fixed: "right",
          data: [
            {id: "handleTodo",name: "查看", fun:"handleTodo"},
          ],
        },
        hasOtherQueryBtn: true, //是否有其他操作
        otherQueryBtn: {
          data: [
            {id: "export",type: "primary",name: "导出",fun: "handleExport"}
          ]
        },
        hasPagination: true,
        listQuery: {size: 10,page: 1},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      getWorkQueryPage(listQuery || this.table.listQuery).then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        }).catch((err) => {
          this.table.loading = false;
        });
    },
    
    // 查看
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "join",
        location: `${process.env.VUE_APP_APPCODE}.`,
        pmInsId: obj.row.PM_INS_ID,
        pmInsType: obj.row.PM_INS_TYPE,
        processInstId: obj.row.PROCESS_INST_ID
      };
      this.cKey++;
      this.viewD = true;
    },
    handleDown(obj){
      this.gps = {
        location: obj.row.location,
        pmInsType: obj.row.pmInsType || 'A',
        pmInsId: obj.row.pmInsId,
        id: obj.row.id,
        types: 'join'
      };

      // 工单标题
      var th = this.util.appNameTH(obj.row.pmInsType);
      this.dialogTitle = (obj.row.title || "") + "";

      this.cKey++;
      this.viewD = true;

    },

    // 导出
    handleExport(){
      exportParameter(this.table.listQuery).then(res => {
        if(res.data){
          this.util.blobDownload(res.data,res.filename);
        }else{
          this.$message({
            message: '导出失败',
            type: 'warning',
            duration: 1500
          });
        }
		  });
    },
   
    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header{text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;}
::v-deep .el-dialog__title{color: black !important;font-size: 15.5px;}
::v-deep .el-dialog__headerbtn .el-dialog__close{
  color: black;
}
</style>