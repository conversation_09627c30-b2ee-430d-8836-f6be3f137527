import request from "@/assets/js/request";
import util from '@/assets/js/public';
import store from "@/store";

export function findPOrgAndCityOrg() {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findPOrgAndCityOrg?appcode=${process.env.VUE_APP_APPCODE}`
  });
}


export function findSonByParentOrgId(orgCode) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findSonByParentOrgId?appcode=${process.env.VUE_APP_APPCODE}&orgCode=` + orgCode
  });
}

export function findOneStep(orgCode) {
    return request({
      url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/findOneStep?appcode=${process.env.VUE_APP_APPCODE}&orgCode=` + orgCode,
      contentType: 'application/json;charset=UTF-8',
	  data: {"appcode":process.env.VUE_APP_APPCODE },
    });
} 

export function findPeopleGroup(params,page) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/group/getAll?appcode=${process.env.VUE_APP_APPCODE}&page=${page}`,
    contentType: 'application/json;charset=UTF-8',
  data: params,
  });
}
// 角色
export function findRole(params,page) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/findRoleNameIsARoleDim?appcode=${process.env.VUE_APP_APPCODE}&page=${page}`,
    contentType: 'application/json;charset=UTF-8',
  data: params,
  });
}

export function interiorDialog(urls,params){
  return request({
    url: `/${process.env.VUE_APP_APPCODE}${urls}?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
    contentType: "application/json;charset=UTF-8",
	data: params
  });
}

export function findDimUserTree(truename) {
  return request({
    url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/findDimUserTree?appcode=${process.env.VUE_APP_APPCODE}`,
    contentType: 'application/json;charset=UTF-8',
    data: {
      "truename":truename
    },
  });
}
