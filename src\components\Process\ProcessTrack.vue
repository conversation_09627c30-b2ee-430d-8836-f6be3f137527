<template>
  <div class="app-container">
    <sb-el-table :table="table" @getList="getList">
      <template v-slot:nextActivityName="{obj}">
        <div>{{obj.row.nextActivityName?(obj.row.nextActivityName+"："+obj.row.nextActivityAssignee):""}}</div>
      </template>
    </sb-el-table>

    <el-divider v-if="copyTrackTable">抄报跟踪</el-divider>
    <sb-el-table :table="table2" v-if="copyTrackTable" @getList="getList2"></sb-el-table>

  </div>
</template>
<script>
import { findFlowTracking ,flowTodoReTracking} from "@/api/process";
export default {
  name: "processTrack",
  props: {
    gps: {
      type: Object,
      required: true
    },

  },
  data() {
    return {
      table: {
        modulName: "processTrack-流程跟踪", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [],
        },
        tr: [
          { id: "name", label: "环节名称", prop: "name", width: 180 },
          { id: "assigneeName", label: "办理人", prop: "assigneeName", width: 90 },
          { id: "createdTime", label: "到达时间", prop: "createdTime", width: 150 },
          { id: "endTime", label: "办理时间", prop: "endTime", width: 150 },
          { id: "approvalComments", label: "办理意见", prop: "approvalComments", width: 180 },
          { id: "nextActivityName", label: "后续处理环节", prop: "nextActivityName", show: "template" }
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [],
        },
        hasPagination: false,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },

      copyTrackTable: false,
      table2: {
        modulName: "processTrack-抄报跟踪", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: false, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [],
        },
        tr: [
          { id: "workItemName", label: "抄报环节名称", prop: "workItemName", width: 300 },
          { id: "sendUserName", label: "发送人", prop: "sendUserName", width: 180 },
          { id: "modifiedTime", label: "抄报时间", prop: "modifiedTime" },
          { id: "recipientName", label: "抄报人", prop: "recipientName", width: 180},
          { id: "content", label: "抄报意见", prop: "content", width: 180 },
        ],
        // hasSetup:true,
        // setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "100",
          fixed: "right",
          data: [],
        },
        hasPagination: false,
        listQuery: { size: 10, page: 1 },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },

    };
  },
  created() {
    this.getList();
    this.getList2();
  },
  mounted() {

  },
  methods: {
    // 查询列表
    getList() {
      this.table.loading = true;
      findFlowTracking(this.gps.processInstId).then((res) => {
        this.table.loading = false;
        this.table.data = res.data;
      }).catch((err) => {
        this.table.loading = false;
      });
    },

    getList2() {
      this.table2.loading = true;
      flowTodoReTracking(this.gps.processInstId).then((res) => {
        this.table2.loading = false;
        this.table2.data = res.data;
        if(this.table2.data.length > 0){
          this.copyTrackTable = true
        }else{
          this.copyTrackTable = false
        }
      }).catch((err) => {
        this.table2.loading = false;
      });
    }


  }
};
</script>
<style scoped>
.app-container {
  padding-bottom: 20px;
}
</style>