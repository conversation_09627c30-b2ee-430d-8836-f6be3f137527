import Vue from "vue";
import Clickoutside from './directive/clickoutside';
Vue.directive('clickoutside',Clickoutside);
import print from './directive/print';
Vue.directive('print',print);
Vue.directive("dialogDrag", {
  bind(el, binding, vnode, oldVnode) {
    const dialogHeaderEl = el.querySelector(".el-dialog__header");
    const dragDom = el.querySelector(".el-dialog");

    dialogHeaderEl.style.cssText += ";cursor:move;";
    dragDom.style.cssText += ";top:0px;";

    //获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素,null);
    const sty = (() => {
      if (window.document.currentStyle) {
        return (dom, attr) => dom.currentStyle[attr];
      } else {
        return (dom, attr) => getComputedStyle(dom, false)[attr];
      }
    })();

    dialogHeaderEl.onmousedown = e => {
      //鼠标按下，计算当前元素中可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft;
      const dixY = e.clientY - dialogHeaderEl.offsetTop;

      const screenWidth = document.body.clientWidth; //body当前宽度
      const screenHeight = document.body.clientHeight; //可见区域高度（应为body高度，可某些环境下无法获取）

      const dragDomWidth = dragDom.offsetWidth; //对话框宽度
      const dragDomHeight = dragDom.offsetTop; //对话框高度

      const minDragDomLeft = dragDom.offsetLeft;
      const maxDragDomLeft = screenWidth - dragDom.offsetLeft - dragDomWidth;

      const minDragDomTop = dragDom.offsetTop;
      const maxDragDomTop = screenHeight - dragDom.offsetTop - dragDomHeight;

      //获取到的值带px 正则匹配替换
      let styL = sty(dragDom, "left");
      let styT = sty(dragDom, "top");

      //注意在ie中第一次获取到的值为组件自带50%移动之后赋值为px
      if (styL.includes("%")) {
        styL = +document.body.clientWidth * (+styL.replace(/\%/g, "") / 100);
        styT = +document.body.clientHeight * (+styT.replace(/\%/g, "") / 100);
      } else {
        styL = +styL.replace(/\px/g, "");
        styT = +styT.replace(/\px/g, "");
      }

      document.onmousemove = function(e) {
        //通过事件委托计算移动的距离
        let left = e.clientX - disX;
        let top = e.clientY - dixY;

        //边界处理
        // if (-left > minDragDomLeft) {
        //   left = -minDragDomLeft;
        // } else if (left > maxDragDomLeft) {
        //   left = maxDragDomLeft;
        // }
        if (-top > minDragDomTop) {
          top = -minDragDomTop;
        } else if (top > maxDragDomTop) {
          top = maxDragDomTop;
        }
        //移动当前元素
        dragDom.style.cssText += `;left:${left + styL}px;top:${top + styT}px;`;
      };
      document.onmouseup = function(e) {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    };
  }
});
function toEmotion(text, isNoGif){
  var list = ['微笑','撇嘴','色','发呆','得意','流泪','害羞','闭嘴','睡','大哭','尴尬','发怒','调皮','呲牙','惊讶','难过','囧','抓狂','吐','偷笑','愉快','白眼','傲慢','困','惊恐','流汗','憨笑','悠闲','奋斗','咒骂','疑问','嘘','晕','衰','骷髅','敲打','再见','擦汗','抠鼻','鼓掌','坏笑','左哼哼','右哼哼','哈欠','鄙视','委屈','快哭了','阴险','亲亲','可怜','菜刀','西瓜','啤酒','咖啡','猪头','玫瑰','凋谢','嘴唇','爱心','心碎','蛋糕','炸弹','便便','月亮','太阳','拥抱','强','弱','握手','胜利','抱拳','勾引','拳头','OK','跳跳','发抖','怄火','转圈','415','40c','412','409','40d','107','403','40e','嘿哈','捂脸','奸笑','机智','皱眉','耶','吃瓜','加油','汗','天啊','Emm','社会社会','旺柴','好的','打脸','加油加油','哇','11b','41d','14c','312','112','红包','發','福'];
  if (!text) {
      return text;
  }
  text = text.replace(/(\[([\u4E00-\u9FA5]|[a-z0-9A-Z]){1,4}\]|(\/\u\e([0-9]|[a-z]){3}))/gi, function(word){
    // console.log(word);
      var newWord = word.replace(/\[|\]/gi,'');
      var index = list.indexOf(newWord);
      var imgHTML = '';
      if(index<0){
          return word;
      }
      if(index>116){
        return word;
      }
      var winurl=window.location.href;
      var url=winurl.substring(0,winurl.match("/#/").index);
      imgHTML = `<i class="static-emotion" style="width:24px;height:24px;display:inline-block;background:url(${url}/emoji/emoji_${index}.png);background-size:24px;"></i>`;
      return imgHTML;
  });
  return text;
}
Vue.directive("emotion",{
  bind:function(el, binding, vnode, oldVnode){
    el.innerHTML=toEmotion(el.innerHTML);
  }
});
