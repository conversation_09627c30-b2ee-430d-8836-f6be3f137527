<template>
  <div class="html" v-html="currentHTML" />
</template>
<script>
import store from "@/store";
import CryptoJS from 'crypto-js';

export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    gps: {},
    appFormValue: {
      type: Object,
      required: true
    },
  },
  data() {
    return {}
  },
  computed:{
    currentHTML() {
      return CryptoJS.enc.Utf8.stringify(CryptoJS.enc.Base64.parse(this.item.control.defaultHtml))
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {}
}
</script>

<style scoped>

</style>
