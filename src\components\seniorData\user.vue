<template>
  <div class="w100 inputBtn">
    <el-input
      ref="elInput"
      :type="item.inputType || 'text'"
      v-bind="$attrs"
      v-on="$listeners"
      :size="item.size || 'small'"
      :placeholder="item.placeholder || item.label || '请输入'"
      :disabled="item.disabled || false"
      :readonly="item.readonly || false"
      :autosize="item.autosize || false"
    >
      <el-button
        v-if="item.appendShow"
        slot="append"
        :size="item.size || 'small'"
        type="primary"
        :disabled="item.disabled || false"
        @click="openDialog"
        >{{ item.btnText }}
        <svg-icon v-if="!item.btnText" iconClass="sousuo"></svg-icon>
      </el-button>
    </el-input>
    <el-dialog
      title="选择人员" 
      v-dialogDrag
      :visible.sync="dialogVisible"
      width="50%"
      append-to-body
      :center="false"
    >
      <el-container class="contwarp">
        <el-main class="warpLeft">
            <div class="card-header">
              <el-input v-model="searchName" placeholder="请输入姓名进行搜索" style="width:200px" clearable/>
              <el-button class="button" type="primary" style="margin-left:10px" @click="searchNames()">搜索</el-button>
            </div>
            <el-tree v-if="!trueBtn" class="tree1" :key="clickKey" style="height: 56vh;overflow: auto;padding-bottom: 32px;" :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :default-expanded-keys="treeExpandData" :load="loadNode" lazy :node-key="item.nodeKey || 'id'"  :check-on-click-node="true">
              <!-- :expand-on-click-node="false" -->
              <template #default="{ node, data }">
                <div @click="onTreeNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
              </template>
            </el-tree>
             <el-tree class="tree2" v-if="trueBtn" :key="(clickKey + 1)" style="height: 56vh;overflow: auto;padding-bottom: 32px;" :props="item.defaultProps || defaultProps" ref="chooseOrgTree" :data="filteredTreeData" default-expand-all :highlight-current="true" :node-key="item.nodeKey || 'id'"  :check-on-click-node="true">
              <template #default="{ node, data }">
                <div @click="onTreeNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
              </template>
            </el-tree>
        </el-main>
        <el-aside width="320px" class="asideR">
          <h5 class="fbold">已选人员</h5>
          <div class="choose-department-checked">
            <div class="choose-department-item" v-for="(citem, index) in multipleSelection" :key="citem[item.nodeKey || 'id']">
              <div class="choose-department-item-text ellipsis-line-1">
                {{ citem[item.defaultProps.label || "name"] }}
              </div>
              <span class="choose-department-item-close" @click="delChoose(citem)">x</span>
            </div>
          </div>
        </el-aside>
      </el-container>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false" size="small">取消</el-button>
        <el-button type="primary" @click="handleConfirm" size="small"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template> 
<script>
import { findOneStep,findDimUserTree } from "@/api/senior";
export default {
  name: "SbChooseOrg",
  props: {
    item: {
      type: Object,
      required: true
    },
    onOk: {
      type: Function
    },
  },
  data() {
    return {
      dialogVisible: false,
      defaultProps: {
        children: "children",
        label: "orgName",
        isLeaf: 'leaf',
      },
      treeData: [],
      multipleSelection: [],
      defaultCheckedKs: [],
      reqs: [],
      treeExpandData: [],
      searchName:"",
      trueBtn:false,
      filteredTreeData:[],
      clickKey: 0,
    };
  },
  methods: {
    searchNames(){
        if(this.searchName.trim()){
          this.trueBtn = true
          this.$nextTick(()=>{
            this.clickKey++;
            findDimUserTree(this.searchName.trim()).then(({ data }) => {
              let newData = this.util.toTreeData(
                  data,
                  'id',
                  'parentId',
                  'id,parentId,name,treeLevel,treeType,orgDisplayName'
              );
              this.filteredTreeData = newData
            })
          })
          
        }else{
          this.trueBtn = false
          this.$nextTick(()=>{
            this.clickKey++;
          })
        }
    },
    loadNode(node, resolve) {
      if(process.env.VUE_APP_Type=='1') {
        if (node.level === 0) {
          findOneStep('').then(({ data }) => {
            if(this.$store.getters.user.belongCompanyTypeDictValue == '02'){
              let treeId=""
              treeId = this.$store.getters.user.belongCompanyCode
              let itemTree = data.find(el=>el.id == treeId)
              this.treeExpandData = [itemTree.id]
              resolve([itemTree])
            }else{
              let id = data[0].id
              this.treeExpandData = [id]
              resolve([data[0]])
            }
            
          })
        } else {
          findOneStep(node.data.id).then(({ data }) => {
            data.forEach((target) => {
              target.treeType == 'user' && Reflect.set(target, 'leaf', true)
            })
            resolve(data)
          })
        }
      } else {
        if (node.level === 0) {
          this.treeExpandData = [process.env.VUE_APP_TOP_ORG]
          resolve([{
            "id": process.env.VUE_APP_TOP_ORG,
            "name": process.env.VUE_APP_TOP_NAME,
          }])
        } else {
          findOneStep(node.data.id).then(({ data }) => {
            data.forEach((target) => {
              target.treeType == 'user' && Reflect.set(target, 'leaf', true)
            })
            resolve(data)
          })
        }
      }
    },
    onTreeNodeClick(node, data) {
      if (data.treeType == 'user') {
        if(this.trueBtn){
          data.orgDisplayName = node.parent.data.orgDisplayName
        }
        if (
            (!this.item.mulitple && this.item.mulitple !== false) ||
            this.item.mulitple === true
        ) {
            //多选
            this.writeNodeToLocalChecked(data);
        }else{
            this.multipleSelection = [data]
        }
      }
    },
    writeNodeToLocalChecked(node) {
      if (!this.arrayIsIncludeTheObject(this.multipleSelection, node)) {
        this.multipleSelection.push({ ...node })
      }
    },
    arrayIsIncludeTheObject(arr, obj, key = 'id') {
      return arr.some((every) => Object.is(every[key], obj[key]))
    },


    openDialog(e) {
      // this.trueBtn = false
      this.multipleSelection = [];
      //console.log(this.$refs.elInput);
      let inputData = this.$refs.elInput._props;
      let formData = this.$refs.elInput.elForm._props.model;
      // console.log(formData);
      let relevancy = this.item.relevancy.split(",");
      let di = formData[relevancy[0].split("-")[0]]
        ? formData[relevancy[0].split("-")[0]].split(",").length
        : 0;
      if (di === 0) {
        this.multipleSelection = [];
        this.defaultCheckedKs = [];
        if (this.$refs.chooseOrgTree)
          this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
      for (let i = 0; i < di; i++) {
        let datai = {};
        for (let j in relevancy) {
          var reF = relevancy[j].split("-");
          if (reF.length === 1) reF.push(reF[0]);
          datai[reF[1]] = formData[reF[0]]
            ? formData[reF[0]].split(",")[i]
            : "";
        }
        var ml = 0;
        for (let k in this.multipleSelection) {
          for (let j in relevancy) {
            var reF = relevancy[j].split("-");
            if (reF.length === 1) reF.push(reF[0]);
            if (datai[reF[1]] === this.multipleSelection[k][reF[1]]) ml++;
          }
        }
        if (ml !== relevancy.length) {
          this.multipleSelection.push(datai);
          this.defaultCheckedKs.push(datai[this.item.nodeKey || "id"]);
        }
      }
      // console.log(JSON.stringify(this.multipleSelection));
      this.dialogVisible = true;
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit("chooseData", this.multipleSelection);
      if (this.onOk && this.item.handleUser) {
        this.onOk(this.item,"handleUser",this.multipleSelection);
      }
    },
    delChoose(row) {
      if (
        (!this.item.mulitple && this.item.mulitple !== false) ||
        this.item.mulitple === true
      ) {
        //多选
        let arry = JSON.parse(JSON.stringify(this.multipleSelection));
        for (let i in arry) {
          if (arry[i][this.item.nodeKey || "id"] === row[this.item.nodeKey || "id"])arry.splice(i, 1);
        }
        this.multipleSelection = arry;
      } else {
        //单选
        this.multipleSelection = [];
        this.$refs.chooseOrgTree.setCheckedKeys([]);
      }
    }
  },
  created() {
    // this.getTreeData();
  }
};
</script>
<style scoped>
.icon {
  margin: 0;
}
.el-dialog__body {
  padding: 0px 20px 30px;
}
.el-main {
  padding: 0px;
  border-left: 0px solid #e0e0e0;
}
.asideR {
  border-left: 1px solid #e0e0e0;
  padding:0px 15px 0px;
}
.chooseD a {
  display: block;
  padding: 5px 0;
}

.choose-department-checked {
  width: 100%;
  box-sizing: border-box;
  padding: 10px;
  height: 500px;
  overflow-y: auto;
  border: 1px solid #e6ebf5;
}

.choose-department-item {
  height: 30px;
  -js-display: flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.choose-department-item-text {
  width: 80%;
  user-select: none;
}

.choose-department-item-close {
  cursor: pointer;
  font-size: 16px;
}
.contwarp{
  height: 58vh;
  overflow: hidden;
}
.warpLeft{
  height: 100%;
  overflow: hidden;
}

.warpLeft ::v-deep .el-input__validateIcon{
    display: none;
}

</style>
