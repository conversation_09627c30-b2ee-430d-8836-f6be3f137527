import request from "@/assets/js/request";

// 获取Lie表相关数据
export function getSkinList(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/loginSkin/findAll?page=${params.page}&${params.size}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 新增
export function createSkinList(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/loginSkin/create`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 编辑
export function updateSkinList(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/loginSkin/update`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 删除
export function deleteById(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/loginSkin/deleteById?id=${id}`,
        contentType: "application/json; charset=utf-8",
        // data: params
    });
}
// 启用
export function setEnabled(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/loginSkin/setEnabled?id=${params.id}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
// 上传
export function oneselfUploadFiles(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/loginSkin/oneselfUploadFiles`,
        contentType: "application/json; charset=utf-8",
        data: params,
        // responseType: 'blob'
    });
}