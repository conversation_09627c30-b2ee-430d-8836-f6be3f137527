<template>
  <div class="app-container">
    <sb-el-table
      :table="table"
      @getList="getList"
      @handleTodo="handleTodo"
      @updateTableData="updateTableData"
      :on-ok="handleDoFun"
    >
      <template v-slot:BUSINESS_TITLE="{obj}">
        <span class="toDetail" @click="handleTodo(obj)">{{obj.row.BUSINESS_TITLE}}</span>
      </template>
      <template v-slot:CREATED_TIME="{obj}">
        <span>{{util.getTimeDate(obj.row.CREATED_TIME,"yyyy-MM-dd HH:mm:ss")}}</span>
      </template>
      <template v-slot:MODIFIED_TIME="{obj}">
        <span>{{util.getTimeDate(obj.row.MODIFIED_TIME,"yyyy-MM-dd HH:mm:ss")}}</span>
      </template>
    </sb-el-table>

    <!-- 工单详情 -->
    <el-dialog :title="dialogTitle" :visible.sync="viewD" v-dialogDrag :close-on-click-modal="false" append-to-body :fullscreen="true">
      <work-order :key="cKey" :gps="gps" :dialogClose="dialogClose"></work-order>
    </el-dialog>
  </div>
</template>
<script>
import WorkOrder from "@/components/WorkOrder";
import { findProcessDraft } from "@/api/process";
import util from "@/assets/js/public";
export default {
  name: "processDraft",
  components: { WorkOrder },
  data() {
    return {
      viewD: false,
      cKey: 0,
      dialogTitle: "",
      gps: {},
      table: {
        modulName: "processDraft-我的草稿", // 列表中文名称
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: "dialog",
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: "90px",
          formItemList: [
            {label: "工单标题",key: "title",type: "input",class: "c4"},
            {label: "申请人",key: "applyUser",type: "input" , class: 'c3'},
          ],
        },
        tr: [
          {id: "RECEIPTCODE",label: "流程编号",prop: "RECEIPTCODE",width: 180},
          {id: "BUSINESS_TITLE",label: "工单标题",prop: "BUSINESS_TITLE", show:"template", template: "BUSINESS_TITLE"},
          {id: "BELONG_DEPARTMENT_NAME",label: "创建部门",prop: "BELONG_DEPARTMENT_NAME",width: 180},
          {id: "CREATOR_NAME",label: "创建人",prop: "CREATOR_NAME",width: 90},
          {id: "CREATED_TIME",label: "创建时间",prop: "CREATED_TIME",width: 160, show: "template" },
          {id: "MODIFIED_TIME",label: "最后一次修改时间",prop: "MODIFIED_TIME",width: 160, show: "template" }
        ],
        // hasSetup:true,
				// setup:[],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: "600px",
          labelWidth: "100px",
          inline: true,
          formItemList: [],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: "80",
          fixed: "right",
          data: [
            {id: "handleTodo",name: "编辑", fun:"handleTodo"},
          ],
        },
        hasPagination: true,
        listQuery: {size: 10,page: 1},
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    // 查询列表
    getList(listQuery) {
      this.table.loading = true;
      findProcessDraft(listQuery || this.table.listQuery).then((res) => {
          this.table.loading = false;
          this.table.data = res.data.content;
          this.table.total = res.data.totalElements;
        }).catch((err) => {
          this.table.loading = false;
        });
    },
   
    // 办理
    handleTodo(obj) {
      // 参数
      this.gps = {
        type: "draft",
        location: process.env.VUE_APP_APPCODE+".start",
        pmInsType: obj.row.PM_INS_TYPE,
        pmInsId: obj.row.PM_INS_ID
      };
      // 工单标题
      var th = this.util.appNameTH(obj.row.PM_INS_TYPE);
      this.dialogTitle = th.type + (obj.row.BUSINESS_TITLE || "") + "-审批";

      this.cKey++;
      this.viewD = true;
    },

    // 关闭弹框
    dialogClose() {
      this.viewD = false;
      this.getList();
    },

    // 刷新数据
    updateTableData(obj) {
      for (let i in obj) {
        this.$set(this.table, i, obj[i]);
      }
    },

    handleDoFun(obj, fun, data) {
      //若一个beforeFun可直接在这个函数里面写
      let n = this[obj[fun]].call(this, obj, data);
      return n;
    }
  }
};
</script>
<style scoped>
/* 修改公共样式弹框样式 */
::v-deep .el-dialog__header{text-align: center !important;
  background: white !important;
  color: black;
  font-size: 14px;
  font-weight: bold;
  border-bottom: 1px solid #f2f2f2 !important;}
::v-deep .el-dialog__title{color: black !important;font-size: 15.5px;}
::v-deep .el-dialog__headerbtn .el-dialog__close{
  color: black;
}
::v-deep .el-form-item__content {
  width: 100% !important;
}

::v-deep .c5 .el-form-item__content {
  width: 60% !important;
}
</style>