<template>
  <div class="app-container" style="display:flex;justify-content: space-between;">
    <!-- 左侧树形面板 -->
    <div class="container-left">
      <el-card shadow="never" style="overflow: auto; width: 100%;height: 100%;">
        <div class="head-container">
          <el-tree class="tree1" :props="defaultProps" ref="chooseOrgTree" :default-expanded-keys="treeExpandData" :load="loadNodeGroup" lazy :node-key="'id'" :highlight-current="true" :check-on-click-node="true">
            <template #default="{ node, data }">
              <div @click="handleNodeClick(node, data)" style="width:100%;height: 100%;line-height: 26px;">{{ data.name }}</div>
            </template>
          </el-tree>
        </div>
      </el-card>
    </div>
    <!-- 右侧列表数据面板 -->
    <div class="container-right">
      <sb-el-table :table="table" @getList="getList" @handleAddData="handleAddData" @handleDelete="handleDelete"
                   @handleUpDataGetRow="handleUpDataGetRow" @handleAddBef="handleAddBef" @handleGroupUser="handleGroupUser">
      </sb-el-table>
    </div>
    <el-dialog title="群组配置" :visible.sync="groupPzAllocationDialog" width="1200px" center :close-on-click-modal="false">
      <div style="display:flex">
        <sb-el-table :table="groupAllocatTable" @getList="findUserByGroupSid" @handleToGroupUserDelete="handleToGroupUserDelete">
        </sb-el-table>
        <User :item="groupAllocatData" @chooseData="createGroupUsers"/>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getAll,
  findGroupsType,
  addgroup,
  findPositionAndUser,
  handleQzDelete,
  createGroupUsers,
  findUserByGroupSid,
  deleteByUserIdFromGroup,
  findAllApps
} from '@/api/system/qzgl.js';
import User from "../component/user.vue";
import ConfigDialog from "../component/configDialog";

export default {
  name: 'dataSet',
  props: ["tableType", "tableTitle"],
  components: {
    ConfigDialog,
    User
  },
  data() {
    return {
      resetPasswordDialog: false,
      fieldTableDialog: false, //字段管理弹框
      fieldTableData: [], //字段列表表数据
      quotaChecked: false,
      quotaIndeterminate: false,
      datasetId: '', //数据集id
      table: {
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        modulName: 'qzgl-群组信息', // 列表中文名称
        stripe: true, // 是否为斑马条样式
        hasSelect: false, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: 'dialog',
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: '80px',
          labelPosition: 'right',
          formItemList: [
            { label: '群组编码', key: 'sid', type: 'input', clearable: true },
            { label: '群组名称', key: 'name', type: 'input', clearable: true }
          ],
        },
        tr: [
          { id: 'sid', label: '群组编码', prop: 'sid', width:'190px'},
          /*{ id: 'fid', label: '父群组ID', prop: 'fid', width:'100x'},*/
          { id: 'name', label: '群组名称', prop: 'name', width:'250px'},
          { id: 'levels', label: '群组级别', prop: 'levels', width:'90x'},
          { id: 'groupType', label: '群组类别', prop: 'groupType', width:'100x'},
          { id: 'id', label: '群组ID', prop: 'id', width:'190px'},
          { id: 'description', label: '群组描述', prop: 'description', width:'369px'},
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        positionType: [], //
        positionLevel: [], //
        nation: [],
        userType: [],
        staffType: [],
        form: {
          width: '600px',
          labelWidth: '120px',
          inline: true,
          labelPosition: 'right',
          formItemList: [
            { class: 'c6', label: '群组编码', key: 'sid', type: 'input', rule: { required: true }, clearable: true},
            { class: 'c6', label: '群组名称', key: 'name', type: 'input', rule: { required: true }, clearable: true},
            { class: 'c6', label: '排序值', key: 'displayOrder', type: 'input',inputType: 'number', rule: { required: true }, clearable: true},
            { class: 'c6', label: '群组级别', key: 'levels', type: 'select', dictType: "groupLevel" , from:true, clearable: true},
            { class: 'c6', label: '群组类别', key: 'groupType', type: 'select', rule: { required: true },dictType: "groupType" , from:true, clearable: true},
            { class: 'c6', label: '父群组id', key: 'fid', type: 'input', clearable: true},
            { class: 'c12', label: '关联组织', key: 'refSid', type: 'input', clearable: true},
            { class: 'c12', label: '应用编码', key: 'appCode', type: 'select', options: [] ,props:{ value: 'appCode', label: 'appName' },rule: { required: true }, from:true,clearable: true },
            { class: 'c12', label: '群组描述', key: 'description', type: 'input',inputType: 'textarea', clearable: true,placeholder: '请输入群组描述' },
          ],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: '250',
          data: [
            { id: 'add', name: '新增',beforeFun: "handleAddBef", fun: 'handleAddData' },
            { id: 'update', name: '编辑', fun: 'handleUpData', beforeFun: "handleUpDataGetRow" },
            { id: 'delete', name: '删除', fun: 'handleDelete'},
            { id: 'qzpz', name: '群组配置', fun: 'handleGroupUser'},
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1, },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
        hasOtherQueryBtn: true,
        otherQueryBtn: {
          data: [
          ],
        },
        hasGroupTabs: true,
        tabsList: [],
        tabsPosition: '全部',
      },
      dbName:"",
      dbArr: [],
      idS: [], //当前选中分组ids
      selectIndex:null,  //搜索结果,被选中的li index
      data: [],
      treeExpandData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      groupPzAllocationDialog: false,
      groupAllocatData: {
        inputType: 'text',
        appendShow: true,
        rows: 12,
        btnText: '搜索',
        mulitple: true,
        defaultProps: {
          children: "children",
          label: "name",
          isLeaf: 'leaf',
        },
      },
      groupAllocatTable: {
        border: true, // 是否带纵向边框
        loading: false, // 加载中动画
        modulName: 'groupPZ-群组配置', // 列表中文名称
        stripe: true, // 是否为斑马条样式
        hasSelect: true, // 是否有复选框
        showIndex: true, // 序号
        data: [], // 数据
        addAndUpdateType: 'dialog',
        total: null,
        hasQueryForm: true, // 是否有查询条件
        queryForm: {
          inline: true,
          labelWidth: '80px',
          labelPosition: 'right',
          formItemList: [
            { label: '用户账号', key: 'username', type: 'input', clearable: true },
          ],
        },
        tr: [
          { id: 'USERNAME', label: '用户账号', prop: 'USERNAME', align: 'center',},
          { id: 'TRUENAME', label: '用户姓名', prop: 'TRUENAME' },
          { id: 'DISPLAY_NAME', label: '所在组织的全路径', prop: 'DISPLAY_NAME', align: 'center' },
        ],
        multipleSelection: [], //多选选中数据存放变量
        dialogVisible: false, //默认对话框关闭
        form: {
          width: '800px',
          labelWidth: '100px',
          inline: true,
          labelPosition: 'right',
          formItemList: [
          ],
        },
        listFormModul: {},
        hasOperation: true, //是否有操作列表
        operation: {
          width: '250',
          data: [
            { id: 'delete', name: '删除', fun: 'handleToGroupUserDelete'},
          ],
        },
        hasPagination: true,
        listQuery: { size: 10, page: 1 ,groupSid: '' },
        hasBatchOperate: false, //有无批量操作
        batchOperate: {},
        hasOtherQueryBtn: false,
        otherQueryBtn: {
          data: [],
        },
        currentGroupSid: '',
      }
    };
  },
  activated() {

  },
  created(){
    this.getList();
    this.findAllApps();
  },
  methods: {
    // 群组添加人员
    createGroupUsers(item) {
      var users = '';
      item.forEach(item=>{
        users = (users + ',' + item.id).slice(1)
      })
      var params = {
        groupSid: this.groupAllocatTable.currentGroupSid,
        usernames: users,
      }
      createGroupUsers(params).then((res) => {
        if(res.status === 200) this.findUserByGroupSid()
      }).catch((err) => {

      });
    },
    // 删除群组下面的用户
    handleToGroupUserDelete(obj) {
      deleteByUserIdFromGroup(obj.ID).then((res) => {
        if(res.status === 200) this.findUserByGroupSid()
      }).catch((err) => {
      });
    },
    //查询所有应用
    findAllApps(){
      findAllApps().then((res) => {
        this.table.form.formItemList[7].options = res.data;
      }).catch((err) => {
      });
    },
    //弹窗内部搜索OA账号
    findUserByGroupSid(obj) {
      this.groupAllocatTable.loading = true;
      findUserByGroupSid(this.groupAllocatTable.listQuery).then((res) => {
        this.groupAllocatTable.loading = false;
        this.groupAllocatTable.data = res.data&&res.data.content?res.data.content:[];
        this.groupAllocatTable.total = res.data.totalElements;
      }).catch((err) => {
        this.groupAllocatTable.loading = false;
      });
    },
    //查询群组下所有人
    getgroupQuery(){
      findPositionAndUser(this.groupAllocatTable.listQuery).then(({ data }) => {
        this.groupAllocatTable.data = data.content;
        this.groupAllocatTable.total = data.totalElements;
      })
    },
    groupAllocationClose() {
      this.groupDialogFlag = false;
      this.groupAllocatTable.listQuery = { size: 10, page: 1 , truename: ''};
      this.groupAllocatTable.data = []
    },
    handleSizeChange(val) {
      this.groupAllocatTable.listQuery.size = val
      this.getgroupQuery()
    },
    handleCurrentChange(val) {
      this.groupAllocatTable.listQuery.page = val
      this.getgroupQuery()
    },
    //左侧树点击
    handleNodeClick(node,data) {
      var params = Object.assign(this.table.listQuery,{groupType: node.data.value,enabled: true})
      this.getList(params)
    },
    loadNodeGroup(node, resolve) {
      if (node.level === 0) {
        findGroupsType().then(({ data }) => {
          resolve(data)
        })
      } else {
        resolve([])
      }
    },
    // 点击组织树节点
    // 查询列表
    getList(params) {
      this.table.loading = true;
      this.table.listQuery.enabled=true
      getAll(params || this.table.listQuery).then((res) => {
        this.table.loading = false;
        this.table.data = res.data&&res.data.content?res.data.content:[];
        this.table.total = res.data.totalElements;
      }).catch((err) => {
        this.table.loading = false;
      });
    },
    handleAddBef(){
      this.table.operation.nowBtn = true;
      this.table.form.formItemList.forEach((item)=>{
        item.disabled = false;
      })
      return true
    },
    // 新增
    handleAddData() {
      addgroup(this.table.listFormModul).then(res => {
        this.table.dialogVisible = false
        if (res.status === 200){
          this.getList();
        }
      })
    },

    // 根据id查询行数据
    handleUpDataGetRow(row) {
      this.table.form.formItemList.forEach((item)=>{
        item.disabled = row.read;
      })
      this.table.listFormModul = row
      this.table.listFormModul.genderDictValue = this.table.listFormModul.genderDictValue+'';
      this.table.listFormModul.employeeTypeDictValue = this.table.listFormModul.employeeTypeDictValue+'';
    },
    // 删除
    handleDelete(row) {
      handleQzDelete(row.id,row.sid).then((res) => {
        this.getList();
      });
    },
    handleGroupUser(obj) {
      this.groupAllocatTable.listQuery.groupSid = obj.row.sid;
      this.findUserByGroupSid(this.groupAllocatTable.listQuery)
      this.groupPzAllocationDialog = true;
      this.groupAllocatTable.listQuery.groupSid = obj.row.sid;
      this.groupAllocatTable.currentGroupSid = obj.row.sid;
    },
  },
};
</script>
<style scoped>
.container-left {
  width: 300px;
  height: 100%;
  overflow: auto;
  background-color: var(--el-bg-color-overlay);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}
.container-right {
  width: calc(100% - 300px);
  height: 100%;
  position: relative;
}
::-webkit-scrollbar {
  display: none;
}
::v-deep .el-card__header{
  padding: 8px 15px;
}
.treeData{
  width:100%;
  display:flex;
  justify-content: space-between;
  font-size: 15px;
  height: 30px;
  line-height: 30px;
  color: #444;
  padding-left: 8px;
}
.treeData .text{
  width: 215px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  /* padding: 5px; */
  cursor: pointer;
}
.treeData .text:hover{
  color: #0F85CF;
}
.treeData .execute{
  width: 70px;
  color: #0F85CF;
  cursor: pointer;
  text-align: right;
}
.treeData .execute:hover{
  text-decoration: underline;
}
::v-deep .el-card__body{
  padding: 0 15px;
  padding-bottom: 15px;
}
.head-container{
  height: calc(100vh - 180px);
  overflow-y: auto;
  padding-top: 20px;
}
::v-deep .el-table__body-wrapper{
  max-height: calc(100vh - 320px);
  overflow-y: auto;
}
.acceptanceBox{
  position: relative;
  /* height: 250px; */
}
.footer{
  width: 100%;
  /* position: absolute; */
  left: 0;
  bottom: 0;
  text-align: center;
  margin-top: 20px;
  margin-bottom: 20px;
}
.selectedColor {
  background-color: #eeeeee;
}
.rateRuleContent {
  margin-right: 22px;
}
</style>
