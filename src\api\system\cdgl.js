import request from "@/assets/js/request";

// 获取Lie表相关数据
export function getMenuList(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/perimission/findPerCustom?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: {
            appCode: `${process.env.VUE_APP_APPCODE}`,
            menuLevel: 1
        }
    });
}
// 获取Lie表相关数据
export function findRootAndNextRoot(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findRootAndNextRoot?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        // data: params
    });
}
// 获取Lie表相关数据
export function findOrgCodeOrgNameDim(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/findSonByParentOrgId?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function addMenu(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/perimission/createCustom?appId=${params.appId}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function updateMenuCustom(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/perimission/update?appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
export function deleteMenuCustom(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/perimission/deleteById?id=${id}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 启用启用
export function updateSysPermission(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/perimission/updateSysPermission?id=${params.id}&status=${params.status}`,
        contentType: "application/json; charset=utf-8",
    });
}
export function findByIdMenu(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/perimission/findById?id=${id}&appcode=${process.env.VUE_APP_APPCODE}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 更新菜单组织
export function updateOrgByPermissionId(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/permission/updateListByPermissionId?permissionId=${params.permissionId}&appId=${params.appId}`,
        contentType: "application/json; charset=utf-8",
        data: params.updlist
    });
}
// 更新菜单人员
export function updateUserByPermissionId(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/user/permission/updateListByPermissionId?permissionId=${params.permissionId}&appId=${params.appId}`,
        contentType: "application/json; charset=utf-8",
        data: params.updlist
    });
}
// 更新菜单角色
export function updateRoleByPermissionId(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/permission/updateListByPermissionId?permissionId=${params.permissionId}&appId=${params.appId}`,
        contentType: "application/json; charset=utf-8",
        data: params.updlist
    });
}
// 查询菜单组织
export function queryOrgByPermissionId(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/org/queryOrgByPermissionId?permissionId=${id}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 查询菜单人员
export function queryUserByPermissionId(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/userinfo/queryUserByPermissionId?permissionId=${id}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 查询菜单角色
export function queryRoleByPermissionId(id) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/queryRoleByPermissionId?permissionId=${id}`,
        contentType: "application/json; charset=utf-8",
    });
}
// 查询菜单角色列表
export function findRoleNameIsARoleDim(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/uums/sys/role/findRoleNameIsARoleDim?page=${params.page}&size=${params.size}`,
        contentType: "application/json; charset=utf-8",
        data: params
    });
}
