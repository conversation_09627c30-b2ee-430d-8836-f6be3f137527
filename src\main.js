
import Vue from "vue";
Vue.config.devtools = true;

import "babel-polyfill";
import "core-js/stable";
// import "regenerator-runtime/runtime";

import ElementUI, { Table } from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import locale from "element-ui/lib/locale/lang/zh-CN";
import draggable from "vuedraggable";

import App from "./App.vue";
import router from "./router";
import "./components/directives";
import store from "./store";
import "@/permission";
import "@/assets/css/public.css";
import Validate from "@/assets/js/rules";
import util from "@/assets/js/public";
import VueWechatTitle from "vue-wechat-title";
// 打印
import Print from 'vue-print-nb'

import SbEcharts from "@/components/SbEcharts"
Vue.component("sb-echarts", SbEcharts);

import SbQuotePage from "@/components/SbQuotePage"
Vue.component("sb-quotepage", SbQuotePage);

import SbMap from "@/components/SbMap/idnex.vue"
Vue.component("sb-map", SbMap);

import SbShowImg from "@/components/SbShowImg/index.vue";
Vue.component("sb-show-img", SbShowImg);

import SbKindeditor from "@/components/SbKindeditor";
Vue.component("sb-kindeditor", SbKindeditor);

import SbTinymce from "@/components/SbTinymce"
Vue.component("sb-tinymce", SbTinymce);

import SbTimestatus from "@/components/SbTimestatus"
Vue.component("sb-timestatus", SbTimestatus);

import SbScore from "@/components/SbScore"
Vue.component("sb-score", SbScore);

import SvgIcon from "@/components/SvgIcon";
Vue.component("svg-icon", SvgIcon);

import SbUpload from "@/components/SbUpload";
Vue.component("sb-upload", SbUpload);

import SbQrcode from "@/components/SbQrcode";
Vue.component("sb-qrcode", SbQrcode);

import SbElUpload from "@/components/SbElUpload";
Vue.component("sb-el-upload", SbElUpload);

import SbImg from "@/components/SbImg";
Vue.component("sb-img", SbImg);

import SbElInputUpload from "@/components/SbElInputUpload";
Vue.component("sb-el-input-upload", SbElInputUpload);

import SbProvince from "@/components/SbProvince";
Vue.component("sb-province", SbProvince);

import SbChooseUsername from "@/components/SbElInputChoose/SbChooseUsername";
Vue.component("sb-choose-username", SbChooseUsername);
import SbChooseTeacher from "@/components/SbElInputChoose/SbChooseTeacher";
Vue.component("sb-choose-teacher", SbChooseTeacher);

import SbChooseOrg from "@/components/SbElInputChoose/SbChooseOrg";
Vue.component("sb-choose-org", SbChooseOrg);

import SbChooseData from "@/components/SbElInputChoose/SbChooseData";
Vue.component("sb-choose-data", SbChooseData);

import SbCard from "@/components/SbCard";
Vue.component("sb-card", SbCard);

import SbCollapse from "@/components/SbCollapse";
Vue.component("sb-collapse", SbCollapse);

import SbTabs from "@/components/SbTabs";
Vue.component("sb-tabs", SbTabs);

import SbTreeSelect from "@/components/SbTreeSelect";
Vue.component("sb-treeSelect", SbTreeSelect);

import SbDownTable from "@/components/SbDownTable";
Vue.component("sb-downTable", SbDownTable);

import SbGroup from "@/components/SbGroup";
Vue.component("sb-group", SbGroup);

import org from "@/components/seniorData/org";
Vue.component("org", org);
import company from "@/components/seniorData/company";
Vue.component("company", company);
import user from "@/components/seniorData/user";
Vue.component("user", user);
import peopleGroup from "@/components/seniorData/peopleGroup";
Vue.component("peopleGroup", peopleGroup);
// 角色组件
import role from "@/components/seniorData/role";
Vue.component("role", role);
import longdialogs from "@/components/seniorData/longdialogs";
Vue.component("longdialogs", longdialogs);
import interiorDialog from "@/components/seniorData/interiorDialog";
Vue.component("interiorDialog", interiorDialog);
import echoTable from "@/components/seniorData/echoTable";
Vue.component("echoTable", echoTable);
import schedule from "@/components/schedule/index.vue";
Vue.component("schedule", schedule);
import sublist from "@/components/sublist/index.vue";
Vue.component("sublist", sublist);
import SbHTML from "@/components/seniorData/SbHTML.vue";
Vue.component("sb-html", SbHTML);
import SbAlert from "@/components/seniorData/SbAlert.vue";
Vue.component("sb-alert", SbAlert);

//数据回显表格
import SbEchoTable from "@/components/SbEchoTable/echotable.vue";
Vue.component("sb-echoTable", SbEchoTable);
import SbEchoBtn from "@/components/SbEchoTable/echoBtn.vue";
Vue.component("sb-echoBtn", SbEchoBtn);

import directive from './directive' // directive
Vue.use(directive)

Vue.use(ElementUI, { locale });
import SbElForm from "@/components/SbElForm";
Vue.component("sb-el-form", SbElForm);
import SbElTable from "@/components/SbElTable";
import "./registerServiceWorker";
Vue.component("sb-el-table", SbElTable);
Vue.component("draggable", draggable);
Vue.use(Validate);
Vue.use(VueWechatTitle);
Vue.use(Print);
Vue.prototype.util = util;


Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount("#app");

//解决在谷歌浏览器中变量值显示省略号的问题
// Vue.prototype.print = (obj, type) => {
//   type = type || "log";
//   const log = JSON.parse(JSON.stringify(obj));
//   console[type](log);
// };

// 打印变量使用
// this.print(obj)
//或者
// this.print(obj,"error")
