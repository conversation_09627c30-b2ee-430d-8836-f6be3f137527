import request from "@/assets/js/request";
import store from "../store";
import util from "@/assets/js/public";

// 获取决策项
export function getDecision(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findDecisions?currentUserCode=${store.state.user.user.username}`,
        contentType: "application/json; charset=utf-8",
        data: params?params:{}
    });
}
// 获取版本号
export function getLastVersion(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getLastVersionByProDefId?key=${params.key}&tenantId=${params.tenantId}`,
        contentType: "application/json; charset=utf-8"
    });
}
// 获取人员组织树
export function getOrgAndUser(params) {
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/getOrgAndUser`,
        contentType: "application/json; charset=utf-8",
        data: params?params:{}
    });
}

// 我的待办
export function findProcessTask(params){
    let data = params?params:{};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsWorkitemModel/myTaskTodo?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}

// 我的草稿
export function findProcessDraft(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/myDraftToDo?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: params
    });
}

// 我的已办
export function findProcessJoin(params){
    let data = params?params:{};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/UsWorkitemModel/queryMyJoin?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}

// 待阅列表
export function findProcessRead(params){
    let data = params?params:{};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findMyPending?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}

// 已阅列表
export function findProcessDoRead(params){
    let data = params?params:{};
    data.databaseType = "oracle";
    data.participant = "OA";
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/findMyRead?source=PC&page=${params.page}&size=${params.size}&loginuser=${store.state.user.user.username}`,
        contentType: "application/json;charset=UTF-8",
		data: data
    });
}

// 流程跟踪
export function findFlowTracking(processInstanceId){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/flowTracking?processInstanceId=${processInstanceId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 流程图
export function getDiagram(processInstanceId){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getDiagram?processInstanceId=${processInstanceId}`,
        method:'get',
        contentType: "application/json;charset=UTF-8",
        responseType: "blob"
    });
}

// 查看意见
export function getWfOptMags(processInstanceId){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/getWfOptMags?processInstanceId=${processInstanceId}`,
        contentType: "application/json;charset=UTF-8"
    });
}

// 废除草稿
export function deleteDraft(params){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/common/deleteDraft`,
        contentType: "application/json;charset=UTF-8",
        data: params
    });
}


// 查询待阅的流程跟踪和意见
export function flowTodoReTracking(processInstanceId){
    return request({
        url: `/${process.env.VUE_APP_APPCODE}/action/process/flowTodoReTracking?processInstanceId=${processInstanceId}`,
        contentType: "application/json;charset=UTF-8"
    });
}