<template>
  <el-alert
      :title="item.control.alertTitle"
      :type="item.control.alertType"
      :description="item.control.alertDescription"
      :show-icon="item.control.alertShowIcon"
      :closable="item.control.alertCloseAble"
  />
</template>
<script>
export default {
  props: {
    item: {
      type: Object,
      required: true
    },
    gps: {},
    appFormValue: {
      type: Object,
      required: true
    },
  },
  data() {
    return {}
  },
  created() {
  },
  mounted() {
  },
  methods: {}
}
</script>

<style scoped>

</style>
