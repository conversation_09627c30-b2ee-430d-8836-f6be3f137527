<template>
  <!-- <div class="app-wrapper" :class="classObj"> -->
  <div class="app-wrapper">
    <!--<span @click="getuser()" style='position:fixed;z-index:1000;'>asdfasdf</span>-->
    <sidebar class="sidebar-container" :class="showSidebar?'open':''"></sidebar>
    <div class="main-container" :class="showSidebar?'':'fullwidth'" style="width:100%;">
      <navbar :showSidebar="showSidebar"></navbar>
      <tab-nav @handleSidebar="handleSidebar"></tab-nav>
      <app-main style="margin-top: 40px"></app-main>
    </div>
  </div>
</template>
<script>
import { Navbar, Sidebar, AppMain, TabNav } from "./components";
import ResizeMixin from "./mixin/ResizeHandler";
export default {
  name: "layout",
  components: {
    Navbar,
    Sidebar,
    AppMain,
    TabNav,
  },
  mixins: [ResizeMixin],
  computed: {
    sidebar() {
      return this.$store.state.app.sidebar;
    },
    device() {
      return this.$store.state.app.device;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  data() {
    return {
      showSidebar: true
    };
  },
  methods: {
    getuser() { },
    handleSidebar() {
      console.log('666')
      this.showSidebar = !this.showSidebar
    }
  },
};
</script>
<style scoped>
.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}
.app-wrapper:after {
  content: "";
  display: table;
  clear: both;
}
.fullwidth {
  padding-left: 0;
}
.sidebar-container {
  width: 0 !important;
}
.sidebar-container.open {
  width: 170px !important;
}
</style>
